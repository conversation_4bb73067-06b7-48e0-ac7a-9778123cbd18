<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="warn">
    <Properties>
        <Property name="PROJECT" value="huida-settlement-dev" />
        <Property name="WXWORK_HOOK_KEY" value="f0c25b17-5d15-40ec-a68f-8d9fd96f0777" />
    </Properties>
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} [%X{requestId:-}] [%X{apiType:-}] [%X{attempt:-}] - %msg%n"/>
        </Console>
        <Loghub name="Loghub"
                project="huida-platform-dev"
                logStore="huida-platform-dev"
                endpoint="cn-beijing.log.aliyuncs.com"
                accessKeyId="LTAI4FfSavyK1fQuycTL8BpD"
                accessKeySecret="******************************"
                topic="product"
                totalSizeInBytes="104857600"
                maxBlockMs="60000"
                ioThreadCount="8"
                batchSizeThresholdInBytes="524288"
                batchCountThreshold="4096"
                lingerMs="2000"
                retries="10"
                baseRetryBackoffMs="100"
                maxRetryBackoffMs="100"
                timeFormat="yyyy-MM-dd'T'HH:mmZ"
                timeZone="Asia/Shanghai"
                ignoreExceptions="true">
            <PatternLayout pattern="%d %-5level [%thread] %logger{0}: %msg"/>
        </Loghub>
        <Dingtalk name="Dingtalk" project="${PROJECT}" sendType="WXWork" mobile="${AT_MOBILE}" url="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${WXWORK_HOOK_KEY}"
                  connectTimeoutMillis="30000" readTimeoutMillis="30000">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            <JsonLayout/>
        </Dingtalk>
        <Alert name="Alert">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            <JsonLayout/>
        </Alert>
    </Appenders>
    <Loggers>
        <Root level="info">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="Dingtalk"/>
            <AppenderRef ref="Alert"/>
            -->
        </Root>
    </Loggers>
</Configuration>