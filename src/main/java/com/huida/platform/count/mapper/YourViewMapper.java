package com.huida.platform.count.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Stream;

@Mapper
public interface YourViewMapper {
    
    /**
     * 流式查询指定日期的数据
     */
    Stream<YourEntity> streamQueryByDate(@Param("processDate") LocalDate processDate);

    /**
     * 批量插入或更新数据
     */
    void batchInsertOrUpdate(List<YourEntity> batchData);
}