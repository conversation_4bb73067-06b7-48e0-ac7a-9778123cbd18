package com.huida.platform.settlement.controller;

import com.huida.platform.common.response.BaseResponse;
import com.huida.platform.settlement.consumer.ProgressUpdateListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

/**
 * 导出进度查询控制器
 * 提供导出任务进度查询功能
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/export/progress")
public class ExportProgressController {
    
    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String PROGRESS_KEY_PREFIX = "export:progress:";
    
    /**
     * 查询导出进度
     * 
     * @param unionId 任务唯一标识
     * @return 进度信息
     */
    @GetMapping("/{unionId}")
    public BaseResponse<ProgressUpdateListener.ProgressInfo> getProgress(@PathVariable String unionId) {
        try {
            if (redisTemplate == null) {
                return BaseResponse.fail("-1", "Redis未配置，无法查询进度");
            }
            
            String key = PROGRESS_KEY_PREFIX + unionId;
            Object progressObj = redisTemplate.opsForValue().get(key);
            
            if (progressObj == null) {
                return BaseResponse.fail("-1", "未找到对应的导出任务进度信息");
            }
            
            if (progressObj instanceof ProgressUpdateListener.ProgressInfo) {
                ProgressUpdateListener.ProgressInfo progressInfo = (ProgressUpdateListener.ProgressInfo) progressObj;
                return BaseResponse.success(progressInfo);
            } else {
                log.warn("进度信息类型异常 - unionId: {}, type: {}", unionId, progressObj.getClass().getName());
                return BaseResponse.fail("-1", "进度信息格式异常");
            }
            
        } catch (Exception e) {
            log.error("查询导出进度失败 - unionId: {}", unionId, e);
            return BaseResponse.fail("-1", "查询进度失败：" + e.getMessage());
        }
    }
    
    /**
     * 清理已完成的进度信息
     * 
     * @param unionId 任务唯一标识
     * @return 操作结果
     */
    @DeleteMapping("/{unionId}")
    public BaseResponse<Void> clearProgress(@PathVariable String unionId) {
        try {
            if (redisTemplate == null) {
                return BaseResponse.fail("-1", "Redis未配置");
            }
            
            String key = PROGRESS_KEY_PREFIX + unionId;
            Boolean deleted = redisTemplate.delete(key);
            
            if (Boolean.TRUE.equals(deleted)) {
                return BaseResponse.success();
            } else {
                return BaseResponse.fail("-1", "进度信息不存在或已被清理");
            }
            
        } catch (Exception e) {
            log.error("清理导出进度失败 - unionId: {}", unionId, e);
            return BaseResponse.fail("-1", "清理进度失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取进度信息的简化版本（只返回关键字段）
     * 
     * @param unionId 任务唯一标识
     * @return 简化的进度信息
     */
    @GetMapping("/{unionId}/simple")
    public BaseResponse<SimpleProgressInfo> getSimpleProgress(@PathVariable String unionId) {
        try {
            BaseResponse<ProgressUpdateListener.ProgressInfo> fullResponse = getProgress(unionId);
            
            if (!fullResponse.isSuccess()) {
                return BaseResponse.fail(fullResponse.getCode(), fullResponse.getMessage());
            }
            
            ProgressUpdateListener.ProgressInfo fullInfo = fullResponse.getData();
            SimpleProgressInfo simpleInfo = new SimpleProgressInfo(
                    fullInfo.getProgress(),
                    fullInfo.getProcessedCount(),
                    fullInfo.getTotalCount(),
                    fullInfo.getMessage()
            );
            
            return BaseResponse.success(simpleInfo);
            
        } catch (Exception e) {
            log.error("查询简化导出进度失败 - unionId: {}", unionId, e);
            return BaseResponse.fail("-1", "查询进度失败：" + e.getMessage());
        }
    }
    
    /**
     * 简化的进度信息
     */
    public static class SimpleProgressInfo {
        private int progress;
        private int processedCount;
        private int totalCount;
        private String message;
        
        public SimpleProgressInfo() {}
        
        public SimpleProgressInfo(int progress, int processedCount, int totalCount, String message) {
            this.progress = progress;
            this.processedCount = processedCount;
            this.totalCount = totalCount;
            this.message = message;
        }
        
        // Getters and Setters
        public int getProgress() { return progress; }
        public void setProgress(int progress) { this.progress = progress; }
        
        public int getProcessedCount() { return processedCount; }
        public void setProcessedCount(int processedCount) { this.processedCount = processedCount; }
        
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}
