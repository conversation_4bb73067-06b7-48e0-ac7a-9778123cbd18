package com.huida.platform.settlement.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSSClient;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huida.platform.common.msg.BaseResponse;
import com.huida.platform.common.util.OssFileUtil;
import com.huida.platform.settlement.alert.api.AlertService;
import com.huida.platform.settlement.constant.FileUploadAddrConstant;
import com.huida.platform.settlement.bo.ProductEnumBO;
import com.huida.platform.settlement.dto.ryl.protocol.BatchDto;
import com.huida.platform.settlement.entity.RylCountSettlementProductEnumEntity;
import com.huida.platform.settlement.entity.countpay.RylCountSettlementBillPayEntity;
import com.huida.platform.settlement.entity.countpay.RylCountSettlementChannelFinanceMappingCfgEntity;
import com.huida.platform.settlement.event.ProductEnumMessageEvent;
import com.huida.platform.settlement.mapper.RylCountSettlementProductEnumMapper;
import com.huida.platform.settlement.mapper.reconciliationpay.RylCountSettlementBillPayMapper;
import com.huida.platform.settlement.mapper.reconciliationpay.RylCountSettlementChannelFinanceMappingCfgMapper;
import com.huida.platform.settlement.service.RylLarateService;
import com.huida.platform.settlement.service.approve.WeChatApproveService;
import com.huida.platform.settlement.service.reconciliationpay.SettBillJobHandlerService;
import com.huida.platform.settlement.service.settlement.SettRateJobHandlerService;
import com.huida.platform.settlement.service.settlement.SettRuleJobHandlerService;
import com.huida.platform.settlement.util.DownLoadUrlStream;
import com.huida.platform.settlement.util.FileUpoadUtil;
import com.huida.platform.settlement.vo.reconciliationpay.MakeCountOrderDataVo;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;


@RestController
@Slf4j
@RequestMapping("/t")
@RefreshScope
public class TestController {
    @Autowired
    private RylLarateService rylLarateService;
    @Autowired
    private SettBillJobHandlerService settBillJobHandlerService;
    @Autowired
    private SettRateJobHandlerService settRateJobHandlerService;

    @GetMapping("/test")
    public BaseResponse test() {
        return BaseResponse.success();
    }

    @Value("${oss.endpoint}")
    private String endpoint;

    @Value("${oss.bucketName.private}")
    private String bucketNamePrivate;

    @Value("${oss.accessKeyId}")
    private String accessKeyId;

    @Value("${oss.accessKeySecret}")
    private String accessKeySecret;

    @Autowired
    FileUpoadUtil fileUpoadUtil;

    @Autowired
    private WeChatApproveService weChatApproveService;
    @Autowired
    private RylCountSettlementProductEnumMapper rylCountSettlementProductEnumMapper;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private RylCountSettlementBillPayMapper rylCountSettlementBillPayMapper;
    @Autowired
    private RylCountSettlementChannelFinanceMappingCfgMapper rylCountSettlementChannelFinanceMappingCfgMapper;
    @Autowired
    private SettRuleJobHandlerService settRuleJobHandlerService;

    @GetMapping(value = "r")
    public BaseResponse t() {
        try {
            rylLarateService.cacheRylLarate();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResponse.success();
    }
    @RequestMapping(value = "a")
    public BaseResponse export(String protocolCode,String productCode){
        try {
           return rylLarateService.export(protocolCode,productCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResponse.success();
    }
    @GetMapping("/testOss")
    public BaseResponse testOss() {

//        String endpoint = "oss-cn-beijing.aliyuncs.com";
//        String accessKeyId = "LTAI4FtkH7Y7f5iHKk24bKFo";
//        String accessKeySecret = "******************************";
//        String bucketName = "int-private";
//        String firstKey = "test/fail.txt";
        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
        try {
            InputStream is = new ByteArrayInputStream("Hello OSS".getBytes());
            ossClient.putObject(bucketNamePrivate, "my-first-key", is);
        } catch (Exception e) {

        } finally {
            ossClient.shutdown();
        }

        return BaseResponse.success();
    }

    @Autowired
    private OssFileUtil ossFileUtil;
    @RequestMapping(value = "testUpload", method = RequestMethod.POST)
    public BaseResponse testUpload(@RequestParam("file") MultipartFile file) {
        System.out.println(file.getOriginalFilename());
        InputStream is = null;
        try {
            is =  file.getInputStream();
            ossFileUtil.putObjectPublic(file.getOriginalFilename(), is);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return BaseResponse.success();
    }

    @GetMapping(value="testDownload")
    public BaseResponse testDownload() {
        try {
            InputStream is = ossFileUtil.getObject("wenjianjia/微信图片_20190927161355.jpg");

        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResponse.success();
    }

    @GetMapping(value = "testUtil")
    public BaseResponse testUtil() {
        try {
            ossFileUtil.putObject("test", new ByteArrayInputStream("Hello OSS".getBytes()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResponse.success();
    }

    @PostMapping(value = "syncSettBillToFk")
    public BaseResponse syncSettBillToFk(@RequestBody String s, HttpServletResponse response) {
        try {
            settBillJobHandlerService.syncSettBillToFk(s);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResponse.success();
    }

    @PostMapping(value = "syncBillPayResult")
    public BaseResponse syncBillPayResult(@RequestBody String s) {
        try {
            settBillJobHandlerService.syncBillPayResult(s);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResponse.success();
    }

    @PostMapping(value = "syncChannelRate")
    public BaseResponse syncChannelRate(@RequestBody String s) {
        try {
            settRateJobHandlerService.syncChannelGradeRate(s);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResponse.success();
    }
    @PostMapping(value = "wenjian")
    public BaseResponse wenjian(@RequestParam("aaaa") MultipartFile file) {
        try {
            InputStream inputStream = file.getInputStream();

            fileUpoadUtil.uploadN(inputStream,"ceshi1122.xlsx","settlement/protocol/");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResponse.success();
    }

    @PostMapping(value = "payStatus")
    public BaseResponse payStatus(String param) {
        try {
            log.info("费控结算单付费结果更新开始，请求参数：{}" ,param);
            long time = System.currentTimeMillis();
            settBillJobHandlerService.syncBillPayResult(param);
            log.info("费控结算单付费结果更新结束,耗时:{}",System.currentTimeMillis() - time);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResponse.success();
    }

    @PostMapping(value = "weChatApprove/{creatorUserId}")
    public BaseResponse weChatApprove(@RequestBody RylCountSettlementBillPayEntity rylCountSettlementBillPayEntity, @PathVariable("creatorUserId")String creatorUserId) {
        try {
            weChatApproveService.approvalSave(rylCountSettlementBillPayEntity,creatorUserId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResponse.success();
    }

    @PostMapping(value = "weChatUploadFile")
    public BaseResponse weChatUploadFile(@RequestParam("aaaa") MultipartFile file) {
        try {
            String s = fileUpoadUtil.weChatUploadFile("推送费控数据关系表.xlsx","推送费控数据关系表.xlsx",null);
            log.info(s);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResponse.success();
    }

    @PostMapping(value = "syncZjSettBillPaymentStatusHandler")
    public BaseResponse syncZjSettBillPaymentStatusHandler(@RequestBody String s, HttpServletResponse response) {
        try {
            settBillJobHandlerService.syncZjSettBillPaymentStatus(s);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResponse.success();
    }

    @PostMapping(value = "batch/productEnum")
    public BaseResponse batchProductEnum(@RequestBody String s, HttpServletResponse response) {
        try {
            BatchDto batchDto = JSONObject.parseObject(s,BatchDto.class);
            // 批量处理未同步费率表的枚举
            List<ProductEnumBO> list = new ArrayList<>();
            rylCountSettlementProductEnumMapper.selectList(new QueryWrapper<RylCountSettlementProductEnumEntity>()
                    .eq("is_del",0)
                    .last(batchDto.getSql()))
                    .forEach(productEntity->{
                        ProductEnumBO productEnumBO = new ProductEnumBO();
                        BeanUtils.copyProperties(productEntity,productEnumBO);
                        productEnumBO.setIsProduct(Boolean.TRUE);
                        list.add(productEnumBO);
                    });
            ProductEnumMessageEvent messageEvent = new ProductEnumMessageEvent(list);
            applicationEventPublisher.publishEvent(messageEvent);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResponse.success();
    }

//    @PostMapping(value = "syncBillPayResult")
//    public BaseResponse syncBillPayResult(@RequestBody String s, HttpServletResponse response) {
//        try {
//            settBillJobHandlerService.syncBillPayResult(s);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return BaseResponse.success();
//    }

    @PostMapping(value = "downloadFolder")
    public BaseResponse downloadFolder(HttpServletResponse response) {
        try {
            List<DownLoadUrlStream> downLoadUrlStreams = new ArrayList<>();
            List<String> keys = new ArrayList<>();
            keys.add("1717058962226.pdf");
            keys.add("1717056506474.png");
            keys.add("1717057582229.png");
            for (String key : keys) {
                DownLoadUrlStream downLoadUrlStream = new DownLoadUrlStream();
                String targetfilePath = FileUploadAddrConstant.RYL_FILE_ADDR.concat(key);
                downLoadUrlStream.setFileKey(targetfilePath);
                downLoadUrlStream.setCatalogue("/结算单和发票/"+key);
                downLoadUrlStreams.add(downLoadUrlStream);
            }
            fileUpoadUtil.downloadFolder(downLoadUrlStreams,"批量打包下载测试.zip",response);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResponse.success();
    }

    @Autowired
    private AlertService alertService;
    @PostMapping(value = "/alert")
    public BaseResponse alert(String alertCode) {
        try {
            alertCode = "N0028";
//            alertService.rateAlert("RPM00040");
            alertService.checkVitalRate("RPM00047");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResponse.success();
    }

    @PostMapping(value = "/initializationRate")
    public BaseResponse initializationRate(String code) {
        try {
            if(!StringUtil.isBlank(code)){
                alertService.checkVitalRate(code);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResponse.success();
    }

    @PostMapping("/updateBankByCountNo")
    public BaseResponse updateBankByCountNo(@RequestBody String s) throws Exception {
        try {
            MakeCountOrderDataVo countVitalAutoDTO = JSON.parseObject(s, MakeCountOrderDataVo.class);
            if(StringUtils.isBlank(countVitalAutoDTO.getStartTime()) || StringUtils.isBlank(countVitalAutoDTO.getEndTime())){
                return BaseResponse.success();
            }
            QueryWrapper<RylCountSettlementBillPayEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.ge("count_start_time",countVitalAutoDTO.getStartTime());
            queryWrapper.le("count_end_time",countVitalAutoDTO.getEndTime());
            queryWrapper.ne("merchant_type","1");
            if(!StringUtils.isBlank(countVitalAutoDTO.getChannelCode())){
                queryWrapper.eq("channel_code",countVitalAutoDTO.getChannelCode());
            }
            if(!StringUtils.isBlank(countVitalAutoDTO.getCountNo())){
                queryWrapper.eq("count_no",countVitalAutoDTO.getCountNo());
            }
            List<RylCountSettlementBillPayEntity> billPayEntityList = rylCountSettlementBillPayMapper.selectList(queryWrapper);
            if(!billPayEntityList.isEmpty()){
                billPayEntityList.stream().forEach(billPay->{
                    QueryWrapper<RylCountSettlementChannelFinanceMappingCfgEntity> channelFinanceMappingCfgQueryWrapper = new QueryWrapper();
                    channelFinanceMappingCfgQueryWrapper.eq("channel_code",billPay.getChannelCode());
                    if (org.apache.commons.lang.StringUtils.isNotEmpty(billPay.getManageCom())){
                        String[] split = StringUtil.split(billPay.getManageCom(), "|");
                        List<String> list = Arrays.asList(split).stream()
                                .map(x -> x.substring(0, Math.min(x.length(), 4)))
                                .distinct()
                                .collect(Collectors.toList());
                        channelFinanceMappingCfgQueryWrapper.and(wrapper1 -> {
                            for (String item : list) {
                                wrapper1.or().like("manage_com", item);
                            }
                        });
                    }
                    List<RylCountSettlementChannelFinanceMappingCfgEntity> rylCountSettlementChannelFinanceMappingCfgEntityList = rylCountSettlementChannelFinanceMappingCfgMapper.selectList(channelFinanceMappingCfgQueryWrapper);
                    if(!rylCountSettlementChannelFinanceMappingCfgEntityList.isEmpty()){
                        rylCountSettlementChannelFinanceMappingCfgEntityList.stream().forEach(x->{
                            if(!StringUtil.isBlank(x.getBankCode()) && !StringUtil.isBlank(x.getBankName())){
                                rylCountSettlementBillPayMapper.updateBankDetailByCountNo(billPay.getCountNo(),x.getBankName(),x.getBankCode());
                            }
                        });
                    }
                });
            }
        } catch (Exception e) {
            log.info("维护结算单银行信息处理异常"+e.getMessage());
        }
        return BaseResponse.success();
    }

    @PostMapping(value = "/testN0027")
    public BaseResponse testN0027() {
        try {
            settRuleJobHandlerService.settExamineQW("N0027");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResponse.success();
    }
}
