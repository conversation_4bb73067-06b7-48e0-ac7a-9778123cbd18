package com.huida.platform.settlement.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huida.platform.common.msg.BaseResponse;

import javax.validation.Valid;
import com.huida.platform.settlement.dto.RylCountCommissionDTO;
import com.huida.platform.settlement.dto.RylCountReportDto;
import com.huida.platform.settlement.dto.RylCountStockSettDTO;
import com.huida.platform.settlement.dto.settlement.rylcount.response.SettChnnelResultExportVo;
import com.huida.platform.settlement.entity.countpay.RylCountSettlementChannelMappingCfgEntity;
import com.huida.platform.settlement.entity.countpay.RylCountSettlementUserDepCfgEntity;
import com.huida.platform.settlement.entity.settlement.RylDictCfgEntity;
import com.huida.platform.settlement.mapper.RylCountSettlementUserDepCfgMapper;
import com.huida.platform.settlement.mapper.reconciliationpay.RylCountSettlementChannelMappingCfgMapper;
import com.huida.platform.settlement.mapper.settlement.RylDictCfgMapper;
import com.huida.platform.settlement.service.settlement.RylCountSettService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 结算中心--管理报表导出功能
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/manageReport")
public class RylManageReportController {
    @Autowired
    private RylCountSettService rylCountSettService;
    @Autowired
    RylCountSettlementUserDepCfgMapper rylCountSettlementUserDepCfgMapper;
    @Autowired
    RylCountSettlementChannelMappingCfgMapper rylCountSettlementChannelMappingCfgMapper;
    @Autowired
    private RylDictCfgMapper rylDictCfgMapper;

    /**
     * 管理报表导出
     * @param settChnnelResultExportVo
     * @return
     */
    @PostMapping("/exportExcel")
    public BaseResponse manageExportExcel(@RequestBody SettChnnelResultExportVo settChnnelResultExportVo){
        log.info("渠道结算数据导出：{}", JSONObject.toJSONString(settChnnelResultExportVo));
        rylCountSettService.manageExportExcel(settChnnelResultExportVo);
        return BaseResponse.success(true);
    }

    /**
     * 下载队列查看
     * @param settChnnelResultExportVo
     * @return
     */
    @GetMapping("/exportList")
    public BaseResponse exportList(SettChnnelResultExportVo settChnnelResultExportVo) {
        log.info("结算中心菜单，渠道结算数据清单导出队列查看入参：{}", JSONObject.toJSONString(settChnnelResultExportVo));
        return BaseResponse.success(rylCountSettService.exportList(settChnnelResultExportVo));
    }

    /**
     * 下载文件
     */
    @GetMapping("/getFilePath")
    public BaseResponse downLoad(String id) {
        log.info("结算中心菜单，渠道结算数据清单导出队列，oss下载入参：{}", JSONObject.toJSONString(id));
        return BaseResponse.success(rylCountSettService.exportDownLoad(id));
    }

    /**
     * 取消下载
     */
    @GetMapping("/cancelDownLoad")
    public BaseResponse cancelDownLoad(String id) {
        return BaseResponse.success(rylCountSettService.cancelDownLoad(id));
    }

    /**
     * 管理报表-查询用户所属部门
     * @param rylCountSettlementUserDepCfgEntity
     * @return
     */
    @PostMapping("/queryUserCfg")
    public BaseResponse manageExportExcel(@RequestBody RylCountSettlementUserDepCfgEntity rylCountSettlementUserDepCfgEntity){
        log.info("管理报表-查询用户所属部门：{}", JSONObject.toJSONString(rylCountSettlementUserDepCfgEntity));
        if(rylCountSettlementUserDepCfgEntity.getUserId() == null){
            return BaseResponse.fail("-1","没有登录用户信息");
        }
        QueryWrapper<RylCountSettlementUserDepCfgEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id",rylCountSettlementUserDepCfgEntity.getUserId());
        queryWrapper.eq("is_del",0);
        List<RylCountSettlementUserDepCfgEntity> cfgEntityList = rylCountSettlementUserDepCfgMapper.selectList(queryWrapper);
        return BaseResponse.success(cfgEntityList);
    }

    /**
     * 管理报表-品控报表导出
     * @param settChnnelResultExportVo
     * @return
     */
    @PostMapping("/qualityControl/export")
    public BaseResponse qualityControlExport(@RequestBody SettChnnelResultExportVo settChnnelResultExportVo){
        log.info("管理报表-品控报表导出：{}", JSONObject.toJSONString(settChnnelResultExportVo));
        rylCountSettService.qualityControlExport(settChnnelResultExportVo);
        return BaseResponse.success(true);
    }

    /**
     * 报表-查询渠道相关信息
     * @param settChnnelResultExportVo
     * @return
     */
    @PostMapping("/queryChannelByDep")
    public BaseResponse queryChannelByDep(@RequestBody SettChnnelResultExportVo settChnnelResultExportVo){
        log.info("管理报表-查询部门下所有渠道：{}", JSONObject.toJSONString(settChnnelResultExportVo));
        List<RylCountSettlementChannelMappingCfgEntity> cfgEntityList = rylCountSettlementChannelMappingCfgMapper.queryChannelBydept(settChnnelResultExportVo.getDetpList());
        return BaseResponse.success(cfgEntityList);
    }

    /**
     * 退保率报表导出
     * @param settChnnelResultExportVo
     * @return
     */
    @PostMapping("/cancelRate")
    public BaseResponse exportCancelRate(@RequestBody SettChnnelResultExportVo settChnnelResultExportVo){
        log.info("退保率报表导出：{}", JSONObject.toJSONString(settChnnelResultExportVo));
        rylCountSettService.exportCancelRate(settChnnelResultExportVo);
        return BaseResponse.success(true);
    }

    /**
     * 可结未付报表导出
     * @param rylCountReportDto
     * @return
     */
    @PostMapping("/exportUnsettledAmount")
    public BaseResponse exportUnsettledAmount(@RequestBody RylCountReportDto rylCountReportDto){
        log.info("可结未付报表导出：{}", JSONObject.toJSONString(rylCountReportDto));
        rylCountSettService.exportUnsettledAmount(rylCountReportDto);
        return BaseResponse.success(true);
    }

    /**
     * 可结未付报表明细导出
     * @param rylCountReportDto
     * @return
     */
    @PostMapping("/exportUnsettledDetail")
    public BaseResponse exportUnsettledDetail(@RequestBody RylCountReportDto rylCountReportDto){
        log.info("可结未付报表明细导出：{}", JSONObject.toJSONString(rylCountReportDto));
        rylCountSettService.exportUnsettledDetail(rylCountReportDto);
        return BaseResponse.success(true);
    }

    /**
     * 新结算进度报表导出
     * @param rylCountReportDto
     * @return
     */
    @PostMapping("/newSchedule")
    public BaseResponse newSchedule(@RequestBody RylCountReportDto rylCountReportDto){
        log.info("新结算进度报表导出：{}", JSONObject.toJSONString(rylCountReportDto));
        rylCountSettService.exportNewSchedule(rylCountReportDto);
        return BaseResponse.success(true);
    }

    /**
     * 可结未付报表明细查询
     * @param rylCountReportDto
     * @return
     */
    @PostMapping("/queryUnsettledDetail")
    public BaseResponse queryUnsettledDetail(@RequestBody RylCountReportDto rylCountReportDto){
        log.info("可结未付报表明细查询：{}", JSONObject.toJSONString(rylCountReportDto));
        return rylCountSettService.queryUnsettledDetail(rylCountReportDto);
    }

    /**
     * 获取所有的结算负责人
     * @return
     */
    @PostMapping("/queryAllOperator")
    public BaseResponse queryAllOperator(){
        return rylCountSettService.queryAllOperator();
    }

    /**
     * 借款与核销报表导出
     * @param rylCountReportDto
     * @return
     * @apiNote 该接口为异步导出，返回任务ID后可通过其他接口查询导出进度和下载链接
     */
    @PostMapping("/settBillPay")
    public BaseResponse settBillPay(@RequestBody @Valid RylCountReportDto rylCountReportDto) {
        long startTime = System.currentTimeMillis();
        log.info("借款与核销报表导出开始，参数：{}", JSONObject.toJSONString(rylCountReportDto));
        try {
            // 参数校验
            if (rylCountReportDto == null) {
                log.warn("借款与核销报表导出参数为空");
                return BaseResponse.fail("请求参数不能为空");
            }
            String result = rylCountSettService.settBillPay(rylCountReportDto);
            long endTime = System.currentTimeMillis();
            log.info("借款与核销报表导出完成，耗时：{}ms，结果：{}", endTime - startTime, result);

            return BaseResponse.success(result);

        } catch (IllegalArgumentException e) {
            log.warn("借款与核销报表导出参数错误：{}", e.getMessage());
            return BaseResponse.fail("参数错误：" + e.getMessage());
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("借款与核销报表导出异常，耗时：{}ms", endTime - startTime, e);
            return BaseResponse.fail("导出失败，请稍后重试");
        }
    }

    /**
     * 渠道费率报表导出
     * @param rylCountReportDto
     * @return
     */
    @PostMapping("/exportChannelRate")
    public BaseResponse exportChannelRate(@RequestBody RylCountReportDto rylCountReportDto){
        log.info("渠道费率报表导出：{}", JSONObject.toJSONString(rylCountReportDto));
        rylCountSettService.exportChannelRate(rylCountReportDto);
        return BaseResponse.success(true);
    }

    /**
     * 手续费追佣预测报表导出
     * @param rylCountReportDto
     * @return
     */
    @PostMapping("/exportForecastCommission")
    public BaseResponse exportForecastCommission(@RequestBody RylCountReportDto rylCountReportDto){
        log.info("手续费追佣预测报表导出：{}", JSONObject.toJSONString(rylCountReportDto));
        rylCountSettService.exportForecastCommission(rylCountReportDto);
        return BaseResponse.success(true);
    }


    /**
     * 非弘结算数据报表导出
     * @param rylCountReportDto
     * @return
     */
    @PostMapping("/fhSettExport")
    public BaseResponse fhSettExport(@RequestBody RylCountReportDto rylCountReportDto){
        log.info("非弘结算数据报表导出：{}", JSONObject.toJSONString(rylCountReportDto));
        rylCountSettService.fhSettExport(rylCountReportDto);
        return BaseResponse.success(true);
    }

    /**
     * 手续费已付未付报表导出
     * @param rylCountCommissionDTO
     * @return
     */
    @PostMapping("/commissionPaidAndUnpaid")
    public BaseResponse commissionPaidAndUnpaidExport(@RequestBody RylCountCommissionDTO rylCountCommissionDTO){
        log.info("手续费已付未付报表导出：{}", JSONObject.toJSONString(rylCountCommissionDTO));
        rylCountSettService.commissionPaidAndUnpaidExport(rylCountCommissionDTO);
        return BaseResponse.success(true);
    }

    /**
     * 手续费已付未付报表-查询交易类型
     * @param
     * @return
     */
    @PostMapping("/queryOpcodeList")
    public BaseResponse queryOpcodeList(){
        QueryWrapper<RylDictCfgEntity> cfgQueryWrapper = new QueryWrapper<>();
        cfgQueryWrapper.eq("code_type","opSubCode");
        cfgQueryWrapper.eq("is_del",0);
        List<RylDictCfgEntity> cfgEntityList = rylDictCfgMapper.selectList(cfgQueryWrapper);
        return BaseResponse.success(cfgEntityList);
    }

    /**
     * 手续费已付未付报表-查询费用类型
     * @param
     * @return
     */
    @PostMapping("/queryRateTypeList")
    public BaseResponse queryRateTypeList(){
        QueryWrapper<RylDictCfgEntity> cfgQueryWrapper = new QueryWrapper<>();
        cfgQueryWrapper.eq("code_type","rate_type");
        cfgQueryWrapper.eq("is_del",0);
        List<RylDictCfgEntity> cfgEntityList = rylDictCfgMapper.selectList(cfgQueryWrapper);
        return BaseResponse.success(cfgEntityList);
    }

    /**
     * 手续费经营分析报表导出
     * @param rylCountStockSettDTO
     * @return
     */
    @PostMapping("/commissionBusiAnalysisExport")
    public BaseResponse commissionBusiAnalysisExport(@RequestBody RylCountStockSettDTO rylCountStockSettDTO){
        log.info("手续费经营分析报表导出："+JSONObject.toJSONString(rylCountStockSettDTO));
        rylCountSettService.commissionBusiAnalysisExport(rylCountStockSettDTO);
        return BaseResponse.success(true);
    }

    /**
     * 投连存量明细报表
     * @param rylCountStockSettDTO
     * @return
     */
    @PostMapping("/investmentLinkStockExport")
    public BaseResponse investmentLinkStockExport(@RequestBody RylCountStockSettDTO rylCountStockSettDTO){
        log.info("投连存量明细报表导出："+JSONObject.toJSONString(rylCountStockSettDTO));
        rylCountSettService.investmentLinkStockExport(rylCountStockSettDTO);
        return BaseResponse.success(true);
    }
}
