package com.huida.platform.settlement.handler;

import com.huida.platform.settlement.service.reconciliationpay.SettBillJobHandlerService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 2、同步 结算单号的支付状态，更新融汇核心
 *    费控系统批单号付款结束后，融汇核心直连费控库 查询视图 回写对应批单号的支付状态
 *    25.7调整: 费控拆分 弘康费控、融汇费控,不同单据需在不同费控查询;且新数据费控不再维护视图,故优先使用新增的接口查询,次用老视图查询.
 * <AUTHOR>
 */
@Slf4j
@Component
@JobHandler("asynSettBillPayResultHandler")
public class AsynSettBillPayResultHandler extends IJobHandler {

    @Autowired
    private SettBillJobHandlerService settBillJobHandlerService;

    @Override
    public ReturnT<String> execute(String param){
        log.info("费控结算单付费结果更新开始，请求参数：{}" ,param);
        long time = System.currentTimeMillis();
        settBillJobHandlerService.syncBillPayResult(param);
        log.info("费控结算单付费结果更新结束,耗时:{}",System.currentTimeMillis() - time);
        return SUCCESS;
    }
}
