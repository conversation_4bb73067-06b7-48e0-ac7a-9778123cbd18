package com.huida.platform.settlement.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.UUID;

/**
 * API调用日志工具类
 * 用于统一处理API请求和响应的日志记录，确保并发环境下的日志可追踪性
 * 
 * <AUTHOR>
 */
@Slf4j
public class ApiLogUtil {
    
    private static final String REQUEST_ID_KEY = "requestId";
    private static final String API_TYPE_KEY = "apiType";
    private static final String ATTEMPT_KEY = "attempt";
    
    /**
     * 生成请求ID并设置到MDC中
     * 
     * @param apiType API类型标识
     * @return 生成的请求ID
     */
    public static String startApiCall(String apiType) {
        String requestId = generateRequestId();
        MDC.put(REQUEST_ID_KEY, requestId);
        MDC.put(API_TYPE_KEY, apiType);
        return requestId;
    }
    
    /**
     * 设置重试次数到MDC中
     * 
     * @param attempt 重试次数
     */
    public static void setAttempt(int attempt) {
        MDC.put(ATTEMPT_KEY, String.valueOf(attempt));
    }
    
    /**
     * 记录API请求日志
     * 
     * @param url 请求URL
     * @param params 请求参数
     * @param attempt 重试次数
     */
    public static void logRequest(String url, Object params, int attempt) {
        String requestId = MDC.get(REQUEST_ID_KEY);
        String apiType = MDC.get(API_TYPE_KEY);
        
        log.info("[{}] 第{}次调用外部API[{}]，类型：{}，请求参数：{}", 
                requestId, attempt, url, apiType, JSONObject.toJSONString(params));
    }
    
    /**
     * 记录API响应日志
     * 
     * @param url 请求URL
     * @param response 响应内容
     * @param costTime 耗时（毫秒）
     * @param httpStatus HTTP状态码
     */
    public static void logResponse(String url, Object response, long costTime, int httpStatus) {
        String requestId = MDC.get(REQUEST_ID_KEY);
        String apiType = MDC.get(API_TYPE_KEY);
        String attempt = MDC.get(ATTEMPT_KEY);
        
        log.info("[{}] 第{}次调用外部API[{}]响应，类型：{}，耗时：{}ms，HTTP状态：{}，响应内容：{}", 
                requestId, attempt, url, apiType, costTime, httpStatus, JSONObject.toJSONString(response));
    }
    
    /**
     * 记录API成功日志
     * 
     * @param dataSize 返回数据大小
     */
    public static void logSuccess(int dataSize) {
        String requestId = MDC.get(REQUEST_ID_KEY);
        String apiType = MDC.get(API_TYPE_KEY);
        
        log.info("[{}] {}调用成功，返回数据量：{}", requestId, apiType, dataSize);
    }
    
    /**
     * 记录API错误日志
     * 
     * @param errorMsg 错误信息
     * @param attempt 重试次数
     * @param maxRetry 最大重试次数
     */
    public static void logError(String errorMsg, int attempt, int maxRetry) {
        String requestId = MDC.get(REQUEST_ID_KEY);
        String apiType = MDC.get(API_TYPE_KEY);
        
        if (attempt < maxRetry) {
            log.warn("[{}] {}第{}次调用失败：{}，将进行重试", requestId, apiType, attempt, errorMsg);
        } else {
            log.error("[{}] {}调用失败，已重试{}次：{}", requestId, apiType, maxRetry, errorMsg);
        }
    }
    
    /**
     * 记录重试等待日志
     * 
     * @param waitSeconds 等待秒数
     * @param nextAttempt 下次重试次数
     */
    public static void logRetryWait(int waitSeconds, int nextAttempt) {
        String requestId = MDC.get(REQUEST_ID_KEY);
        String apiType = MDC.get(API_TYPE_KEY);
        
        log.info("[{}] {}等待{}秒后进行第{}次重试", requestId, apiType, waitSeconds, nextAttempt);
    }
    
    /**
     * 清理MDC，避免内存泄漏
     * 在API调用完成后必须调用此方法
     */
    public static void clearMDC() {
        MDC.remove(REQUEST_ID_KEY);
        MDC.remove(API_TYPE_KEY);
        MDC.remove(ATTEMPT_KEY);
    }
    
    /**
     * 生成唯一的请求ID
     * 
     * @return 8位随机字符串
     */
    private static String generateRequestId() {
        return UUID.randomUUID().toString().substring(0, 8);
    }
    
    /**
     * 获取当前请求ID
     * 
     * @return 当前请求ID，如果没有则返回"UNKNOWN"
     */
    public static String getCurrentRequestId() {
        String requestId = MDC.get(REQUEST_ID_KEY);
        return requestId != null ? requestId : "UNKNOWN";
    }
}
