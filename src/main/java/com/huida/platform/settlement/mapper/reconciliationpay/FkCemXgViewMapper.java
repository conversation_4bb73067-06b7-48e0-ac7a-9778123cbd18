package com.huida.platform.settlement.mapper.reconciliationpay;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huida.platform.settlement.entity.count.FkCemXgV;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
@DS("fk")
@Mapper
public interface FkCemXgViewMapper extends BaseMapper<FkCemXgV> {

    /**
     * 查询 支付结果
     * 审批节点状态,也放在此视图里(不再单独查视图里的表T_FEE_ECS_BILL_DETAIL 获取了)
     * @param billList 结算单号列表
     * @return
     */
    @Select({
            "<script>",
            "select BILLNO,PAYSTATUS,ACCOUNTING_DATE,GETNAME,GETACCOUNT,GETBANKBRANCH,PAYMADEDATE,ORIGINALCURRENCYSUM,BILLSTATUS from FKPRD.CEM_XG_V where PAYSTATUS!=0" +
                    " AND BILLNO in " ,
            "<foreach collection='billList' item = 'bill' open='(' separator=',' close=')'> #{bill} </foreach>  ",
            " order by MODIFYTIME desc",
            "</script>"
    })
    List<FkCemXgV> selectBillNoResult(@Param("billList") List<String> billList);
}
