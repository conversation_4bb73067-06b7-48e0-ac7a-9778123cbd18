package com.huida.platform.settlement.mapper.reconciliationpay;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huida.platform.settlement.dto.QuerySettStatusDto;
import com.huida.platform.settlement.dto.QuerySettStatusResDto;
import com.huida.platform.settlement.dto.reconciliationpay.*;
import com.huida.platform.settlement.entity.count.FkCemXgV;
import com.huida.platform.settlement.entity.countpay.RylCountSettlementBillPayEntity;
import com.huida.platform.settlement.vo.CommissionSettlementReqVo;
import com.huida.platform.settlement.vo.CommissionSettlementResVo;
import com.huida.platform.settlement.vo.RylCountBillPayResVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @since 20220228
 */
@DS("ryl")
@Mapper
public interface RylCountSettlementBillPayMapper extends BaseMapper<RylCountSettlementBillPayEntity> {

    /**
     * 结算单数据列表查询 付款
     *
     * @param page
     * @param query
     * @return IPage
     */
    IPage<RylCountSettlementBillPayDto> queryCountData(Page<RylCountSettlementBillPayDto> page,
                                                           @Param(Constants.WRAPPER) Wrapper<RylCountSettlementBillPayEntity> query);

    /**
     * 结算单数据列表查询 付款
     *
     * @param query
     * @return IPage
     */
    List<Map<String,Object>> queryCountExcelData(@Param(Constants.WRAPPER) Wrapper<RylCountSettlementBillPayEntity> query);

    /**
     * 获取需要推送给费控的 审批结算清单列表
     *  【只取未付款的，付款失败的需要明确原因后再运维确认是否重推（以防多次付款）】
     * @param countNo
     * @return
     */
    @Select({
            "<script>",
            "select id,count_no,channel_code,channel_name,commission_charge_total_after,count_start_time,count_end_time,user_id,create_time,update_time,commission_charge_total,user_fk_uuid,is_pay_behalf,manage_com,is_hkrh_sett " +
                    "  from ryl_count_settlement_bill_pay where examine_status=2 and asyn_fk_flag in (0,2) and bill_pay_status=0 and merchant_type != '1' " +
                    "<when test='countNo!=null'>" +
                    " and count_no=#{countNo}" +
                    " </when>" +
                    " order by update_time asc",
            "</script>"
    })
    List<RylCountSettlementBillPayEntity> queryUnSyncBillPay(@Param("countNo") String countNo);

    /**
     * 获取需要查支付状态的单据号列表
     * @param countNo
     * @param startDate
     * @param endDate
     * @return
     */
    @Select({
            "<script>",
            "select user_fk_uuid,fk_bill_no from ryl_count_settlement_bill_pay where (fk_bill_no is not null AND LENGTH(TRIM(fk_bill_no)) > 0) and bill_pay_status in (0,2) and user_fk_uuid  is  not null " +
                    "<when test='countNo!=null'>" +
                    " and count_no=#{countNo}" +
                    " </when>" +
                    "<when test='startDate!=null'>" +
                    " and count_start_time <![CDATA[ >= ]]> #{startDate}" +
                    " </when>" +
                    "<when test='endDate!=null'>" +
                    " and count_end_time <![CDATA[ <= ]]> #{endDate}" +
                    " </when>" +
                    "</script>"
    })
    List<Map<String, Object>> queryUnPayList(@Param("countNo") String countNo, @Param("startDate") Date startDate, @Param("endDate") Date endDate);
    /**
     * 根据批单号更新支付状态
     *  新增 费控审批节点状态
     * @param billList
     */
    @Update("<script>" +
            "<foreach collection=\"billList\" item=\"item\" index=\"index\" separator=\";\">" +
            "update ryl_count_settlement_bill_pay set bill_pay_status=#{item.payStatus},update_time=now(),fk_accounting_date=#{item.accountingDate}," +
            " fk_get_name=#{item.getName},fk_get_account=#{item.getAccount},fk_bank_branch=#{item.getBankBranch}," +
            " fk_pay_date=#{item.payMadeDate},fk_bill_status=#{item.billStatus} where fk_bill_no= #{item.billNo}" +
            "</foreach>" +
            "</script>"
    )
    void batchUpdatePayFlag(@Param("billList") List<FkCemXgV> billList);

    /**
     * 根据费控系统单据编号 获取融汇核心结算单号
     * @param billNo
     * @return
     */
    @Select({
            "<script>",
            "select * from ryl_count_settlement_bill_pay where fk_bill_no= #{billNo}",
            "</script>"
    })
    RylCountSettlementBillPayEntity queryCountNo(@Param("billNo") String billNo);

    /**
     * 根据费控系统单据编号 获取融汇核心结算单
     * @param billNo
     * @return
     */
    @Select({
            "<script>",
            "select * from ryl_count_settlement_bill_pay where fk_bill_no= #{billNo}",
            "</script>"
    })
    RylCountSettlementBillPayEntity queryListByBillNo(@Param("billNo") String billNo);

    /**
     * 更新 结算单推送费控系统的结果
     * @param billPay
     */
    @Update({
            "<script>",
            "update ryl_count_settlement_bill_pay set asyn_fk_flag=#{billPay.asynFkFlag},asyn_fk_desc=#{billPay.asynFkDesc},asyn_fk_success_date=#{billPay.asynFkSuccessDate},fk_bill_no=#{billPay.fkBillNo},bill_type=#{billPay.billType},fk_verification_sheet=#{billPay.fkVerificationSheet},update_time=now() " ,
                    " where count_no= #{billPay.countNo}",
            "</script>"
    })
    void updateBillToFkResult(@Param("billPay") RylCountSettlementBillPayEntity billPay);
    /**
     * 根据结算单号 查询结算单总记录
     * @param countNo
     * @return
     */
    @Select({
            "<script>",
            "select id,channel_code,count_no,mio_amnt_total,commission_charge_total,mio_amnt_total_after,commission_charge_total_after,fk_bill_no,count_start_time,count_end_time,reconciliation_status,manage_com " +
                    " from ryl_count_settlement_bill_pay where count_no=#{countNo}",
            "</script>"
    })
    RylCountSettlementBillPayEntity queryByCountNo(@Param("countNo") String countNo);

    /**
     * 根据结算单号 更新对账状态
     * @param countNo
     */
    @Update({
            "<script>",
            "update ryl_count_settlement_bill_pay set diffs=#{diffCount},reconciliation_status=#{reconStatus},update_time=now(),reconciliation_time=now() where count_no=#{countNo}",
            "</script>"
    })
    void updateReconciliationStatus(@Param("countNo") String countNo,@Param("reconStatus") String reconStatus,@Param("diffCount") String diffCount);
    /**
     * 把逐单调差的金额 更新到主bill上
     * @param countNo
     * @param chargeAfter
     * @return
     */
    @Update({
            "<script>",
            "update ryl_count_settlement_bill_pay set commission_charge_total_after=#{chargeAfter} where count_no=#{countNo}",
            "</script>"
    })
    int updateChargeTotalAfter(@Param("countNo") String countNo, @Param("chargeAfter") BigDecimal chargeAfter);

    /**
     * 查询 已经推费控的 结算单(同一渠道的)
     *
     * @param channelCode
     */
    @Select({
            "<script>",
            "select count_no from ryl_count_settlement_bill_pay where asyn_fk_flag=1 and channel_code=#{channelCode}",
            "</script>"
    })
    List<String> queryFkCountNos(@Param("channelCode") String channelCode);

    @Update({
            "<script>",
            "update ryl_count_settlement_bill_pay set batch_diff_file=#{fileName},commission_charge_total_after=#{chargeAfter},mio_amnt_total_after=#{amntAfter} where count_no=#{countNo}",
            "</script>"
    })
    int updateOssBatchFile(@Param("countNo") String countNo, @Param("fileName") String fileName, @Param("chargeAfter") String chargeAfter, @Param("amntAfter") String amntAfter);

    /**
     * 手续费结算单查询接口
     * @param page
     */
    @Select({
            "<script>",
            "select r.protocol_bouncers,t.channel_code, r.channel_name, t.count_no, t.commission_charge_total,t.commission_charge_total_after, t.create_time,t.reconciliation_remark, t.reconciliation_status, t.bill_pay_status, t.reconciliation_time, t.fk_pay_date " +
                    " FROM ryl_count_settlement_bill_pay t LEFT JOIN ryl_count_settlement_channel_cfg r ON t.channel_code = r.channel_code where 1=1 " +
                    " <if test=\"settResVo.startDate != null and settResVo.endDate  != null \" >" +
                    " and DATE_FORMAT(t.create_time,'%Y-%m') BETWEEN #{settResVo.startDate} AND #{settResVo.endDate}" +
                    " </if>" +
                    " and exists (select 1 from ryl_count_settlement_channel_lab lab where t.channel_code = lab.channel_code and lab.is_ryl_settlement = '0' and lab.is_del = '0') " +

                    "<if test='channelCodeList!=null and channelCodeList.size() > 0'>" ,
                    "    and t.channel_code in " ,
                    "            <foreach item='item' collection='channelCodeList' open='(' separator=',' close=')'>" +
                            "            #{item}",
                    "            </foreach>" ,
                    "</if>",

                    "<if test='settResVo.countNoList!=null and settResVo.countNoList.size() > 0'>" ,
                    "    and t.count_no in " ,
                    "            <foreach item='item' collection='settResVo.countNoList' open='(' separator=',' close=')'>" +
                            "            #{item}",
                    "            </foreach>" ,
                    "</if>",

                    "<if test='settResVo.protocolBouncers!=null and settResVo.protocolBouncers!=\"\"'>" +
                        " and r.protocol_bouncers=#{settResVo.protocolBouncers}" +
                    "</if>" +

                    "<if test='settResVo.merchantType!=null and settResVo.merchantType!=\"\"'>" +
                    " and t.merchant_type=#{settResVo.merchantType}" +
                    "</if>" +

                    "<if test='reStatus!=null and settCodeList!=null and settCodeList.size() == 0 and billPayStatus==null '>" +
                        " and (t.reconciliation_status is null or t.reconciliation_status = '0')" +
                    "</if>" +

                    "<if test='settCodeList!=null and settCodeList.size() > 0'>" ,
                        "   and ((t.bill_pay_status = '0' and t.reconciliation_status in " ,
                        "            <foreach item='sett' collection='settCodeList' open='(' separator=',' close=')'>" +
                                "            #{sett}",
                        "            </foreach>" ,
                    " )"+

                    "<if test='billPayStatus == 1 and settCodeList!=null and settCodeList.size() > 0 '>" +
                        " or (t.bill_pay_status ='1' and t.examine_status = '2' )" +
                    "</if>" +

                    "<if test='reStatus == 1 and settCodeList!=null and settCodeList.size() > 0 '>" +
                        " or (t.reconciliation_status is null or t.reconciliation_status = '0')" +
                    "</if>" +
                    ")"+
                    "</if>",

                    "<if test='billPayStatus!=null and settCodeList!=null and settCodeList.size() == 0 and reStatus==null '>" +
                        " and t.bill_pay_status =#{billPayStatus} " +
                    "</if>" +
                    " order by t.create_time desc "+
                    "</script>"
    })
    IPage<CommissionSettlementResVo> querySettlementListPage(IPage<RylCountSettlementBillPayEntity> page, @Param("settResVo") CommissionSettlementReqVo commissionSettlementReqVo, @Param("channelCodeList") List<String> channelCodeList, @Param("settCodeList") List<String> settCodeList, @Param("billPayStatus") String billPayStatus, @Param("reStatus") String reStatus);

    /**
     * 容易连透出接口分页查询结算单
     * @param page
     */
    @Select({
            "<script>",
            "select count_no as countNo,channel_code as channelCode,commission_charge_total as commissionChargeTotal,commission_charge_total_after as commissionChargeTotalAfter, " +
                    " reconciliation_status as reconciliationStatus,reconciliation_time as reconciliationTime,create_time as createTime,bill_pay_status as billPayStatus  " +
                    " from ryl_count_settlement_bill_pay  " +
                    "        <if test=\"ew.emptyOfWhere == false\">\n" +
                    "            ${ew.customSqlSegment}\n" +
                    "        </if>" +
                    "</script>"
    })
    IPage<RylCountBillPayResVo> queryCountNoListPage(IPage<RylCountSettlementBillPayEntity> page, @Param("ew") QueryWrapper<RylCountSettlementBillPayEntity> queryWrapper);

    /**
     * 查看订单数据 是否已合进结算单里
     *
     * @param orderId
     * @return
     */
    @Select({
            "<script>",
            " select count(*) from ryl_count_settlement_bill_pay where  order_ids like concat(#{orderId}, '%')",
            "</script>"
    })
    int queryByOrderId(@Param("orderId") String orderId);

    /**
     * 结算节点列表查询
     * @param page
     */
    @Select({
            "<script>",
            "select t.channel_code as channelCode,s.channel_name as channelName,t.count_no as countNo,t.fk_bill_no as fkBillNo, " +
                    " t.commission_charge_total as commissionChargeTotal,t.commission_charge_total_after as commissionChargeTotalAfter, " +
                    " t.reconciliation_status as reconciliationStatus,t.bill_pay_status as billPayStatus,t.examine_operator as examineOperator, " +
                    " t.examine_status as examineStatus,t.count_start_time as countStartTime,t.count_end_time as countEndTime,asyn_fk_flag as asynFkFlag,fk_bill_status as fkBillStatus, " +
                    " t.asyn_fk_success_date as asynFkSuccessDate,t.fk_pay_date as fkPayDate,t.approval_num as approvalNum,t.merchant_type as merchantType," +
                    " t.examine_sub_status as examineSubStatus,d.examine_status as overallDiffExamineStatus " +
                    " from ryl_count_settlement_bill_pay t left join ryl_count_settlement_channel_cfg s on t.channel_code = s.channel_code" +
                    " left join ryl_count_settlement_overall_diff d on d.count_no = t.count_no and d.is_del = '0' where 1=1" +
                    "<if test='settDto.countNo!=null and settDto.countNo!=\"\"'>" +
                    " and t.count_no=#{settDto.countNo}" +
                    "</if>" +
                    "<if test='settDto.channelCode!=null and settDto.channelCode!=\"\"'>" +
                    " and t.channel_code=#{settDto.channelCode}" +
                    "</if>" +
                    "<if test='settDto.fkBillNo!=null and settDto.fkBillNo!=\"\"'>" +
                    " and t.fk_bill_no=#{settDto.fkBillNo}" +
                    "</if>" +
                    "<if test='settDto.depCodeList!=null and settDto.depCodeList.size() > 0'>" +
                    " and dep_code in " +
                    " <foreach item='depCode' collection='settDto.depCodeList' open='(' separator=',' close=')'>" +
                    " #{depCode}" +
                    " </foreach>" ,
                    "</if>" +
                    "<if test=' settDto.nodeList!=null and settDto.nodeList.size() > 0 and (settDto.statusList==null or settDto.statusList.size() == 0) '>" +
                        " and " +
                        "<foreach item='node' collection='settDto.nodeList' index='index' >" +
                            "<if test='node==\"sett_reconciliation\" '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " (t.reconciliation_status in ('0','1','2') " +
                                " and t.examine_status = '0') " +
                            "</if>" +
                            "<if test='node==\"sett_review\" '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " (t.examine_status in ('1','2','3') " +
                                " and t.asyn_fk_flag = '0' and t.examine_sub_status != '5') " +
                            "</if>" +
                            "<if test='node==\"sett_pushing\" '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " t.asyn_fk_flag = '2' " +
                            "</if>" +
                            "<if test='node==\"sett_approval\" '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " t.asyn_fk_flag = '1' " +
                            "</if>" +
                            "<if test='node==\"sett_kaipiao\" '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " (t.examine_sub_status in ('3','6') " +
                                " and t.examine_status in ('1','3')" +
                                " and t.merchant_type = '1')" +
                            "</if>" +
                            "<if test='node==\"sett_shoukuan\" '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " (t.examine_status in ('2') " +
                                " and t.merchant_type = '1') " +
                            "</if>" +
                            "<if test='node==\"sett_overallDiff\" '>" +
                            "<if test='index > 0 '>" +
                            " or " +
                            "</if>" +
                            " (d.examine_status in ('0','1','2','3') " +
                            " and t.reconciliation_status = '0')" +
                            "</if>" +
                        "</foreach>" ,
                    "</if>" +
                    "<if test='settDto.statusList!=null and settDto.statusList.size() > 0 '>" +
                        " and " +
                        "<foreach item='status' collection='settDto.statusList' index='index' >" +
                            "<if test='status==1 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " (t.reconciliation_status = '0' " +
                                " and t.examine_status = '0') " +
                            "</if>" +
                            "<if test='status==2 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " (t.reconciliation_status = '1' " +
                                " and t.examine_status = '0') " +
                            "</if>" +
                            "<if test='status==3 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " (t.reconciliation_status = '2' " +
                                " and t.examine_status = '0') " +
                            "</if>" +
                            "<if test='status==4 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " t.examine_status = '1' " +
                            "</if>" +
                            "<if test='status==5 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " (t.examine_status = '2' " +
                                " and t.asyn_fk_flag = '0'" +
                                " and t.examine_sub_status != '5') " +
                            "</if>" +
                            "<if test='status==6 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " t.examine_status = '3' " +
                            "</if>" +
                            "<if test='status==7 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " t.asyn_fk_flag = '2' " +
                            "</if>" +
                            "<if test='status==8 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " (t.asyn_fk_flag = '1' and t.fk_bill_status = '0')" +
                            "</if>" +
                            "<if test='status==9 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " t.fk_bill_status = '1' " +
                            "</if>" +
                            "<if test='status==10 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " t.fk_bill_status = '2' " +
                            "</if>" +
                            "<if test='status==11 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " t.fk_bill_status = '3' " +
                            "</if>" +
                            "<if test='status==12 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " t.fk_bill_status = '4' " +
                            "</if>" +
                            "<if test='status==13 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " t.fk_bill_status = '5' " +
                            "</if>" +
                            "<if test='status==14 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " t.examine_sub_status = '3' " +
                            "</if>" +
                            "<if test='status==15 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " t.examine_sub_status = '6' " +
                            "</if>" +
                            "<if test='status==16 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " (t.examine_sub_status = '5' " +
                                " and t.bill_pay_status in (0,2))" +
                            "</if>" +
                            "<if test='status==17 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                            " (t.examine_sub_status = '5' " +
                            " and t.bill_pay_status in (1))" +
                            "</if>" +
                            "<if test='status==18 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " d.examine_status = '0' " +
                            "</if>" +
                            "<if test='status==19 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " d.examine_status = '1' " +
                            "</if>" +
                            "<if test='status==20 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " d.examine_status = '2' " +
                            "</if>" +
                            "<if test='status==21 '>" +
                                "<if test='index > 0 '>" +
                                " or " +
                                "</if>" +
                                " d.examine_status = '3' " +
                            "</if>" +
                        "</foreach>" ,
                    "</if>" +
                    " order by t.create_time desc ",
            "</script>"
    })
    IPage<QuerySettStatusResDto> querySettNodeListPage(IPage<RylCountSettlementBillPayEntity> page, @Param("settDto") QuerySettStatusDto querySettStatusDto);

    /**
     * 可结佣金非0或不可结佣金为0，且已对账成功的（不再允许生成结算单，除非已对账成功的结算单删除）
     *
     * @param map
     * @return
     */
    @Select({
            "<script>",
            "select * from ryl_count_settlement_bill_pay where reconciliation_status=1 and count_no in (" +
                    "select distinct(count_no) from ryl_count_settlement_upload_pay where ((settle_status ='Y' and commission_charge=#{map.commissionCharge}) or (settle_status='Never' and commission_charge=0.00))" +
                    " and policy_no=#{map.policyNo} and risk_code=#{map.riskCode}" +
                    " and op_code=#{map.opCode} and op_sub_code=#{map.opSubCode} and rate_type=#{map.rateType} and business_date=#{map.businessDate} " +
                    ")",
            "</script>"
    })
    List<RylCountSettlementBillPayEntity> selectReconSucessByMap(@Param("map") Map<String, Object> map);


    /**
     * 结算节点列表查询
     * @param page
     */
    @Select({
            "<script>",
            "select t.channel_code as channelCode,s.channel_name as channelName,t.count_no as countNo,t.fk_bill_no as fkBillNo, " +
                    " t.commission_charge_total as commissionChargeTotal,t.commission_charge_total_after as commissionChargeTotalAfter, " +
                    " t.reconciliation_status as reconciliationStatus,t.bill_pay_status as billPayStatus,t.examine_operator as examineOperator, " +
                    " t.examine_status as examineStatus,t.count_start_time as countStartTime,t.count_end_time as countEndTime,asyn_fk_flag as asynFkFlag,fk_bill_status as fkBillStatus, " +
                    " t.asyn_fk_success_date as asynFkSuccessDate,t.fk_pay_date as fkPayDate,t.approval_num as approvalNum,t.merchant_type as merchantType, " +
                    " t.examine_sub_status as examineSubStatus,d.examine_status as overallDiffExamineStatus " +
                    " from ryl_count_settlement_bill_pay t left join ryl_count_settlement_channel_cfg s on t.channel_code = s.channel_code" +
                    " left join ryl_count_settlement_overall_diff d on d.count_no = t.count_no and d.is_del = '0' where 1=1" +
                    "<if test='settDto.countNo!=null and settDto.countNo!=\"\"'>" +
                    " and t.count_no=#{settDto.countNo}" +
                    "</if>" +
                    "<if test='settDto.channelCode!=null and settDto.channelCode!=\"\"'>" +
                    " and t.channel_code=#{settDto.channelCode}" +
                    "</if>" +
                    "<if test='settDto.fkBillNo!=null and settDto.fkBillNo!=\"\"'>" +
                    " and t.fk_bill_no=#{settDto.fkBillNo}" +
                    "</if>" +
                    "<if test='settDto.depCodeList!=null and settDto.depCodeList.size() > 0'>" +
                    " and dep_code in " +
                    " <foreach item='depCode' collection='settDto.depCodeList' open='(' separator=',' close=')'>" +
                    " #{depCode}" +
                    " </foreach>" ,
            "</if>" +
                    "<if test=' settDto.nodeList!=null and settDto.nodeList.size() > 0 and (settDto.statusList==null or settDto.statusList.size() == 0) '>" +
                    " and " +
                    "<foreach item='node' collection='settDto.nodeList' index='index' >" +
                    "<if test='node==\"sett_reconciliation\" '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " (t.reconciliation_status in ('0','1','2') " +
                    " and t.examine_status = '0') " +
                    "</if>" +
                    "<if test='node==\"sett_review\" '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " (t.examine_status in ('1','2','3') " +
                    " and t.asyn_fk_flag = '0' and t.examine_sub_status != '5') " +
                    "</if>" +
                    "<if test='node==\"sett_pushing\" '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " t.asyn_fk_flag = '2' " +
                    "</if>" +
                    "<if test='node==\"sett_approval\" '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " t.asyn_fk_flag = '1' " +
                    "</if>" +
                    "<if test='node==\"sett_kaipiao\" '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " (t.examine_sub_status in ('3','6') " +
                    " and t.examine_status in ('1','3')" +
                    " and t.merchant_type = '1')" +
                    "</if>" +
                    "<if test='node==\"sett_shoukuan\" '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " (t.examine_status in ('2') " +
                    " and t.merchant_type = '1') " +
                    "</if>" +
                    "<if test='node==\"sett_overallDiff\" '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " (d.examine_status in ('0','1','2','3') " +
                    " and t.reconciliation_status = '0')" +
                    "</if>" +
                    "</foreach>" ,
            "</if>" +
                    "<if test='settDto.statusList!=null and settDto.statusList.size() > 0 '>" +
                    " and " +
                    "<foreach item='status' collection='settDto.statusList' index='index' >" +
                    "<if test='status==1 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " (t.reconciliation_status = '0' " +
                    " and t.examine_status = '0') " +
                    "</if>" +
                    "<if test='status==2 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " (t.reconciliation_status = '1' " +
                    " and t.examine_status = '0') " +
                    "</if>" +
                    "<if test='status==3 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " (t.reconciliation_status = '2' " +
                    " and t.examine_status = '0') " +
                    "</if>" +
                    "<if test='status==4 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " t.examine_status = '1' " +
                    "</if>" +
                    "<if test='status==5 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " (t.examine_status = '2' " +
                    " and t.asyn_fk_flag = '0'" +
                    " and t.examine_sub_status != '5') " +
                    "</if>" +
                    "<if test='status==6 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " t.examine_status = '3' " +
                    "</if>" +
                    "<if test='status==7 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " t.asyn_fk_flag = '2' " +
                    "</if>" +
                    "<if test='status==8 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " (t.asyn_fk_flag = '1' and t.fk_bill_status = '0')" +
                    "</if>" +
                    "<if test='status==9 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " t.fk_bill_status = '1' " +
                    "</if>" +
                    "<if test='status==10 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " t.fk_bill_status = '2' " +
                    "</if>" +
                    "<if test='status==11 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " t.fk_bill_status = '3' " +
                    "</if>" +
                    "<if test='status==12 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " t.fk_bill_status = '4' " +
                    "</if>" +
                    "<if test='status==13 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " t.fk_bill_status = '5' " +
                    "</if>" +
                    "<if test='status==14 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " t.examine_sub_status = '3' " +
                    "</if>" +
                    "<if test='status==15 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " t.examine_sub_status = '6' " +
                    "</if>" +
                    "<if test='status==16 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " (t.examine_sub_status = '5' " +
                    " and t.bill_pay_status in (0,2))" +
                    "</if>" +
                    "<if test='status==17 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " (t.examine_sub_status = '5' " +
                    " and t.bill_pay_status in (1))" +
                    "</if>" +
                    "<if test='status==18 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " d.examine_status = '0' " +
                    "</if>" +
                    "<if test='status==19 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " d.examine_status = '1' " +
                    "</if>" +
                    "<if test='status==20 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " d.examine_status = '2' " +
                    "</if>" +
                    "<if test='status==21 '>" +
                    "<if test='index > 0 '>" +
                    " or " +
                    "</if>" +
                    " d.examine_status = '3' " +
                    "</if>" +
                    "</foreach>" ,
            "</if>" +
                    " order by t.create_time desc ",
            "</script>"
    })
    List<QuerySettStatusResDto> downSettNodeList(@Param("settDto") QuerySettStatusDto querySettStatusDto);

    /**
     * 查询已对账成功且需要自动推送企微审批的结算单
     * @return
     */
    @Select({
            "<script>",
            " select * from ryl_count_settlement_bill_pay t where t.reconciliation_status = '1' " +
            " and EXISTS (select 1 from ryl_count_settlement_channel_cfg c where t.channel_code = c.channel_code and c.is_wechat_approve = '0') " +
            " and t.examine_status = '0' " ,
            "</script>"
    })
    List<RylCountSettlementBillPayEntity> queryContnoList();

    /**
     * 更新渠道收款开户行编码以及名称
     * @param countNo
     * @param bankName
     * @return
     */
    @Update({
            "<script>",
            "update ryl_count_settlement_bill_pay set bank_code=#{bankCode},bank_name=#{bankName} where count_no=#{countNo}",
            "</script>"
    })
    int updateBankDetailByCountNo(@Param("countNo") String countNo, @Param("bankName") String bankName, @Param("bankCode") String bankCode);
}
