package com.huida.platform.settlement.example;

import com.huida.platform.settlement.util.ApiLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * API日志使用示例
 * 展示如何在并发环境下正确使用日志追踪
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ApiLogExample {
    
    /**
     * 示例：调用外部API的标准流程
     */
    public void exampleApiCall() {
        // 1. 开始API调用，生成请求ID
        String requestId = ApiLogUtil.startApiCall("费控系统查询");
        
        try {
            // 2. 模拟API调用
            List<String> params = Arrays.asList("param1", "param2");
            String url = "http://example.com/api";
            
            // 3. 记录请求日志
            ApiLogUtil.logRequest(url, params, 1);
            
            // 4. 模拟API响应
            String response = "{'code': '000000', 'data': []}";
            
            // 5. 记录响应日志
            ApiLogUtil.logResponse(url, response, 1500L, 200);
            
            // 6. 记录成功日志
            ApiLogUtil.logSuccess(0);
            
        } catch (Exception e) {
            // 7. 记录错误日志
            ApiLogUtil.logError("调用失败：" + e.getMessage(), 1, 3);
        } finally {
            // 8. 清理MDC（重要！）
            ApiLogUtil.clearMDC();
        }
    }
    
    /**
     * 示例：带重试的API调用
     */
    public void exampleApiCallWithRetry() {
        String requestId = ApiLogUtil.startApiCall("费控系统重试调用");
        
        try {
            int maxRetry = 3;
            for (int attempt = 1; attempt <= maxRetry; attempt++) {
                try {
                    ApiLogUtil.setAttempt(attempt);
                    
                    // 模拟API调用
                    if (attempt < 3) {
                        // 前两次失败
                        throw new RuntimeException("网络超时");
                    }
                    
                    // 第三次成功
                    ApiLogUtil.logSuccess(10);
                    break;
                    
                } catch (Exception e) {
                    ApiLogUtil.logError(e.getMessage(), attempt, maxRetry);
                    
                    if (attempt < maxRetry) {
                        // 记录重试等待
                        ApiLogUtil.logRetryWait(attempt, attempt + 1);
                        Thread.sleep(1000 * attempt);
                    } else {
                        throw e;
                    }
                }
            }
        } catch (Exception e) {
            log.error("最终调用失败", e);
        } finally {
            ApiLogUtil.clearMDC();
        }
    }
}
