package com.huida.platform.settlement.constant;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableTable;
import com.google.common.collect.Table;

import java.util.Map;

/**
 * @ClassName: CommonConstant
 * @Description: 提示信息公共类
 * <AUTHOR>
 * @date 2019年10月24日
 *
 */
public class CommonConstant {
	private CommonConstant() {
	}

	public static final String DIMENSION_PERIOD = "10001"; // 费率维度：期数
	public static final String DIMENSION_PAYMENT = "10002"; // 费率维度：交费期间
	public static final String DIMENSION_INSURED = "10003"; // 费率维度：保险期间
	public static final String DIMENSION_PERIOD_NAME = "期数"; // 期数
	public static final String DIMENSION_PAYMENT_NAME = "交费期间"; // 交费期间
	public static final String DIMENSION_INSURED_NAME = "保险期间"; // 保险期间

	/**
	 * 公司编码：
	 * 弘康：HongKang
	 * 融汇 ：RongHui
	 * 汇易达：HuiYiDa
	 */
	public static final String HONGKANG_CODE = "HongKang";

	public static final String RONGHUI_CODE = "RongHui";

	public static final String YIHUIDA_CODE = "HuiYiDa";

	/**
	 * excel sheet 页名字
	 */
	public static final String IMPROT_JINGDAI = "经代导入";

	public static final String IMPORT_YINBAO = "银保导入";

	public static final String IMPORT_DIANSHANG = "电商导入";

	public static final String IMPORT_SETTLE_INFO="结算导入";

	public static final Integer EXCEL_START_ROW_INDEX=2;

	public static final String IMPORT_BY_EXCEL="excel";
	public static final String IMPORT_BY_PAGE="page";

	/**
	 * 是否默认主题色
	 */
	//是
	public static final String DEF_THEM_COLOR_YES="1";
	//否
	public static final String DEF_THEM_COLOR_NO="0";

	/**费率结算  审核状态 1-审核中 2-审核通过 3-审核失败*/
	public static final Integer SETT_AUDIT_STATE =1;
	public static final Integer SETT_AUDIT_STATE_PASS =2;

	public static final Map<String,String> MANAGE_COM = new ImmutableMap.Builder<String,String>()
			.put("北京分公司","8611")
			.put("上海分公司","8631")
			.put("河南分公司","8641")
			.put("江苏分公司","8632")
			.put("陕西分公司","8661")
			.build();

	public static final Table<String,String,String> MANAGECOM_CONSTANTS = new ImmutableTable.Builder<String,String,String>()
			.put("managecom","8611","北京分公司")
			.put("managecom","8631","上海分公司")
			.put("managecom","8641","河南分公司")
			.put("managecom","8632","江苏分公司")
			.put("managecom","8661","陕西分公司")
			.build();

	public static final String UUID = "uuid";
	public static final String IMG_VERIFICATION_CODE = "IMG_VERIFICATION_CODE";
	public static final String IMG_VERIFICATION_COUNT = "IMG_VERIFICATION_COUNT";
	//图形验证码时间
	public static final Integer VERIFY_CODE = 5;
	//图形验证码最多输错5次
	public static final Integer VERIFY_CODE_COUNT = 5;
	//失败
	public static final String COMMON_ERROR_CODE = "-1";
	public static final String LOGIN_ERROR_MSG = "登陆失败";
	public static final String IMGVERIFYCODE_WRITE_ERROR = "-4";
	public static final String IMGVERIFYCODE_WRITE_ERROR_MSG = "图片验证码输入有误";
	public static final String IMGVERIFYCODE_FREQUENCY_ERROR = "-5";
	public static final String IMGVERIFYCODE_FREQUENCY_ERROR_MSG = "图片验证码输入过于频繁";
	public static final String LOGIN_USER_NOT_EXIST_MSG = "用户名不存在";
	public static final String LOGIN_USER_FREZE_MSG = "密码不正确";
	public static final String COMMON_SUCCESS_CODE = "0";
	public static final String COMMON_SUCCESS_CODE_MSG = "成功";
	public static final String LOGIN_USER_ERROR_MSG = "该账号已冻结";
	public static final String REGISTER_REGISTERED_MSG = "手机号已注册";
	public static final String REGISTER_USERRRROR_MSG = "用户已注册";
	public static final String SENDCODE_EXCEED_MSG = "验证码获取今日已达上限，请明日再试";
	public static final String REGISTER_PASSWORD_BLANK_MSG = "密码为空，请输入密码";
	public static final String CHECKCODE_FREQUENCY_MSG = "验证码输入有误，请重新获取验证码";
	public static final String CHECKCODE_CODE_ERROR_MSG = "验证码不正确，请重新输入";
	public static final String REGISTER_ERROR_MSG = "注册失败";
	public static final String SENDCODE_ERROR_MSG = "发送验证码失败";
	public static final String RESET_PASSWORD_NOT_EXIST_MSG = "手机号未注册，不能重置密码";
	public static final String SENDCODE_SUCCESS_MSG = "发送验证码成功";
	public static final String RESET_PASSWORD_BLANK_MSG = "密码为空，请输入密码";
	public static final String RESET_PASSWORD_ERROR_MSG = "重置密码失败";
	public static final String COMMON_ERROR_CODE_MSG = "失败";

	/**
	 * east2.0 导入的销售人员信息原文件 上传oss的地址
	 */
	public static final String EAST2_ADDR = "settlement/east2/";
	/**
	 * east2.0 导出报送数据 下载地址
	 */
	public static final String EAST2_DOWNDATA_ADDR = "settlement/east2/down/";
	/**
	 * east2.0 页面功能导出报送数据 下载地址
	 */
	public static final String EAST2_PAGEDOWN_ADDR = "settlement/east2/pagedown/";
	/**
	 * 数据类型- 销售人员佣金信息表
	 */
	public static final String EAST2_FILE_TYPE_YJXXB = "YJXXB";
	/**
	 * 数据类型- 销售人员直接佣金信息表
	 */
	public static final String EAST2_FILE_TYPE_ZJYJXXB = "ZJYJXXB";
	/**
	 * 数据类型- 销售人员处罚信息表
	 */
	public static final String EAST2_FILE_TYPE_CFXXB = "CFXXB";
	/**
	 * 数据类型- 销售人员问责信息表
	 */
	public static final String EAST2_FILE_TYPE_WZXXB = "WZXXB";
	/**
	 * 保险机构代码
	 */
	public static final String EAST2_BXJGDM = "000166";
	/**
	 * 保险机构名称
	 */
	public static final String EAST2_BXJGMC = "弘康人寿保险股份有限公司";
	/**
	 * 默认的 货币代码
	 */
	public static final String EAST2_HBDM = "CNY";
	public static final String EAST2_DEFAULT_BDH = "000000";
	/**
	 * 团个性质 - 个人
	 */
	public static final String EAST2_TGXZ_GD = "个人";
	/**
	 * 团个性质 - 团体
	 */
	public static final String EAST2_TGXZ_TT = "团体";
	/**
	 * 导出数据类型-中介机构手续费
	 */
	public static final String EAST2_TYPE_COMCHARGE = "comCharge";
	/**
	 * 导出数据类型-中介机构信息表
	 */
	public static final String EAST2_TYPE_COM = "com";
	/**
	 * 导出数据类型-中介机构保单手续费
	 */
	public static final String EAST2_TYPE_COMMITION = "commission";
	/**
	 * 导出数据类型-销售人员信息
	 */
	public static final String EAST2_TYPE_AGENT = "agent";
	/**
	 * 导出数据类型-销售人员佣金信息
	 */
	public static final String EAST2_TYPE_XSRYYJ = "xsryyj";
	/**
	 * 导出数据类型-销售人员直接佣金信息
	 */
	public static final String EAST2_TYPE_XSRYZJYJ = "xsryzjyj";
	/**
	 * east2 报送业务类型 码表名称
	 */
	public static final String EAST2_BUSINESS_TYPE = "east2_business_type";
	/**
	 * east2 excel操作日志类型- 导入
	 */
	public static final String EAST2_LOGTYPE_IMPORT = "import";
	/**
	 * east2 excel操作日志类型- 导出
	 */
	public static final String EAST2_LOGTYPE_EXPORT = "export";

	/**
	 * 费率录入rate_type_param：沉淀量算法规则[ D日沉淀量佣金=(D-1)日产品保单价值-(D-1)日累计净贷款金额]*费率/天数
	 */
	public static final String STOCK_NETVALUE = "netValue";
	/**
	 * 费率录入rate_type_param：沉淀量算法规则[D日沉淀量佣金=(D-1)日保单账户价值]*费率/天数
	 */
	public static final String STOCK_INSUVALUE = "insuranceValue";
	/**
	 * 费控用户部门名称前缀 - 弘康
	 */
	public static final String FKUSERDEPT_HK = "弘康";
	/**
	 * 费控支付结果- 未付款
	 */
	public static final String FKPAYSTATUS_NOPAY = "0";
	/**
	 * 费控支付结果-付款成功
	 */
	public static final String FKPAYSTATUS_SUCC = "1";
}
