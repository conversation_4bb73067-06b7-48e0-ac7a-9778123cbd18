package com.huida.platform.settlement.event;

import lombok.Data;
import org.springframework.context.ApplicationEvent;

/**
 * 进度更新事件
 * 专门用于处理导出进度更新，避免与日志记录事件混淆
 * 
 * <AUTHOR>
 */
@Data
public class ProgressUpdateEvent extends ApplicationEvent {
    
    private String unionId;
    private int progress;
    private int processedCount;
    private int totalCount;
    private String dataType;
    private String message;
    
    public ProgressUpdateEvent(Object source, String unionId, int progress, int processedCount, int totalCount, String dataType) {
        super(source);
        this.unionId = unionId;
        this.progress = progress;
        this.processedCount = processedCount;
        this.totalCount = totalCount;
        this.dataType = dataType;
        this.message = String.format("进度更新：%d/%d (%d%%)", processedCount, totalCount, progress);
    }
    
    public ProgressUpdateEvent(Object source, String unionId, int progress, int processedCount, int totalCount, String dataType, String message) {
        super(source);
        this.unionId = unionId;
        this.progress = progress;
        this.processedCount = processedCount;
        this.totalCount = totalCount;
        this.dataType = dataType;
        this.message = message;
    }
}
