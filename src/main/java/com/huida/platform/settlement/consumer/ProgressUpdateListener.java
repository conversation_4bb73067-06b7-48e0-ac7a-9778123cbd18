package com.huida.platform.settlement.consumer;

import com.huida.platform.settlement.event.ProgressUpdateEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 进度更新事件监听器
 * 专门处理导出进度更新事件，提供以下功能：
 * 1. 记录进度日志
 * 2. 缓存进度信息到Redis（供前端查询）
 * 3. 可扩展：WebSocket推送、数据库记录等
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ProgressUpdateListener implements ApplicationListener<ProgressUpdateEvent> {
    
    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String PROGRESS_KEY_PREFIX = "export:progress:";
    private static final int PROGRESS_EXPIRE_HOURS = 24; // 进度信息保存24小时
    
    @Override
    public void onApplicationEvent(ProgressUpdateEvent event) {
        try {
            // 1. 记录进度日志
            logProgress(event);
            
            // 2. 缓存进度信息到Redis
            cacheProgress(event);
            
            // 3. 特殊进度处理
            handleSpecialProgress(event);
            
        } catch (Exception e) {
            log.error("处理进度更新事件失败 - unionId: {}, 错误: {}", event.getUnionId(), e.getMessage(), e);
        }
    }
    
    /**
     * 记录进度日志
     */
    private void logProgress(ProgressUpdateEvent event) {
        if (event.getProgress() % 10 == 0 || event.getProgress() >= 95) {
            // 每10%或接近完成时记录详细日志
            log.info("导出进度更新 - unionId: {}, 类型: {}, 进度: {}%, 已处理: {}/{}, 消息: {}", 
                    event.getUnionId(), 
                    event.getDataType(),
                    event.getProgress(), 
                    event.getProcessedCount(), 
                    event.getTotalCount(),
                    event.getMessage());
        } else {
            // 其他时候记录简单日志
            log.debug("导出进度 - unionId: {}, 进度: {}%", event.getUnionId(), event.getProgress());
        }
    }
    
    /**
     * 缓存进度信息到Redis
     */
    private void cacheProgress(ProgressUpdateEvent event) {
        if (redisTemplate == null) {
            return;
        }
        
        try {
            String key = PROGRESS_KEY_PREFIX + event.getUnionId();
            ProgressInfo progressInfo = new ProgressInfo(
                    event.getUnionId(),
                    event.getDataType(),
                    event.getProgress(),
                    event.getProcessedCount(),
                    event.getTotalCount(),
                    event.getMessage(),
                    System.currentTimeMillis()
            );
            
            redisTemplate.opsForValue().set(key, progressInfo, PROGRESS_EXPIRE_HOURS, TimeUnit.HOURS);
            
        } catch (Exception e) {
            log.warn("缓存进度信息失败 - unionId: {}, 错误: {}", event.getUnionId(), e.getMessage());
        }
    }
    
    /**
     * 处理特殊进度
     */
    private void handleSpecialProgress(ProgressUpdateEvent event) {
        // 完成时的特殊处理
        if (event.getProgress() >= 100) {
            log.info("导出任务完成 - unionId: {}, 类型: {}, 总计处理: {}条", 
                    event.getUnionId(), event.getDataType(), event.getTotalCount());
            
            // 可以在这里添加：
            // - 发送完成通知
            // - 清理临时数据
            // - 统计信息记录等
        }
        
        // 错误处理（如果进度为负数表示错误）
        if (event.getProgress() < 0) {
            log.error("导出任务异常 - unionId: {}, 类型: {}, 错误信息: {}", 
                    event.getUnionId(), event.getDataType(), event.getMessage());
        }
    }
    
    /**
     * 进度信息数据结构
     */
    public static class ProgressInfo {
        private String unionId;
        private String dataType;
        private int progress;
        private int processedCount;
        private int totalCount;
        private String message;
        private long timestamp;
        
        public ProgressInfo() {}
        
        public ProgressInfo(String unionId, String dataType, int progress, int processedCount, 
                           int totalCount, String message, long timestamp) {
            this.unionId = unionId;
            this.dataType = dataType;
            this.progress = progress;
            this.processedCount = processedCount;
            this.totalCount = totalCount;
            this.message = message;
            this.timestamp = timestamp;
        }
        
        // Getters and Setters
        public String getUnionId() { return unionId; }
        public void setUnionId(String unionId) { this.unionId = unionId; }
        
        public String getDataType() { return dataType; }
        public void setDataType(String dataType) { this.dataType = dataType; }
        
        public int getProgress() { return progress; }
        public void setProgress(int progress) { this.progress = progress; }
        
        public int getProcessedCount() { return processedCount; }
        public void setProcessedCount(int processedCount) { this.processedCount = processedCount; }
        
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    }
}
