package com.huida.platform.settlement.consumer;

import java.util.Date;
import java.util.Map;
import java.util.Optional;

import com.huida.platform.settlement.dto.settlement.rylcount.request.SettCountExportReq;
import com.huida.platform.settlement.entity.settlement.RylExportReportCommonLogEntity;
import com.huida.platform.settlement.event.SendMessageEvent;
import com.huida.platform.settlement.mapper.settlement.RylExportReportCommonMapper;
import com.huida.platform.settlement.util.DynaFormParseUtil;
import jodd.util.StringUtil;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;


import lombok.extern.slf4j.Slf4j;

/**
 * 导出报表日志记录监听器
 * 专门处理导出任务的开始、结束、错误等日志记录
 * 不处理进度更新事件（由ProgressUpdateListener处理）
 * <AUTHOR>
 */
@Slf4j
@Component
public class ExportReportLogListener implements ApplicationListener <SendMessageEvent> {

    @Autowired
    private RylExportReportCommonMapper rylExportReportCommonMapper;

    @Override
    public void onApplicationEvent(SendMessageEvent sendMessageEvent) {
        Map<String, String> messageEventSource = (Map<String, String>) sendMessageEvent.getSource();
        if(MapUtils.isNotEmpty(messageEventSource)){
            log.info("监听到导出报表请求消息message={}",messageEventSource);
            messageEventSource.get("FileUploadOutDto");
            Optional<SettCountExportReq> queryParamOpt = Optional.ofNullable(messageEventSource.get("queryParam")).map(obj -> JSONObject.parseObject(obj, SettCountExportReq.class));

            // 检查是否是进度更新事件（operate=1表示进度更新）
            if (sendMessageEvent.getOperate() == 1) {
                log.debug("收到进度更新事件，跳过日志记录：{}", messageEventSource);
                return;
            }

            // 校验必要字段（修复逻辑：任何一个条件为true就返回）
            if(!Optional.ofNullable(messageEventSource.get("threadId")).isPresent()
                    || !StringUtil.isNotEmpty(sendMessageEvent.getDataType())
                    || !queryParamOpt.isPresent()){
                log.error("未获取到关键信息,无法进行日志处理 message==>{}",sendMessageEvent);
                return;
            }
            SettCountExportReq settCountExportReq = queryParamOpt.get();
            RylExportReportCommonLogEntity reportLog = new RylExportReportCommonLogEntity();
            reportLog.setDataType(sendMessageEvent.getDataType());
            reportLog.setQueryContent(messageEventSource.get("queryParam"));
            reportLog.setCreateTime(new Date(sendMessageEvent.getTimestamp()));
            reportLog.setOperate(sendMessageEvent.getOperate());
            reportLog.setThreadId(Long.valueOf(messageEventSource.get("threadId")));
            reportLog.setUserId(settCountExportReq.getUserId());
            reportLog.setFileName(messageEventSource.get("fileName"));
            reportLog.setUnionId(sendMessageEvent.getUnionId());
            rylExportReportCommonMapper.insertRylExportReportCommonLog(reportLog);
        }
    }
}
