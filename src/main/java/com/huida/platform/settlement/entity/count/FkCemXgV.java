package com.huida.platform.settlement.entity.count;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 费控 视图 实体
 * <AUTHOR>
 */
@Data
@TableName("CEM_XG_V")
public class FkCemXgV implements Serializable {

    /**
     * 费控单据编号
     */
    private String billNo;
    /**
     * 付款状态： 0-未付款、2-付款失败(9-失败 8-退票)、1-付款成功
     */
    private String payStatus;
    /**
     * 财务确认日期
     */
    private String accountingDate;
    /**
     * 收款人银行账户名称
     */
    private String getName;
    /**
     * 收款人银行账号
     */
    private String getAccount;
    /**
     * 收款人开户银行名称
     */
    private String getBankBranch;
    /**
     * 实际到账日期
     */
    private String payMadeDate;
    /**
     * 实际到账日期
     */
    private String originalCurrencySum;
    /**
     * 审批状态 (跟支付结果同时获取,不再单独用AsynFkSettBillStatusHandler)
     */
    private Integer billStatus;
}
