package com.huida.platform.settlement.service.settlement;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.huida.platform.common.msg.BaseResponse;
import com.huida.platform.settlement.dto.CountSettlementMrmorandumDTO;
import com.huida.platform.settlement.dto.RylCountCommissionDTO;
import com.huida.platform.settlement.dto.RylCountReportDto;
import com.huida.platform.settlement.dto.RylCountStockSettDTO;
import com.huida.platform.settlement.dto.settlement.rylcount.request.ExceptionReq;
import com.huida.platform.settlement.dto.settlement.rylcount.request.SettCountExportReq;
import com.huida.platform.settlement.dto.settlement.rylcount.response.SettChnnelResultExportVo;
import com.huida.platform.settlement.dto.settlement.rylcount.response.SettCountResultExportVo;
import com.huida.platform.settlement.entity.RylCountManageReportLogEntity;
import com.huida.platform.settlement.entity.RylCountQualityControlLogEntity;
import com.huida.platform.settlement.entity.RylCountSettlementMemorandumEntity;
import com.huida.platform.settlement.entity.settlement.RylExportReportCommonLogEntity;
import org.springframework.stereotype.Repository;
import org.springframework.web.multipart.MultipartFile;

/**
 * "结算中心"菜单 下 功能
 * <AUTHOR>
 */
@Repository
public interface RylCountSettService {
    /**
     * "结算中心"菜单--》手续费 查询
     * @param settCountExportReq
     * @return
     */
    BaseResponse rhSettCountResultList(SettCountExportReq settCountExportReq);

    /**
     * "结算中心"菜单--》手续费 导出
     * @param settCountExportReq
     */
    void handlerExportData(SettCountExportReq settCountExportReq);

    /**
     * "结算中心"菜单--》手续费导出 队列查看
     * @param settCountExportReq
     * @return
     */
    IPage<RylExportReportCommonLogEntity> getExportList(SettCountExportReq settCountExportReq);

    /**
     * "结算中心"菜单--》手续费导出 oss下载
     * @param id
     * @return
     */
    String exportDownLoad(String id);
    /**
     * "结算中心"菜单--》手续费导出 取消下载
     * @param id
     * @return
     */
    Boolean cancelDownLoad(String id);

    /**
     * "结算中心"菜单--》手续费导出队列 查询详情
     * @param id
     * @return
     */
    SettCountResultExportVo exportList(String id);

    /**
     * 渠道结算数据导出
     * @param settChnnelResultExportVo
     */
    void channelResultExportExcel(SettChnnelResultExportVo settChnnelResultExportVo);

    /**
     * 渠道结算数据导出队列查看
     * @param settChnnelResultExportVo
     * @return
     */
    IPage<RylExportReportCommonLogEntity> exportResultList(SettChnnelResultExportVo settChnnelResultExportVo);

    /**
     * 渠道结算数据导出队列查看透出容易连
     *
     * @param settChnnelResultExportVo
     * @return
     */
    IPage<RylExportReportCommonLogEntity> exportResultRylList(SettChnnelResultExportVo settChnnelResultExportVo);

    /**
     * 管理报表导出
     *
     * @param settChnnelResultExportVo
     */
    void manageExportExcel(SettChnnelResultExportVo settChnnelResultExportVo);

    /**
     * 管理报表导出队列查看
     *
     * @param settChnnelResultExportVo
     * @return
     */
    IPage<RylExportReportCommonLogEntity> exportList(SettChnnelResultExportVo settChnnelResultExportVo);

    /**
     * 管理报表-品控报表导出
     *
     * @param settChnnelResultExportVo
     */
    void qualityControlExport(SettChnnelResultExportVo settChnnelResultExportVo);

    /**
     * 全量渠道结算数据导出
     *
     * @param settChnnelResultExportVo
     */
    void allChannelResultExportExcel(SettChnnelResultExportVo settChnnelResultExportVo);

    /***
     * 报送备忘录查询
     * @param countSettlementMrmorandumDTO
     * @return
     */
    BaseResponse queryMemorandum(CountSettlementMrmorandumDTO countSettlementMrmorandumDTO);

    /***
     * 报送备忘录更新
     * @param rylCountSettlementMemorandumEntity
     * @return
     */
    BaseResponse updateMemorandum(RylCountSettlementMemorandumEntity rylCountSettlementMemorandumEntity);

    /***
     * 报送备忘录新增
     * @param rylCountSettlementMemorandumEntity
     * @return
     */
    BaseResponse addMemorandum(RylCountSettlementMemorandumEntity rylCountSettlementMemorandumEntity) throws Exception;

    /***
     * 报送备忘录上传文件
     * @param file
     * @return
     */
    BaseResponse uploadFile(MultipartFile file) throws Exception;

    /***
     * 报送备忘录下载文件
     * @param rylExportReportCommonLogEntity
     * @return
     */
    BaseResponse downloadFile(RylExportReportCommonLogEntity rylExportReportCommonLogEntity) throws Exception;

    /**
     * 退保率报表导出
     *
     * @param settChnnelResultExportVo
     */
    void exportCancelRate(SettChnnelResultExportVo settChnnelResultExportVo);

    /***
     * 结算单上传文件接口
     * @param file1
     * @return
     */
    BaseResponse uploadContnoFile(MultipartFile file1,MultipartFile file2,MultipartFile file3,MultipartFile file4,MultipartFile file5) throws Exception;

    BaseResponse queryExceptionData(ExceptionReq exceptionReq);

    /**
     * 可结未付报表导出
     *
     * @param rylCountReportDto
     */
    void exportUnsettledAmount(RylCountReportDto rylCountReportDto);

    /**
     * 可结未付明细报表导出
     *
     * @param rylCountReportDto
     */
    void exportUnsettledDetail(RylCountReportDto rylCountReportDto);

    /**
     * 新结算进度报表导出
     *
     * @param rylCountReportDto
     */
    void exportNewSchedule(RylCountReportDto rylCountReportDto);

    /**
     * 可结未付报表明细查询
     *
     * @param rylCountReportDto
     */
    BaseResponse queryUnsettledDetail(RylCountReportDto rylCountReportDto);

    /**
     * 获取所有的结算负责人
     */
    BaseResponse queryAllOperator();

    /**
     * 借款与核销报表导出
     *
     * @param rylCountReportDto 导出参数
     * @return 导出任务ID或状态信息
     */
    String settBillPay(RylCountReportDto rylCountReportDto);

    /**
     * 新结算进度报表导出
     *
     * @param rylCountReportDto
     */
    void exportChannelRate(RylCountReportDto rylCountReportDto);

    /**
     * 手续费追佣预测报表导出
     *
     * @param rylCountReportDto
     */
    void exportForecastCommission(RylCountReportDto rylCountReportDto);

    /**
     * 非弘结算数据报表导出
     *
     * @param rylCountReportDto
     */
    void fhSettExport(RylCountReportDto rylCountReportDto);

    /**
     * 手续费已付未付报表导出
     *
     * @param rylCountCommissionDTO
     */
    void commissionPaidAndUnpaidExport(RylCountCommissionDTO rylCountCommissionDTO);

    /**
     * 手续费经营分析报表导出
     *
     * @param rylCountStockSettDTO
     */
    void commissionBusiAnalysisExport(RylCountStockSettDTO rylCountStockSettDTO);

    /**
     * 投连存量明细报表导出
     *
     * @param rylCountStockSettDTO
     */
    void investmentLinkStockExport(RylCountStockSettDTO rylCountStockSettDTO);
}
