package com.huida.platform.settlement.service.settlement.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.merge.OnceAbsoluteMergeStrategy;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.huida.platform.common.msg.BaseResponse;
import com.huida.platform.common.util.ObjConverUtil;
import com.huida.platform.common.util.OssFileUtil;
import com.huida.platform.settlement.constant.CommonConstant;
import com.huida.platform.settlement.constant.FileUploadAddrConstant;
import com.huida.platform.settlement.constant.SettlementConstant;
import com.huida.platform.settlement.dto.*;
import com.huida.platform.settlement.dto.protocol.FileUploadOutDto;
import com.huida.platform.settlement.dto.reconciliationpay.ChannelDTO;
import com.huida.platform.settlement.dto.reconciliationpay.count.BillPayStatementDto;
import com.huida.platform.settlement.dto.reconciliationpay.count.FkBillPayDto;
import com.huida.platform.settlement.dto.reconciliationpay.count.FkBillPayReq;
import com.huida.platform.settlement.dto.settlement.rylcount.request.ExceptionReq;
import com.huida.platform.settlement.dto.settlement.rylcount.request.SettCountExportReq;
import com.huida.platform.settlement.dto.settlement.rylcount.response.*;
import com.huida.platform.settlement.entity.*;
import com.huida.platform.settlement.entity.count.RylCountSettlementBillMappingCfgEntity;
import com.huida.platform.settlement.entity.countpay.*;
import com.huida.platform.settlement.entity.settlement.*;
import com.huida.platform.settlement.event.SendMessageEvent;
import com.huida.platform.settlement.mapper.*;
import com.huida.platform.settlement.mapper.reconciliationpay.*;
import com.huida.platform.settlement.mapper.settlement.RylCountContinueRateMapper;
import com.huida.platform.settlement.mapper.settlement.RylCountSettResultMapper;
import com.huida.platform.settlement.mapper.settlement.RylCountSettResultVitalMapper;
import com.huida.platform.settlement.mapper.settlement.RylCountSettlementChannelLabMapper;
import com.huida.platform.settlement.mapper.settlement.RylCountSettlementHkrhResultMapper;
import com.huida.platform.settlement.mapper.settlement.RylCountSettlementInitMapper;
import com.huida.platform.settlement.mapper.settlement.RylCountSettlementRateLevelMapper;
import com.huida.platform.settlement.mapper.settlement.RylCountSettlementRateMapper;
import com.huida.platform.settlement.mapper.settlement.RylDictCfgMapper;
import com.huida.platform.settlement.mapper.settlement.RylExportReportCommonMapper;
import com.huida.platform.settlement.service.settlement.RylCountSettService;
import com.huida.platform.settlement.service.settlement.RylCountSettUtilsService;
import com.huida.platform.settlement.service.settlement.TaskThreadQuery;
import com.huida.platform.settlement.util.BusiUtils;
import com.huida.platform.settlement.util.DynaFormParseUtil;
import com.huida.platform.settlement.util.FileUpoadUtil;
import com.huida.platform.settlement.util.SettlementUtil;
import jodd.util.StringUtil;
import jodd.util.concurrent.ThreadFactoryBuilder;
import lombok.Cleanup;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import java.awt.Color;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.slf4j.MDC;
import com.huida.platform.settlement.util.ApiLogUtil;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.*;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 结算中心--》手续费导出
 * <AUTHOR>
 */
@Service
@Slf4j
@SuppressWarnings("all")
public class RylCountSettServiceImpl extends ServiceImpl<RylCountSettResultMapper, RylCountSettlementResult> implements RylCountSettService {
    @Autowired
    private RylCountSettResultMapper rylCountSettResultMapper;
    @Autowired
    RylCountSettlementRateMapper rylCountSettlementRateMapper;
    @Autowired
    RylCountSettlementChannelCfgMapper rylCountSettlementChannelCfgMapper;

    public static final String PAY_MODE_9 = "9";
    public static final String PAY_MODE_B = "b";
    public static final String PAY_MODE_C = "c";

    @Autowired
    private RylCountSettUtilsService rylCountSettUtilsService;

    @Value("${imagesOSSUrlChange}")
    private String imagesOSSUrlChange;
    @Value("${imagesPortCatalogChange}")
    private String imagesPortCatalogChange;
    @Value("${pageMaxNum.limit}")
    Integer pageMaxNum;
    @Value("${manageReportOssUrl}")
    private String manageReportOssUrl;
    @Value("${qualityControlOssUrl}")
    private String qualityControlOssUrl;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private FileUpoadUtil fileUpoadUtil;
    @Autowired
    private OssFileUtil ossFileUtil;
    @Autowired
    private RylExportReportCommonMapper exportReportCommonMapper;
    @Autowired
    private RylCountManageReportLogMapper rylCountManageReportLogMapper;
    @Autowired
    private RylCountQualityControlLogMapper rylCountQualityControlLogMapper;
    @Autowired
    private TCountExceptionDataLogMapper countExceptionDataLogMapper;


    private static ThreadFactory settThreadFactory = new ThreadFactoryBuilder()
            .setNameFormat("sett-channelReport-%d").get();

    private static final ExecutorService CHANNEL_REPORT_THREAD_EXECUTOR = new ThreadPoolExecutor(
            5,
            5,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>(), settThreadFactory);

    public static final String ALLCHANNEL_REPORT_REPROT = "allChannelResuleExport";
    public static final String SETT_RESULT_REPROT = "settResultExport";
    public static final String CONTNUE_RATE_REPROT_ADDR = "count/settResultExport/";
    public static final String SETT_CHANNEL_RESULT_REPROT = "settChannelResultExport";
    public static final String MANAGE_REPORT_REPROT = "manageRrportExport";
    public static final String QUALITY_CONTROL_REPORT_REPROT = "qualityControlRrportExport";
    public static final String SETT_BILL_PAY_REPORT_REPROT = "settBillPayReportExport";
    public static final String FH_SETT_REPROT = "fhSettExport";
    @Autowired
    RylCountSettlementMemorandumMapper rylCountSettlementMemorandumMapper;
    /**
     * 报送备忘录上传文件oss地址
     */
    public static final String COUNT_SETT_MEMORANDUM_ADDR = "count/memorandum/";
    @Autowired
    RylCountSlowKnotLogMapper rylCountSlowKnotLogMapper;
    @Autowired
    RylCountSettlementChannelMappingCfgMapper rylCountSettlementChannelMappingCfgMapper;
    @Autowired
    RylCountCancelRateMapper rylCountCancelRateMapper;
    @Autowired
    RylCountSettlementInitMapper rylCountSettlementInitMapper;
    @Autowired
    RylCountContinueRateMapper rylCountContinueRateMapper;
    @Value("${cancelRateReportOssUrl}")
    private String cancelRateReportOssUrl;
    public static final String CANCEL_RATE_REPORT_REPROT = "cancelRateReportExport";
    @Autowired
    RylCountCancelBaseDataMapper rylCountCancelBaseDataMapper;
    @Autowired
    private RylCountUnsettledAmountLogMapper rylCountUnsettledAmountLogMapper;
    public static final String UNSETTLED_REPORT_REPROT = "unsettledRrportExport";
    public static final String UNSETTLED_DETAIL_REPORT_REPROT = "unsettledDetailRrportExport";
    private Map<String, String> riskMap = new HashMap<>();
    private Map<String, String> channelMap = new HashMap<>();
    @Autowired
    private RylCountSettNewScheduleMapper rylCountSettNewScheduleMapper;
    public static final String NEWSCHEDULE_REPORT_REPROT = "newScheduleRrportExport";
    @Autowired
    private RylCountSettlementChannelLabMapper rylCountSettlementChannelLabMapper;
    @Autowired
    private RylCountSettlementBillPayMapper rylCountSettlementBillPayMapper;
    @Autowired
    private RestTemplate externalRestTemplate;

    @Value("${fk.hk.domain}")
    private String fkHkDns;
    @Value("${fk.rh.domain}")
    private String fkrhDns;
    @Value("${fkHxBillNoUrl}")
    private String fkHxBillNoUrl;
    @Value("${fkJkBillNoUrl}")
    private String fkJkBillNoUrl;
    public static final String CHANNEL_RATE_DETAIL_REPROT = "channelRateDetailExport";
    @Autowired
    private RylCountSettlementRateLevelMapper rylCountSettlementRateLevelMapper;
    public static final String FORECAST_COMMISSION_REPROT = "forecastCommissionExport";
    @Autowired
    private RylCountForecastResultMapper rylCountForecastResultMapper;
    @Autowired
    private RylCountCommissionForecastMapper rylCountCommissionForecastMapper;
    @Autowired
    private RylCountSettlementFHUploadPayMapper fhUploadPayMapper;
    public static final String COMMISSION_PAIDANDUNPAID_REPROT = "commissionPaidAndUnpaidExport";
    @Autowired
    private RylCountSettResultVitalMapper rylCountSettResultVitalMapper;
    @Autowired
    private RylCountSettlementHkrhResultMapper rylCountSettlementHkrhResultMapper;
    @Autowired
    private RylCountSettlementUploadVitalMapper rylCountSettlementUploadVitalMapper;
    @Autowired
    private RylDictCfgMapper rylDictCfgMapper;
    public static final String COMMISSION_BUSI_ANALYSIS_REPROT = "commissionBusiAnalysisExport";
    public static final String INVESTMENT_LINK_STOCK_REPROT = "investmentLinkStockExport";
    @Autowired
    private RylCountChargeBusinessAnalysisMapper rylCountChargeBusinessAnalysisMapper;
    @Autowired
    private RylCountSettlementBillMappingCfgMapper rylCountSettlementBillMappingCfgMapper;

    @Override
    public BaseResponse rhSettCountResultList(SettCountExportReq settCountResultReq) {
        long time = System.currentTimeMillis();
        SettCountResultRes settCountResultRes = new SettCountResultRes();
        Map<String, String> channelMap = Maps.newHashMap();
        List<String> channelCodeList = new ArrayList<>();
        try {
            channelCodeList = handlerQueryParams(settCountResultReq, channelCodeList,channelMap);
            //条件查询的总数
            int total  = rylCountSettResultMapper.selectTotalNum(settCountResultReq,channelCodeList);
            log.info("结算中心--》手续费查询明细总条数结束,耗时:{}",System.currentTimeMillis() - time);
            //自定义每次查询的行数
            int rows = settCountResultReq.getSize();
            int offset = settCountResultReq.getCurrent();
            //计算遍历的次数
            int allpage = (total-1) / rows + 1;
            List<SettCountResultExportDto> list = rylCountSettUtilsService.queryByPage(channelCodeList,settCountResultReq,offset,rows);

            List<RiskDTO> riskList = rylCountSettlementRateMapper.getAllRisk();
            Map<String,String> riskMap = riskList.stream().collect(Collectors.toMap(RiskDTO::getRiskCode, RiskDTO::getRiskName));

            list.stream().forEach(x->{
                x.setMainRiskName(riskMap.get(x.getMainRiskName()));
                x.setRiskName(riskMap.get(x.getRiskName()));
                x.setChannelName(channelMap.get(settCountResultReq.getChannelCode()));
                if (x.getMioAmnt().compareTo(BigDecimal.ZERO) < 0) {
                    if (PAY_MODE_9.equals(x.getPayMode())) {
                        x.setPayRole("渠道");
                    } else {
                        x.setPayRole("弘康");
                    }

                } else {
                    if (PAY_MODE_B.equals(x.getPayMode()) || PAY_MODE_C.equals(x.getPayMode()) || PAY_MODE_9.equals(x.getPayMode())) {
                        x.setPayRole("渠道");
                    } else {
                        x.setPayRole("弘康");
                    }
                }
            });
            log.info("结算中心--手续费查询,耗时:{}",System.currentTimeMillis() - time);
            settCountResultRes.setCurrent(settCountResultReq.getCurrent());
            settCountResultRes.setPages(allpage);
            settCountResultRes.setTotal(total);
            settCountResultRes.setSize(rows);
            settCountResultRes.setRecords(list);
        }catch (Exception e){
            log.error("结算中心--手续费查询,异常，原因：{}",e);
        }
        return BaseResponse.success(settCountResultRes);
    }

    @Override
    public void handlerExportData(SettCountExportReq settCountExportReq) {
        Map<String, String> channelMap = Maps.newHashMap();
        Map<String, String> message = Maps.newHashMap();
        List<String> channelCodeList = new ArrayList<>();
        message.put("queryParam", JSONObject.toJSONString(settCountExportReq));
        FileUploadOutDto fileUploadOutDto = null;
        List<RiskDTO> rylContRiskCodeEntities = rylCountSettlementRateMapper.getAllRisk();
        Map<String, String> riskMap = rylContRiskCodeEntities.stream().collect(Collectors.toMap(RiskDTO::getRiskCode, RiskDTO::getRiskName));
        ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
        if(CollectionUtils.isNotEmpty(settCountExportReq.getResultId())){
            //不展示查询，也不用按id导出了
        }else{
            channelCodeList = handlerQueryParams(settCountExportReq, channelCodeList,channelMap);
            List<String> finalChannelCodeList = channelCodeList;
            //条件查询导出
            singleThreadExecutor.execute(new Runnable() {
                @Override
                public void run() {
                    exportDataByConditions(settCountExportReq, finalChannelCodeList,fileUploadOutDto,channelMap,riskMap,message);
                }
            });

        }
        singleThreadExecutor.shutdown();
    }

    @Override
    public IPage<RylExportReportCommonLogEntity> getExportList(SettCountExportReq settCountExportReq) {
        Long userId = settCountExportReq.getUserId();
        QueryWrapper<RylExportReportCommonLogEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(RylExportReportCommonLogEntity::getUserId,userId);
        queryWrapper.lambda().eq(RylExportReportCommonLogEntity::getDataType,SETT_RESULT_REPROT);
        IPage<RylExportReportCommonLogEntity> page = new Page<>(settCountExportReq.getCurrent(),settCountExportReq.getSize());
        List<RylExportReportCommonLogEntity> list = exportReportCommonMapper.selectPageByUserId(page, queryWrapper);
        list.forEach(commonLog->{
            JSONObject content = DynaFormParseUtil.convertObj(commonLog.getQueryContent());
            commonLog.setQueryContent(content);
        });
        return page.setRecords(list);
    }

    @Override
    public String exportDownLoad(String id) {
        RylExportReportCommonLogEntity reportCommonLogEntity = exportReportCommonMapper.selectById(id);
        String targetPath = StrUtil.join( reportCommonLogEntity.getFileKey());
        //上传 文件路径/fileKey
        String targetfilePath = CONTNUE_RATE_REPROT_ADDR.concat( reportCommonLogEntity.getFileKey());
        String fileName = reportCommonLogEntity.getFileName();

        String urlPrivate = ossFileUtil.getDownLoadUrlPrivate(5, targetfilePath,fileName);
        //转化为测试环境URL
        return  fileUpoadUtil.replaceDomainAndPort(imagesOSSUrlChange, imagesPortCatalogChange, urlPrivate);
    }

    @Override
    public Boolean cancelDownLoad(String id) {
        RylExportReportCommonLogEntity reportLog = exportReportCommonMapper.selectById(id);
        Integer status = reportLog.getStatus();
        Long threadId = reportLog.getThreadId();
        boolean isCancel = false;
        if(1 == status){
            Set<Thread> threads = Thread.currentThread().getAllStackTraces().keySet();
            for (Thread thread : threads) {
                if(thread.getId()==threadId){
                    //确认任务状态
                    reportLog = exportReportCommonMapper.selectById(id);
                    if(1 == reportLog.getStatus() && 1!=reportLog.getOperate() && thread.isAlive() && !thread.isInterrupted()) {
                        try {
                            thread.interrupt();
                            isCancel =thread.isInterrupted();
                        }catch (Exception e){
                            log.warn("承保清单导出任务被中止==>{}",reportLog);
                        }
                    }
                }
            }
        }
        RylExportReportCommonLogEntity reportCommonLogEntity = new RylExportReportCommonLogEntity();
        reportCommonLogEntity.setId(reportLog.getId());
        reportCommonLogEntity.setOperate(1);
        reportCommonLogEntity.setStatus(2);
        exportReportCommonMapper.updateById(reportCommonLogEntity);
        return isCancel;
    }

    @Override
    public SettCountResultExportVo exportList(String id) {
        RylExportReportCommonLogEntity reportLog = exportReportCommonMapper.selectById(id);
        JSONObject jsonObject = DynaFormParseUtil.convertObj(reportLog.getQueryContent());
        String json = DynaFormParseUtil.toJson(jsonObject);
        SettCountExportReq settCountExportReq  = JSONObject.parseObject(json, SettCountExportReq.class);
        Map<String, Object> map = Maps.newHashMap();
        map.put("is_del",0);
        map.put("channel_code",settCountExportReq.getChannelCode());
        Optional<RylCountSettlementChannelCfgEntity> channel = rylCountSettlementChannelCfgMapper.selectByMap(map).stream().findFirst();
        SettCountResultExportVo resultExportVo = ObjConverUtil.copy(settCountExportReq,SettCountResultExportVo.class);
        resultExportVo.setChannelName(channel.isPresent()?channel.map(RylCountSettlementChannelCfgEntity::getChannelName).get():"");
        return resultExportVo;
    }

    @Override
    public void channelResultExportExcel(SettChnnelResultExportVo settChnnelResultExportVo) {
        Map<String, String> channelMap = Maps.newHashMap();
        Map<String, String> message = Maps.newHashMap();
        List<String> channelCodeList = new ArrayList<>();
        message.put("queryParam", JSONObject.toJSONString(settChnnelResultExportVo));
        FileUploadOutDto fileUploadOutDto = null;
        List<RiskDTO> rylContRiskCodeEntities = rylCountSettlementRateMapper.getAllRisk();
        Map<String, String> riskMap = rylContRiskCodeEntities.stream().collect(Collectors.toMap(RiskDTO::getRiskCode, RiskDTO::getRiskName));
        ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
        if(settChnnelResultExportVo != null){
            channelCodeList = queryChannnelListParams(settChnnelResultExportVo, channelCodeList,channelMap);
            List<String> finalChannelCodeList = channelCodeList;
            //条件查询导出
            singleThreadExecutor.execute(new Runnable() {
                @Override
                public void run() {
                    exportChannelResult(settChnnelResultExportVo, finalChannelCodeList,fileUploadOutDto,channelMap,riskMap,message);
                }
            });
        }
        singleThreadExecutor.shutdown();
    }

    @SneakyThrows
    public void exportChannelResult(SettChnnelResultExportVo settChnnelResultExportVo, List<String> channelCodeList, FileUploadOutDto fileUploadOutDto, Map<String, String> channelMap,Map<String, String> riskMap, Map<String, String> message){
        long time = System.currentTimeMillis();
        Integer totalCount  = rylCountSettResultMapper.selectChannelResultTotalNum(settChnnelResultExportVo,channelCodeList);
        log.info("结算中心--》手续费缓/不结清单导出查询明细总条数结束,耗时:{}",System.currentTimeMillis() - time);
        log.info("=====结算中心--》手续费缓/不结清单导出查询明细条数为：{} 条=====",totalCount);
        if(totalCount==0){
            log.error("结算中心--》手续费缓/不结清单导出明细条数为空！");
            return ;
        }
        Long threadId = Thread.currentThread().getId();
        String fileName;
        if (StringUtils.isNotEmpty(settChnnelResultExportVo.getChannelCode())){
            QueryWrapper<RylCountSettlementChannelCfgEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("channel_code",settChnnelResultExportVo.getChannelCode());
            RylCountSettlementChannelCfgEntity rylChannelCfg = rylCountSettlementChannelCfgMapper.selectOne(queryWrapper);
            fileName = rylChannelCfg.getChannelName()+"-"+rylChannelCfg.getProtocolBouncersName()+"-"+"手续费缓/不结清单-"+new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        }else {
            fileName = "全量渠道-手续费缓/不结清单-"+new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        }
        String threadName = Thread.currentThread().getName();
        message.put("threadId",String.valueOf(threadId));
        message.put("fileName",fileName);
        SendMessageEvent sendMessageEvent = new SendMessageEvent(message);
        sendMessageEvent.setDataType(SETT_CHANNEL_RESULT_REPROT);
        sendMessageEvent.setOperate(0);
        String unionId = UUID.randomUUID() + "-" + threadId;
        sendMessageEvent.setUnionId(unionId);
        applicationEventPublisher.publishEvent(sendMessageEvent);
        log.info("当前线程名称:{} 当前线程Id:{}",threadName,threadId);
        @Cleanup
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ExcelWriter excelWriter= EasyExcel.write(os).build();
        try {
            List<SettChannelResultExportGatherDto> resultList = new ArrayList<>();
            List<SettChannelResultExportDto> targetList = rylCountSettResultMapper.selectChannelResultList(settChnnelResultExportVo,channelCodeList);
            targetList.stream().forEach(data->{
                data.setChannelCode(channelMap.get(data.getChannelCode()));
                String manageCom = data.getManageCom();
                if(null != manageCom && !"".equals(manageCom)){
                    data.setManageCom(CommonConstant.MANAGECOM_CONSTANTS.get("managecom",manageCom.substring(0,4)));
                }else{
                    data.setManageCom("DEFAULT");
                }
                data.setRiskCode(riskMap.get(data.getRiskCode()));
                if (StringUtils.isNotEmpty(data.getSettleStatusReason())){
                    JSONObject JSONA = JSONObject.parseObject(data.getSettleStatusReason());
                    JSONArray mess = (JSONArray)JSONA.get("messages");
                    ArrayList<String> list = mess.toJavaObject(ArrayList.class);
                    if(list.size()>=1){
                        String stringPipe = String.join("|", list);
                        data.setSettleStatusReason(stringPipe);
                    }
                }
            });

            List<String> nrusltList = targetList.stream().map(SettChannelResultExportDto::getSettleStatus).filter(settleStatus-> "不可结".equals(settleStatus)).collect(Collectors.toList());
            List<SettChannelResultExportDto> prusltList = targetList.stream().filter(person -> "缓结".equals(person.getSettleStatus())).collect(Collectors.toList());
            List<SettChannelResultExportDto> channelResultList = new ArrayList<>();
            if(!prusltList.isEmpty()){
                prusltList.stream().forEach(a->{
                    if (StringUtils.isNotEmpty(a.getSettleStatusReason())){
                        List<String> settList = Arrays.asList(StringUtil.split(a.getSettleStatusReason(), "|"));
                        for(String sett : settList){
                            SettChannelResultExportDto resultDto = new SettChannelResultExportDto();
                            BeanUtils.copyProperties(a, resultDto);
                            resultDto.setSettleStatusReason(sett);
                            channelResultList.add(resultDto);
                        }
                    }
                });

                Map<String,List<SettChannelResultExportDto>> list = channelResultList.stream().collect(Collectors.groupingBy(SettChannelResultExportDto::getSettleStatusReason));
                int i = 1;
                for (Map.Entry<String,List<SettChannelResultExportDto>> entry : list.entrySet()) {
                    SettChannelResultExportGatherDto rusultDto = new SettChannelResultExportGatherDto();
                    rusultDto.setNum(String.valueOf(i++));
                    rusultDto.setPolicySum(String.valueOf(targetList.size()));
                    if(!nrusltList.isEmpty()){
                        rusultDto.setNeverPolicySum(String.valueOf(nrusltList.size()));
                    }else{
                        rusultDto.setNeverPolicySum("0");
                    }
                    rusultDto.setPausePolicySum(String.valueOf(prusltList.size()));
                    rusultDto.setSlowSettMessage(entry.getKey());
                    rusultDto.setSlowSettPolicySum(String.valueOf(entry.getValue().size()));
                    resultList.add(rusultDto);
                }
            }

            if(!nrusltList.isEmpty() && prusltList.isEmpty()){
                int i = 1;
                SettChannelResultExportGatherDto rusultDto = new SettChannelResultExportGatherDto();
                rusultDto.setNum(String.valueOf(i));
                rusultDto.setPolicySum(String.valueOf(targetList.size()));
                rusultDto.setNeverPolicySum(String.valueOf(nrusltList.size()));
                if(!prusltList.isEmpty()){
                    rusultDto.setPausePolicySum(String.valueOf(prusltList.size()));
                }else{
                    rusultDto.setPausePolicySum("0");
                }
                resultList.add(rusultDto);
            }

            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            // 字体策略
            WriteFont contentWriteFont = new WriteFont();
            // 字体大小
            contentWriteFont.setFontHeightInPoints((short) 12);
            contentWriteFont.setFontName("宋体");
            contentWriteCellStyle.setWriteFont(contentWriteFont);
            headWriteCellStyle.setWriteFont(contentWriteFont);
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            WriteSheet writeSheet = EasyExcel.writerSheet("手续费缓不结清单交易明细").registerWriteHandler(horizontalCellStyleStrategy).head(SettChannelResultExportDto.class).build();
            excelWriter.write(targetList, writeSheet);
            //创建一个新的sheet
            //合并单元格
            String msg = "注：1、当期保单总数：当期业务类型为“新单”的总笔数；&2、举例，一个保单如同时未双录、未回访，“未回访成功”、“双录质检未通过”将分别体现各一次";
            SettChannelResultExportGatherDto settChannelResultExportGatherDto = new SettChannelResultExportGatherDto();
            settChannelResultExportGatherDto.setNum(String.join(String.valueOf((char)10), Arrays.asList(msg.split("&"))));
            resultList.add(settChannelResultExportGatherDto);
            OnceAbsoluteMergeStrategy mergeStrategy = new OnceAbsoluteMergeStrategy(resultList.size(),resultList.size()+3,0,5);
            if(resultList.size() >= 3){
                OnceAbsoluteMergeStrategy smergeStrategy = new OnceAbsoluteMergeStrategy(1,resultList.size()-1,1,1);
                OnceAbsoluteMergeStrategy nmergeStrategy = new OnceAbsoluteMergeStrategy(1,resultList.size()-1,2,2);
                OnceAbsoluteMergeStrategy pmergeStrategy = new OnceAbsoluteMergeStrategy(1,resultList.size()-1,3,3);
                writeSheet = EasyExcel.writerSheet("手续费缓不结清单汇总").head(SettChannelResultExportGatherDto.class).registerWriteHandler(horizontalCellStyleStrategy).registerWriteHandler(smergeStrategy).registerWriteHandler(nmergeStrategy)
                        .registerWriteHandler(pmergeStrategy).registerWriteHandler(mergeStrategy).build();
            }else{
                writeSheet = EasyExcel.writerSheet("手续费缓不结清单汇总").head(SettChannelResultExportGatherDto.class).registerWriteHandler(horizontalCellStyleStrategy).registerWriteHandler(mergeStrategy).build();
            }
            excelWriter.write(resultList, writeSheet);

            log.info("结算中心--》手续费缓/不结清单导出，查询明细写入sheet结束，总共写入条数:{}，耗时:{}",targetList.size(),System.currentTimeMillis() - time);
            excelWriter.finish();
            byte[] content = os.toByteArray();
            InputStream is = new ByteArrayInputStream(content);
            fileUploadOutDto = fileUpoadUtil.uploadN(is, fileName, CONTNUE_RATE_REPROT_ADDR);
            os.flush();
            log.info("结算中心--》手续费缓/不结清单导出，查询明细上传oss结束，耗时:{}",System.currentTimeMillis() - time);
            updateLog(unionId,fileUploadOutDto,SETT_CHANNEL_RESULT_REPROT);

        } catch (Exception e) {
            // 重置response
            Map<String, String> map = new HashMap<>(2);
            map.put("status", "failure");
            map.put("message", "上传文件失败" + e.getMessage());
            updateLog(unionId,fileUploadOutDto,SETT_CHANNEL_RESULT_REPROT);
            log.error("error-message:{}", e);
        } finally {
            excelWriter.finish();
            os.flush();
        }
        log.info("结算中心--》渠道结算数据导出结束，耗时:{}",System.currentTimeMillis() - time);
    }

    @Override
    public IPage<RylExportReportCommonLogEntity> exportResultList(SettChnnelResultExportVo settChnnelResultExportVo) {
        Long userId = settChnnelResultExportVo.getUserId();
        QueryWrapper<RylExportReportCommonLogEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(RylExportReportCommonLogEntity::getUserId,userId);
        queryWrapper.lambda().eq(RylExportReportCommonLogEntity::getDataType,SETT_CHANNEL_RESULT_REPROT);
        IPage<RylExportReportCommonLogEntity> page = new Page<>(settChnnelResultExportVo.getCurrent(),settChnnelResultExportVo.getSize());
        List<RylExportReportCommonLogEntity> list = exportReportCommonMapper.selectPageByUserId(page, queryWrapper);
        list.forEach(commonLog->{
            JSONObject content = DynaFormParseUtil.convertObj(commonLog.getQueryContent());
            commonLog.setQueryContent(content);
        });
        return page.setRecords(list);
    }

    @Override
    public IPage<RylExportReportCommonLogEntity> exportResultRylList(SettChnnelResultExportVo settChnnelResultExportVo) {
        QueryWrapper<RylExportReportCommonLogEntity> queryWrapper = new QueryWrapper<>();
        if(settChnnelResultExportVo.getChannelCode() != null){
            queryWrapper.like("query_content",settChnnelResultExportVo.getChannelCode());
        }
        if(settChnnelResultExportVo.getSignDateStart() != null && settChnnelResultExportVo.getSignDateEnd() != null){
            queryWrapper.apply(" DATE_FORMAT(create_time,'%Y-%m-%d') >= "+"'"+(settChnnelResultExportVo.getSignDateStart())+"'");
            queryWrapper.apply(" DATE_FORMAT(create_time,'%Y-%m-%d') <= "+"'"+(settChnnelResultExportVo.getSignDateEnd())+"'");
        }
        queryWrapper.lambda().eq(RylExportReportCommonLogEntity::getDataType,SETT_CHANNEL_RESULT_REPROT);
        Page<RylExportReportCommonLogEntity> page = new Page<>(settChnnelResultExportVo.getCurrent(),settChnnelResultExportVo.getSize());
        List<RylExportReportCommonLogEntity> list = exportReportCommonMapper.selectPageByUserId(page,queryWrapper);
        list.forEach(commonLog->{
            JSONObject content = DynaFormParseUtil.convertObj(commonLog.getQueryContent());
            commonLog.setQueryContent(content);
        });
        return page.setRecords(list);
    }

    private List<String> queryChannnelListParams(SettChnnelResultExportVo settChnnelResultExportVo,List<String> channelCodeList , Map<String, String> channelMap) {
        if (!StringUtil.isBlank(settChnnelResultExportVo.getChannelCode())) {
            String channelCodeMapping = rylCountSettlementChannelCfgMapper.getChannelCodeMapping(settChnnelResultExportVo.getChannelCode());
            if(StringUtil.isNotBlank(channelCodeMapping)){
                channelCodeList= Arrays.asList(StringUtil.split(channelCodeMapping, "|"));
                rylCountSettlementChannelCfgMapper.getChannelMapping(channelCodeList).forEach(dto -> channelMap.put(dto.getChannelCode(),dto.getChannelName()));
            }else{
                channelCodeList.add(settChnnelResultExportVo.getChannelCode());
                rylCountSettlementChannelCfgMapper.getChannelMapping(channelCodeList).forEach(dto -> channelMap.put(dto.getChannelCode(),dto.getChannelName()));
            }
        }else{
            rylCountSettlementChannelCfgMapper.getChannelMapping(channelCodeList).forEach(dto -> channelMap.put(dto.getChannelCode(),dto.getChannelName()));
        }
        return channelCodeList;
    }


    @SneakyThrows
    public void exportDataByConditions(SettCountExportReq settCountExportReq, List<String> channelCodeList, FileUploadOutDto fileUploadOutDto, Map<String, String> channelMap,Map<String, String> riskMap, Map<String, String> message){
        long time = System.currentTimeMillis();
        Integer totalCount  = rylCountSettResultMapper.selectTotalNum(settCountExportReq,channelCodeList);
        log.info("结算中心--》承保清单导出查询明细总条数结束,耗时:{}",System.currentTimeMillis() - time);
        log.info("=====结算中心--》承保清单导出查询明细条数为：{} 条=====",totalCount);
        if(totalCount==0){
            log.error("结算中心--》承保清单导出明细条数为空！");
            return ;
        }
        Long threadId = Thread.currentThread().getId();
        String fileName = "承保清单导出明细表-"+new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        String threadName = Thread.currentThread().getName();
        message.put("threadId",String.valueOf(threadId));
        message.put("fileName",fileName);
        SendMessageEvent sendMessageEvent = new SendMessageEvent(message);
        sendMessageEvent.setDataType(SETT_RESULT_REPROT);
        sendMessageEvent.setOperate(0);
        String unionId = UUID.randomUUID() + "-" + threadId;
        sendMessageEvent.setUnionId(unionId);
        applicationEventPublisher.publishEvent(sendMessageEvent);
        log.info("当前线程名称:{} 当前线程Id:{}",threadName,threadId);
        @Cleanup
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ExcelWriter excelWriter= EasyExcel.write(os).build();
        try {
            int pageNumber = totalCount/pageMaxNum+1;
            log.info("总页为pageNumber:{}",pageNumber);
            List<SettCountResultExportDto> targetList = Lists.newArrayList();

            List<Callable<List<SettCountResultExportDto>>> taskList = new ArrayList<>();//创建任务
            int rows = pageMaxNum;

            for (int i = 1; i <= pageNumber; i++) {
                int offset = i;
                Callable<List<SettCountResultExportDto>> task= new TaskThreadQuery(channelCodeList,settCountExportReq,offset, rows);//条件查询，name没有就给null
                taskList.add(task);
                offset += rows;
            }
            int threadNum = 10;//可自定义线程数量
            ExecutorService executor = Executors.newFixedThreadPool(threadNum);
            List<Future<List<SettCountResultExportDto>>> futureList = executor.invokeAll(taskList);
            if (futureList != null && futureList.size() > 0){
                for (Future<List<SettCountResultExportDto>> future:futureList) {
                    if(future.get() != null) {
                        future.get().stream().forEach(data ->{
                            data.setMainRiskName(riskMap.get(data.getMainRiskCode()));
                            data.setRiskName(riskMap.get(data.getRiskCode()));
                            if (data.getMioAmnt().compareTo(BigDecimal.ZERO) < 0) {
                                if (PAY_MODE_9.equals(data.getPayMode())) {
                                    data.setPayRole("渠道");
                                } else {
                                    data.setPayRole("弘康");
                                }
                            } else {
                                if (PAY_MODE_B.equals(data.getPayMode()) || PAY_MODE_C.equals(data.getPayMode()) || PAY_MODE_9.equals(data.getPayMode())) {
                                    data.setPayRole("渠道");
                                } else {
                                    data.setPayRole("弘康");
                                }
                            }
                            if(!StringUtil.isBlank(data.getPaymentMethod()) && "X".equals(data.getPaymentMethod())){
                                data.setPaymentMethod("续期保缴费");
                            }
                        });
                        targetList.addAll(future.get());
                    }
                }
            }
            executor.shutdown();//关闭线程*/
            log.info("结算中心--》承保清单导出，多线程查询明细结束,耗时:{}",System.currentTimeMillis() - time);
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            // 字体策略
            WriteFont contentWriteFont = new WriteFont();
            // 字体大小
            contentWriteFont.setFontHeightInPoints((short) 12);
            contentWriteFont.setFontName("宋体");
            contentWriteCellStyle.setWriteFont(contentWriteFont);
            headWriteCellStyle.setWriteFont(contentWriteFont);
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            WriteSheet writeSheet = EasyExcel.writerSheet("承保清单明细").registerWriteHandler(horizontalCellStyleStrategy).head(SettCountResultExportDto.class).build();
            excelWriter.write(targetList, writeSheet);
            log.info("结算中心--》承保清单导出，查询明细写入sheet结束，总共写入条数:{}，耗时:{}",targetList.size(),System.currentTimeMillis() - time);

            excelWriter.finish();
            byte[] content = os.toByteArray();
            InputStream is = new ByteArrayInputStream(content);

            fileUploadOutDto = fileUpoadUtil.upload(is, fileName, CONTNUE_RATE_REPROT_ADDR);
            os.flush();
            log.info("结算中心--》承保清单导出，查询明细上传oss结束，耗时:{}",System.currentTimeMillis() - time);
            updateLog(unionId,fileUploadOutDto,SETT_RESULT_REPROT);

        } catch (Exception e) {
            // 重置response
            Map<String, String> map = new HashMap<>(2);
            map.put("status", "failure");
            map.put("message", "上传文件失败" + e.getMessage());
            updateLog(unionId,fileUploadOutDto,SETT_RESULT_REPROT);
            log.error("error-message:{}", e);
        } finally {
            excelWriter.finish();
            os.flush();
        }
        log.info("结算中心--》承保清单导出结束，耗时:{}",System.currentTimeMillis() - time);
    }

    public void updateLog(String unionId,FileUploadOutDto fileUploadOutDto,String dataType) {
        log.info("更新当前线程的导出日志信息,导出unionId:{}",unionId);
        RylExportReportCommonLogEntity reportCommonLogEntity = new RylExportReportCommonLogEntity();
        reportCommonLogEntity.setDataType(dataType);
        reportCommonLogEntity.setUnionId(unionId);
        if(!Optional.ofNullable(fileUploadOutDto).isPresent()){
            //导出失败
            reportCommonLogEntity.setStatus(2);
            log.info("导出失败 reportLog{}",reportCommonLogEntity);
            exportReportCommonMapper.updateByUnionId(reportCommonLogEntity);
        }else {
            reportCommonLogEntity.setFileKey(fileUploadOutDto.getFileKey());
            reportCommonLogEntity.setStatus(9);
            log.info("导出成功 reportLog{}",reportCommonLogEntity);
            exportReportCommonMapper.updateByUnionId(reportCommonLogEntity);
        }
    }

    private List<String> handlerQueryParams(SettCountExportReq settCountExportReq,List<String> channelCodeList , Map<String, String> channelMap) {
        //获取渠道列表
        if (!StringUtil.isBlank(settCountExportReq.getChannelCode())) {
            String channelCodeMapping = rylCountSettlementChannelCfgMapper.getChannelCodeMapping(settCountExportReq.getChannelCode());
            if(StringUtil.isNotBlank(channelCodeMapping)){
                channelCodeList= Arrays.asList(StringUtil.split(channelCodeMapping, "|"));
                rylCountSettlementChannelCfgMapper.getChannelMapping(channelCodeList).forEach(dto -> channelMap.put(dto.getChannelCode(),dto.getChannelName()));
            } else {
                channelCodeList.add(settCountExportReq.getChannelCode());
                rylCountSettlementChannelCfgMapper.getChannelMapping(channelCodeList).forEach(dto -> channelMap.put(dto.getChannelCode(), dto.getChannelName()));
            }
        } else {
            rylCountSettlementChannelCfgMapper.getChannelMapping(channelCodeList).forEach(dto -> channelMap.put(dto.getChannelCode(), dto.getChannelName()));
        }
        return channelCodeList;
    }

    @Override
    public void manageExportExcel(SettChnnelResultExportVo settChnnelResultExportVo) {
        Map<String, String> channelMap = Maps.newHashMap();
        Map<String, String> message = Maps.newHashMap();
        List<String> channelCodeList = new ArrayList<>();
        message.put("queryParam", JSONObject.toJSONString(settChnnelResultExportVo));
        FileUploadOutDto fileUploadOutDto = null;
        ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
        channelCodeList = queryChannnelList(settChnnelResultExportVo, channelCodeList, channelMap);
        List<String> finalChannelCodeList = channelCodeList;
        //条件查询导出
        singleThreadExecutor.execute(new Runnable() {
            @Override
            public void run() {
                manageExportExcelList(settChnnelResultExportVo, finalChannelCodeList, fileUploadOutDto, channelMap, message);
            }
        });
        singleThreadExecutor.shutdown();
    }

    @SneakyThrows
    public void manageExportExcelList(SettChnnelResultExportVo settChnnelResultExportVo, List<String> channelCodeList, FileUploadOutDto fileUploadOutDto, Map<String, String> channelMap, Map<String, String> message) {
        long time = System.currentTimeMillis();
        Long threadId = Thread.currentThread().getId();
        QueryWrapper<RylCountManageReportLogEntity> queryWrapper = new QueryWrapper<>();
        if (channelCodeList.size() > 0) {
            queryWrapper.in("channel_code", channelCodeList);
        }
        if (!settChnnelResultExportVo.getDetpList().isEmpty()) {
            queryWrapper.in("dep_code", settChnnelResultExportVo.getDetpList());
        }
        if (!StringUtil.isBlank(settChnnelResultExportVo.getFrequency())) {
            queryWrapper.eq("frequency", settChnnelResultExportVo.getFrequency());
            String settDate = null;
            if ("W".equals(settChnnelResultExportVo.getFrequency())) {
                int week = DateUtil.thisDayOfWeek();
                if (week < BigDecimal.ROUND_UNNECESSARY) {
                    settDate = DateUtil.format(DateUtil.lastWeek(), "yyyy-MM-dd");
                } else {
                    settDate = DateUtil.format(new Date(), "yyyy-MM-dd");
                }
                queryWrapper.eq("settlement_date", settDate);
            } else if ("M".equals(settChnnelResultExportVo.getFrequency())) {
                String monthDate = DateUtil.format(DateUtil.endOfMonth(DateUtil.date()), "yyyy-MM-dd");
                if (DateUtil.format(new Date(), "yyyy-MM-dd").equals(monthDate)) {
                    settDate = monthDate;
                } else {
                    settDate = DateUtil.format(DateUtil.endOfMonth(DateUtil.lastMonth()), "yyyy-MM-dd");
                }
            }
            if (settDate != null) {
                queryWrapper.eq("settlement_date", settDate);
            }
        }
        queryWrapper.orderByDesc("current_accrual_commission_charge");
        List<RylCountManageReportLogEntity> resultList = rylCountManageReportLogMapper.selectList(queryWrapper);
        if (resultList.isEmpty()) {
            return;
        }
        String fileName = "手续费及账期报表导出明细清单-" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        String threadName = Thread.currentThread().getName();
        message.put("threadId", String.valueOf(threadId));
        message.put("fileName", fileName);
        SendMessageEvent sendMessageEvent = new SendMessageEvent(message);
        sendMessageEvent.setDataType(MANAGE_REPORT_REPROT);
        sendMessageEvent.setOperate(0);
        String unionId = UUID.randomUUID() + "-" + threadId;
        sendMessageEvent.setUnionId(unionId);
        applicationEventPublisher.publishEvent(sendMessageEvent);
        log.info("当前线程名称:{} 当前线程Id:{}", threadName, threadId);
        @Cleanup
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");

            //当期应结手续费汇总
            BigDecimal sumCurrentAccrualCommissionCharge = resultList.stream().map(RylCountManageReportLogEntity::getCurrentAccrualCommissionCharge).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
            //当期可结手续费汇总
            BigDecimal sumCurrentCurableCommissionCharge = resultList.stream().map(RylCountManageReportLogEntity::getCurrentCurableCommissionCharge).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
            //当期已结手续费汇总
            BigDecimal sumCurrentClosedCommissionCharge = resultList.stream().map(RylCountManageReportLogEntity::getCurrentClosedCommissionCharge).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
            //应结未付手续费汇总
            BigDecimal sumAccrualUnpaidCommissionCharge = resultList.stream().map(RylCountManageReportLogEntity::getAccrualUnpaidCommissionCharge).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);

            Map map = Maps.newHashMap();
            List<Map<String, Object>> listMap = new ArrayList<>();
            resultList.stream().forEach(x -> {
                Map<String, Object> lm = new HashMap<>();
                lm.put("channelName",x.getChannelName());
                lm.put("currentAccrualCommissionCharge",decimalFormat.format(x.getCurrentAccrualCommissionCharge().setScale(2, BigDecimal.ROUND_HALF_UP)));
                lm.put("currentCurableCommissionCharge",decimalFormat.format(x.getCurrentCurableCommissionCharge().setScale(2, BigDecimal.ROUND_HALF_UP)));
                lm.put("currentClosedCommissionCharge",decimalFormat.format(x.getCurrentClosedCommissionCharge().setScale(2, BigDecimal.ROUND_HALF_UP)));
                lm.put("accrualUnpaidCommissionCharge",decimalFormat.format(x.getAccrualUnpaidCommissionCharge().setScale(2, BigDecimal.ROUND_HALF_UP)));
                if(x.getCurrentAccrualCommissionCharge().setScale(2,BigDecimal.ROUND_HALF_UP).intValue() > 0 ||
                        x.getCurrentAccrualCommissionCharge().setScale(2,BigDecimal.ROUND_HALF_UP).intValue() < 0){
                    lm.put("accrualUnpaidAvgPaymentDays",x.getAccrualUnpaidAvgPaymentDays());
                    lm.put("accrualUnpaidMaxPaymentDays",x.getAccrualUnpaidMaxPaymentDays());
                    lm.put("closedMinPaymentDays",x.getClosedMinPaymentDays());
                }
                lm.put("settlementDate", DateUtil.format(x.getSettlementDate(), "yyyy-MM-dd"));
                listMap.add(lm);
            });
            map.put("resultList", listMap);
            map.put("sumCurrentAccrualCommissionCharge", decimalFormat.format(sumCurrentAccrualCommissionCharge));
            map.put("sumCurrentCurableCommissionCharge", decimalFormat.format(sumCurrentCurableCommissionCharge));
            map.put("sumCurrentClosedCommissionCharge", decimalFormat.format(sumCurrentClosedCommissionCharge));
            map.put("sumAccrualUnpaidCommissionCharge", decimalFormat.format(sumAccrualUnpaidCommissionCharge));
            //获取导出模板地址
            ClassPathResource classPathResource = new ClassPathResource(manageReportOssUrl);
            String path = classPathResource.getPath();
            TemplateExportParams templateExportParams1 = new TemplateExportParams(path);
            Workbook wb = ExcelExportUtil.exportExcel(templateExportParams1, map);
            ByteArrayOutputStream bufferedOutPut = new ByteArrayOutputStream();
            bufferedOutPut.flush();
            wb.write(bufferedOutPut);
            bufferedOutPut.close();
            ByteArrayInputStream swapStream = new ByteArrayInputStream(bufferedOutPut.toByteArray());
            fileUploadOutDto = fileUpoadUtil.uploadN(swapStream, fileName, CONTNUE_RATE_REPROT_ADDR);
            log.info("结算中心--》管理报表导出，查询明细上传oss结束，耗时:{}", System.currentTimeMillis() - time);
            updateLog(unionId, fileUploadOutDto, MANAGE_REPORT_REPROT);
        } catch (Exception e) {
            // 重置response
            Map<String, String> map = new HashMap<>(2);
            map.put("status", "failure");
            map.put("message", "上传文件失败" + e.getMessage());
            updateLog(unionId, fileUploadOutDto, MANAGE_REPORT_REPROT);
            log.error("error-message:{}", e);
        } finally {
//            excelWriter.finish();
            os.flush();
        }
        log.info("结算中心--》管理报表导出结束，耗时:{}", System.currentTimeMillis() - time);
    }

    @Override
    public IPage<RylExportReportCommonLogEntity> exportList(SettChnnelResultExportVo settChnnelResultExportVo) {
        QueryWrapper<RylExportReportCommonLogEntity> queryWrapper = new QueryWrapper<>();
        if(settChnnelResultExportVo.getUserId() != null){
            Long userId = settChnnelResultExportVo.getUserId();
            queryWrapper.lambda().eq(RylExportReportCommonLogEntity::getUserId, userId);
        }
        queryWrapper.lambda().eq(RylExportReportCommonLogEntity::getDataType, settChnnelResultExportVo.getDataType());
        IPage<RylExportReportCommonLogEntity> page = new Page<>(settChnnelResultExportVo.getCurrent(), settChnnelResultExportVo.getSize());
        List<RylExportReportCommonLogEntity> list = exportReportCommonMapper.selectPageByUserId(page, queryWrapper);
        list.forEach(commonLog -> {
            JSONObject content = DynaFormParseUtil.convertObj(commonLog.getQueryContent());
            commonLog.setQueryContent(content);
        });
        return page.setRecords(list);
    }

    @Override
    public void qualityControlExport(SettChnnelResultExportVo settChnnelResultExportVo) {
        Map<String, String> channelMap = Maps.newHashMap();
        Map<String, String> message = Maps.newHashMap();
        List<String> channelCodeList = new ArrayList<>();
        message.put("queryParam", JSONObject.toJSONString(settChnnelResultExportVo));
        FileUploadOutDto fileUploadOutDto = null;
        ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
        channelCodeList = queryChannnelList(settChnnelResultExportVo, channelCodeList, channelMap);
        List<String> finalChannelCodeList = channelCodeList;
        //条件查询导出
        singleThreadExecutor.execute(new Runnable() {
            @Override
            public void run() {
                qualityControlExportList(settChnnelResultExportVo, finalChannelCodeList, fileUploadOutDto, channelMap, message);
            }
        });
        singleThreadExecutor.shutdown();
    }

    @SneakyThrows
    public void qualityControlExportList(SettChnnelResultExportVo settChnnelResultExportVo, List<String> channelCodeList, FileUploadOutDto fileUploadOutDto, Map<String, String> channelMap, Map<String, String> message) {
        long time = System.currentTimeMillis();
        QueryWrapper<RylCountQualityControlLogEntity> queryWrapper = new QueryWrapper<>();
        if (channelCodeList.size() > 0) {
            queryWrapper.in("channel_code", channelCodeList);
        }
        if (!settChnnelResultExportVo.getDetpList().isEmpty()) {
            queryWrapper.in("dep_code", settChnnelResultExportVo.getDetpList());
        }
        queryWrapper.orderByDesc("accrual_commission_charge");
        List<RylCountQualityControlLogEntity> targetList = rylCountQualityControlLogMapper.selectList(queryWrapper);
        if (targetList.isEmpty()) {
            return;
        }
        Long threadId = Thread.currentThread().getId();
        String fileName = "品控报表导出明细表-" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        String threadName = Thread.currentThread().getName();
        message.put("threadId", String.valueOf(threadId));
        message.put("fileName", fileName);
        SendMessageEvent sendMessageEvent = new SendMessageEvent(message);
        sendMessageEvent.setDataType(QUALITY_CONTROL_REPORT_REPROT);
        sendMessageEvent.setOperate(0);
        String unionId = UUID.randomUUID() + "-" + threadId;
        sendMessageEvent.setUnionId(unionId);
        applicationEventPublisher.publishEvent(sendMessageEvent);
        log.info("当前线程名称:{} 当前线程Id:{}", threadName, threadId);
        @Cleanup
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
            String percentFormat = "%";
            //应结手续费
            BigDecimal sumAccrualCommissionCharge = targetList.stream().map(RylCountQualityControlLogEntity::getAccrualCommissionCharge).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
            //首次结算金额
            BigDecimal sumFirstSettlementAmount = targetList.stream().map(RylCountQualityControlLogEntity::getFirstSettlementAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
            //首次缓结金额
            BigDecimal sumFirstPauseAmount = targetList.stream().map(RylCountQualityControlLogEntity::getFirstPauseAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
            Map map = Maps.newHashMap();
            List<Map<String, Object>> listMap = new ArrayList<>();
            targetList.stream().forEach(x -> {
                Map<String, Object> lm = new HashMap<>();
                lm.put("channelName",x.getChannelName());
                lm.put("accrualCommissionCharge",decimalFormat.format(x.getAccrualCommissionCharge().setScale(2, BigDecimal.ROUND_HALF_UP)));
                lm.put("firstSettlementAmount",decimalFormat.format(x.getFirstSettlementAmount().setScale(2, BigDecimal.ROUND_HALF_UP)));
                lm.put("firstSettlementAmountProportion",x.getFirstSettlementAmountProportion()+percentFormat);
                lm.put("firstPauseAmount",decimalFormat.format(x.getFirstPauseAmount().setScale(2, BigDecimal.ROUND_HALF_UP)));
                lm.put("firstPauseAmountProportion",x.getFirstPauseAmountProportion()+percentFormat);
                lm.put("avgPauseDays",x.getAvgPauseDays());
                lm.put("settlementDate", x.getSettlementDate());
                listMap.add(lm);
            });
            map.put("resultList", listMap);
            map.put("sumAccrualCommissionCharge", decimalFormat.format(sumAccrualCommissionCharge));
            map.put("sumFirstSettlementAmount", decimalFormat.format(sumFirstSettlementAmount));
            map.put("sumFirstPauseAmount", decimalFormat.format(sumFirstPauseAmount));
            //获取导出模板地址
            ClassPathResource classPathResource = new ClassPathResource(qualityControlOssUrl);
            String path = classPathResource.getPath();
            TemplateExportParams templateExportParams1 = new TemplateExportParams(path);
            Workbook wb = ExcelExportUtil.exportExcel(templateExportParams1, map);
            ByteArrayOutputStream bufferedOutPut = new ByteArrayOutputStream();
            bufferedOutPut.flush();
            wb.write(bufferedOutPut);
            bufferedOutPut.close();
            ByteArrayInputStream swapStream = new ByteArrayInputStream(bufferedOutPut.toByteArray());
            fileUploadOutDto = fileUpoadUtil.uploadN(swapStream, fileName, CONTNUE_RATE_REPROT_ADDR);
            log.info("结算中心--》管理报表导出，查询明细上传oss结束，耗时:{}", System.currentTimeMillis() - time);
            updateLog(unionId, fileUploadOutDto, QUALITY_CONTROL_REPORT_REPROT);
        } catch (Exception e) {
            // 重置response
            Map<String, String> map = new HashMap<>(2);
            map.put("status", "failure");
            map.put("message", "上传文件失败" + e.getMessage());
            updateLog(unionId, fileUploadOutDto, QUALITY_CONTROL_REPORT_REPROT);
            log.error("error-message:{}", e);
        } finally {
//            excelWriter.finish();
            os.flush();
        }
        log.info("管理报表-品控报表导出结束，耗时:{}", System.currentTimeMillis() - time);
    }

    @Override
    public void allChannelResultExportExcel(SettChnnelResultExportVo settChnnelResultExportVo) {
        Map<String, String> channelMap = Maps.newHashMap();
        Map<String, String> message = Maps.newHashMap();
        List<String> channelCodeList = new ArrayList<>();
        message.put("queryParam", JSONObject.toJSONString(settChnnelResultExportVo));
        FileUploadOutDto fileUploadOutDto = null;
        ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
        channelCodeList = queryChannnelList(settChnnelResultExportVo, channelCodeList, channelMap);
        List<String> finalChannelCodeList = channelCodeList;
        //条件查询导出
        singleThreadExecutor.execute(new Runnable() {
            @Override
            public void run() {
                allExportChannelResult(settChnnelResultExportVo,finalChannelCodeList,fileUploadOutDto,message);
            }
        });
        singleThreadExecutor.shutdown();
    }

    @SneakyThrows
    public void allExportChannelResult(SettChnnelResultExportVo settChnnelResultExportVo,List<String> channelCodeList,FileUploadOutDto fileUploadOutDto, Map<String, String> message){
        long time = System.currentTimeMillis();
        QueryWrapper<RylCountSlowKnotLogEntity> queryWrapper = new QueryWrapper<>();

        if(StringUtil.isBlank(settChnnelResultExportVo.getStartMonth())){
            queryWrapper.ge("busi_month", "2022-10");
        }else{
            queryWrapper.ge("busi_month", settChnnelResultExportVo.getStartMonth());
        }
        if(StringUtil.isBlank(settChnnelResultExportVo.getEndMonth())){
            queryWrapper.le("busi_month", DateUtil.format(new Date(),"yyyy-MM"));
        }else{
            queryWrapper.le("busi_month", settChnnelResultExportVo.getEndMonth());
        }
        if (channelCodeList.size() > 0) {
            queryWrapper.in("channel_code", channelCodeList);
        }
        if (!settChnnelResultExportVo.getDetpList().isEmpty()) {
            queryWrapper.in("dep_code", settChnnelResultExportVo.getDetpList());
        }
        List<RylCountSlowKnotLogEntity> countSlowList = rylCountSlowKnotLogMapper.selectList(queryWrapper);
        if(countSlowList.isEmpty()){
            return ;
        }
        Long threadId = Thread.currentThread().getId();
        String fileName = "手续费缓/不结清单-"+new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        String threadName = Thread.currentThread().getName();
        message.put("threadId",String.valueOf(threadId));
        message.put("fileName",fileName);
        SendMessageEvent sendMessageEvent = new SendMessageEvent(message);
        sendMessageEvent.setDataType(ALLCHANNEL_REPORT_REPROT);
        sendMessageEvent.setOperate(0);
        String unionId = UUID.randomUUID() + "-" + threadId;
        sendMessageEvent.setUnionId(unionId);
        applicationEventPublisher.publishEvent(sendMessageEvent);
        log.info("当前线程名称:{} 当前线程Id:{}",threadName,threadId);
        @Cleanup
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ExcelWriter excelWriter= EasyExcel.write(os).build();
        try {
            DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
            List<SettAllChannelResultExportDto> resultList = new ArrayList<>();
            List<RylCountSlowKnotLogEntity> rylCountList = countSlowList.stream().filter(x-> x.getSlowSettMessage() != null).collect(Collectors.toList());
            List<RylCountSlowKnotLogEntity> nrylCountList = countSlowList.stream().filter(x-> x.getSlowSettMessage() == null).collect(Collectors.toList());
            int nSum = nrylCountList.stream().map(RylCountSlowKnotLogEntity::getSumPolicy).reduce(0,Integer::sum);
            Map<String,List<RylCountSlowKnotLogEntity>> list = rylCountList.stream().collect(Collectors.groupingBy(RylCountSlowKnotLogEntity::getSlowSettMessage));
            int i = 1;
            //根据渠道以及计算月份进行组合去重
            List<RylCountSlowKnotLogEntity> collect = rylCountList.stream()
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                            new TreeSet<>(Comparator.comparing(o -> o.getChannelCode() + ";" + o.getBusiMonth()))), ArrayList::new));
            int policySum = collect.stream().map(RylCountSlowKnotLogEntity::getSumPolicy).reduce(0,Integer::sum);
            int neverPolicySum = collect.stream().map(RylCountSlowKnotLogEntity::getSumNeverPolicy).reduce(0,Integer::sum);
            int pausePolicySum = collect.stream().map(RylCountSlowKnotLogEntity::getSumPausePolicy).reduce(0,Integer::sum);
            policySum = new BigDecimal(policySum).add(new BigDecimal(nSum)).intValue();
            neverPolicySum = new BigDecimal(neverPolicySum).add(new BigDecimal(nSum)).intValue();
            BigDecimal pauseCommissionCharge = collect.stream().map(RylCountSlowKnotLogEntity::getPauseCommissionCharge).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
            for (Map.Entry<String,List<RylCountSlowKnotLogEntity>> entry : list.entrySet()) {
                SettAllChannelResultExportDto rusultDto = new SettAllChannelResultExportDto();
                rusultDto.setNum(String.valueOf(i++));
                List<RylCountSlowKnotLogEntity> entryList = entry.getValue();
                rusultDto.setPolicySum(String.valueOf(policySum));
                rusultDto.setNeverPolicySum(String.valueOf(neverPolicySum));
                rusultDto.setPausePolicySum(String.valueOf(pausePolicySum));
                rusultDto.setPauseCommissionCharge(decimalFormat.format(pauseCommissionCharge));
                rusultDto.setSlowSettMessage(entry.getKey());
                int slowSettPolicySum = entryList.stream().mapToInt(RylCountSlowKnotLogEntity::getSumSlowSettPolicy).reduce(0,Integer::sum);
                rusultDto.setSlowSettPolicySum(String.valueOf(slowSettPolicySum));
                BigDecimal slowCommissionCharge = entryList.stream().map(RylCountSlowKnotLogEntity::getSumSlowCommissionCharge).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
                rusultDto.setSlowCommissionCharge(decimalFormat.format(slowCommissionCharge));
                resultList.add(rusultDto);
            }
            //表头样式策略
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            //设置表头居中对齐
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            //表头前景设置黑色
            headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setBold(true);
            headWriteFont.setFontName("宋体");
            headWriteFont.setFontHeightInPoints((short) 14);
            headWriteCellStyle.setWriteFont(headWriteFont);
            //内容样式策略策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            // 设置背景颜色白色
            contentWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
            // 设置垂直居中为居中对齐
            contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            // 设置左右对齐为靠左对齐
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            // 设置单元格上下左右边框为细边框
            contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
            contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
            contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
            contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
            //创建字体对象
            WriteFont contentWriteFont = new WriteFont();
            //内容字体大小
            contentWriteFont.setFontName("宋体");
            contentWriteFont.setFontHeightInPoints((short) 12);
            contentWriteCellStyle.setWriteFont(contentWriteFont);
            // 初始化表格样式
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            //创建一个新的sheet
            //合并单元格
            String msg = "注：1、当期保单总数：当期业务类型为“新单”的总笔数；&2、举例，一个保单如同时未双录、未回访，“未回访成功”、“双录质检未通过”将分别体现各一次";
            SettAllChannelResultExportDto resultExportDto = new SettAllChannelResultExportDto();
            resultExportDto.setNum(String.join(String.valueOf((char)10), Arrays.asList(msg.split("&"))));
            resultList.add(resultExportDto);
            WriteSheet writeSheet = new WriteSheet();
            OnceAbsoluteMergeStrategy mergeStrategy = new OnceAbsoluteMergeStrategy(resultList.size(), resultList.size() + 4, 0, 7);
            if(resultList.size() >= 3){
                OnceAbsoluteMergeStrategy smergeStrategy = new OnceAbsoluteMergeStrategy(1,resultList.size()-1,1,1);
                OnceAbsoluteMergeStrategy nmergeStrategy = new OnceAbsoluteMergeStrategy(1,resultList.size()-1,2,2);
                OnceAbsoluteMergeStrategy pmergeStrategy = new OnceAbsoluteMergeStrategy(1,resultList.size()-1,3,3);
                OnceAbsoluteMergeStrategy zmergeStrategy = new OnceAbsoluteMergeStrategy(1,resultList.size()-1,4,4);
                writeSheet = EasyExcel.writerSheet("手续费缓不结清单汇总").head(SettAllChannelResultExportDto.class).registerWriteHandler(horizontalCellStyleStrategy).registerWriteHandler(smergeStrategy).registerWriteHandler(nmergeStrategy)
                        .registerWriteHandler(pmergeStrategy).registerWriteHandler(mergeStrategy).registerWriteHandler(zmergeStrategy).build();
            }else{
                writeSheet = EasyExcel.writerSheet("手续费缓不结清单汇总").head(SettAllChannelResultExportDto.class).registerWriteHandler(horizontalCellStyleStrategy).registerWriteHandler(mergeStrategy).build();
            }
            excelWriter.write(resultList, writeSheet);
            log.info("结算中心--》手续费缓/不结清单汇总导出，写入sheet结束，总共写入条数:{}，耗时:{}",resultList.size(),System.currentTimeMillis() - time);
            excelWriter.finish();
            byte[] content = os.toByteArray();
            InputStream is = new ByteArrayInputStream(content);
            fileUploadOutDto = fileUpoadUtil.uploadN(is, fileName, CONTNUE_RATE_REPROT_ADDR);
            os.flush();
            log.info("结算中心--》手续费缓/不结清单汇总导出，查询明细上传oss结束，耗时:{}",System.currentTimeMillis() - time);
            updateLog(unionId,fileUploadOutDto,ALLCHANNEL_REPORT_REPROT);
        } catch (Exception e) {
            // 重置response
            Map<String, String> map = new HashMap<>(2);
            map.put("status", "failure");
            map.put("message", "上传文件失败" + e.getMessage());
            updateLog(unionId,fileUploadOutDto,ALLCHANNEL_REPORT_REPROT);
            log.error("error-message:{}", e);
        } finally {
            excelWriter.finish();
            os.flush();
        }
        log.info("结算中心--》手续费缓/不结清单汇总导出结束，耗时:{}",System.currentTimeMillis() - time);
    }

    public static List<String> getMonthBetweenDate(String startTime, String endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        List<String> list = new ArrayList<String>();
        try {
            Date startDate = sdf.parse(startTime);
            Date endDate = sdf.parse(endTime);
            Calendar calendar = Calendar.getInstance();
            while (startDate.getTime() <= endDate.getTime()) {
                list.add(sdf.format(startDate));
                calendar.setTime(startDate);
                calendar.add(Calendar.MONTH, 1);
                startDate = calendar.getTime();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    @Override
    public BaseResponse queryMemorandum(CountSettlementMrmorandumDTO countSettlementMrmorandumDTO) {
        if(countSettlementMrmorandumDTO == null){
            return BaseResponse.fail("-1","请检查报送备忘录的入参");
        }
        QueryWrapper<RylCountSettlementMemorandumEntity> queryWrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(countSettlementMrmorandumDTO.getChannelCode())){
            queryWrapper.eq("channel_code",countSettlementMrmorandumDTO.getChannelCode());
        }
        if(StringUtils.isNotBlank(countSettlementMrmorandumDTO.getRiskCode())){
            queryWrapper.eq("risk_code",countSettlementMrmorandumDTO.getRiskCode());
        }
        if(StringUtils.isNotBlank(countSettlementMrmorandumDTO.getEventName())){
            queryWrapper.like("event_name",countSettlementMrmorandumDTO.getEventName());
        }
        queryWrapper.eq("is_del","0");
        IPage<RylCountSettlementMemorandumEntity> page = new Page<>(countSettlementMrmorandumDTO.getPageNum(),countSettlementMrmorandumDTO.getPageSize());
        IPage<RylCountSettlementMemorandumEntity> list = rylCountSettlementMemorandumMapper.selectPage(page,queryWrapper);
        return BaseResponse.success(list);
    }

    @Override
    public BaseResponse updateMemorandum(RylCountSettlementMemorandumEntity rylCountSettlementMemorandumEntity) {
        int flag = 0;
        UpdateWrapper<RylCountSettlementMemorandumEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id",rylCountSettlementMemorandumEntity.getId());
        flag = rylCountSettlementMemorandumMapper.update(rylCountSettlementMemorandumEntity,updateWrapper);
        if (flag == 0) {
            return BaseResponse.fail("-1", "更新报送备忘录信息失败" + rylCountSettlementMemorandumEntity.getId());
        } else {
            return BaseResponse.success();
        }
    }

    @Override
    public BaseResponse addMemorandum(RylCountSettlementMemorandumEntity rylCountSettlementMemorandumEntity) throws Exception {
        int flag = 0;
        flag = rylCountSettlementMemorandumMapper.insert(rylCountSettlementMemorandumEntity);
        if (flag == 0) {
            return BaseResponse.fail("-1", "新增报送备忘录信息失败");
        } else {
            return BaseResponse.success();
        }
    }

    @Override
    public BaseResponse uploadFile(MultipartFile file) throws Exception {
        if (file == null || file.isEmpty()) {
            return BaseResponse.fail("-1", "报送备忘录上传文件为空");
        }
        InputStream inputStream = file.getInputStream();
        FileUploadOutDto fileListDTO = fileUpoadUtil.uploadN(inputStream, file.getOriginalFilename(), COUNT_SETT_MEMORANDUM_ADDR);
        if (fileListDTO == null) {
            return BaseResponse.fail("-1", "报送备忘录上传文件失败");
        }else{
            return BaseResponse.success(fileListDTO);
        }
    }


    @Override
    public BaseResponse downloadFile(RylExportReportCommonLogEntity rylExportReportCommonLogEntity) throws Exception {
        if(rylExportReportCommonLogEntity.getFileKey() == null || rylExportReportCommonLogEntity.getFileName() == null){
            return BaseResponse.fail("-1", "文件信息不能为空");
        }
        List<FileUploadOutDto> fileList = fileUpoadUtil.getFileList(rylExportReportCommonLogEntity.getFileKey(), rylExportReportCommonLogEntity.getFileName(), COUNT_SETT_MEMORANDUM_ADDR);
        if (fileList.isEmpty()) {
            return BaseResponse.fail("-1", "获取文件信息失败");
        }else{
            return BaseResponse.success(fileList);
        }
    }

    private List<String> queryChannnelList(SettChnnelResultExportVo settChnnelResultExportVo,List<String> channelCodeList , Map<String, String> channelMap) {
        if(!settChnnelResultExportVo.getChannelCodeList().isEmpty()){
            for(String channelCode : settChnnelResultExportVo.getChannelCodeList()){
                String channelCodeMapping = rylCountSettlementChannelCfgMapper.getChannelCodeMapping(channelCode);
                if(StringUtil.isNotBlank(channelCodeMapping)){
                    List<String> codeList= Arrays.asList(StringUtil.split(channelCodeMapping, "|"));
                    channelCodeList.addAll(codeList);
                    rylCountSettlementChannelCfgMapper.getChannelMapping(channelCodeList).forEach(dto -> channelMap.put(dto.getChannelCode(),dto.getChannelName()));
                }else{
                    channelCodeList.add(channelCode);
                    rylCountSettlementChannelCfgMapper.getChannelMapping(channelCodeList).forEach(dto -> channelMap.put(dto.getChannelCode(),dto.getChannelName()));
                }
            }
        }else{
            List<ChannelDTO> channelDTOS = rylCountSettlementChannelCfgMapper.getChannelMapping(channelCodeList);
            for(ChannelDTO x : channelDTOS){
                channelMap.put(x.getChannelCode(),x.getChannelName());
                channelCodeList.add(x.getChannelCode());
            }
        }
        return channelCodeList;
    }

    @Override
    public void exportCancelRate(SettChnnelResultExportVo settChnnelResultExportVo) {
        Map<String, String> channelMap = Maps.newHashMap();
        Map<String, String> message = Maps.newHashMap();
        List<String> channelCodeList = new ArrayList<>();
        List<RiskDTO> rylContRiskCodeEntities = rylCountSettlementRateMapper.getAllRisk();
        Map<String, String> riskMap = rylContRiskCodeEntities.stream().collect(Collectors.toMap(RiskDTO::getRiskCode, RiskDTO::getRiskName));
        message.put("queryParam", JSONObject.toJSONString(settChnnelResultExportVo));
        FileUploadOutDto fileUploadOutDto = null;
        ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
        channelCodeList = queryChannnelList(settChnnelResultExportVo, channelCodeList, channelMap);
        String startMonth = null;
        String endMonth = null;
        if(!StringUtil.isBlank(settChnnelResultExportVo.getSettMonth())){
            endMonth = settChnnelResultExportVo.getSettMonth();
            startMonth = getStartMonth(endMonth);
        }else{
            endMonth = DateUtil.format(new Date(),"yyyy-MM");
            startMonth = getStartMonth(endMonth);
        }
        //条件查询导出
        String finalStartMonth = startMonth;
        String finalEndMonth = endMonth;
        singleThreadExecutor.execute(new Runnable() {
            @Override
            public void run() {
                cancelRate(settChnnelResultExportVo,fileUploadOutDto,channelMap,riskMap,finalStartMonth,finalEndMonth,message);
            }
        });
        singleThreadExecutor.shutdown();
    }

    @SneakyThrows
    public void cancelRate(SettChnnelResultExportVo settChnnelResultExportVo,FileUploadOutDto fileUploadOutDto,
                           Map<String, String> channelMap,Map<String, String> riskMap,String startMonth,String endMonth, Map<String, String> message) {
        long time = System.currentTimeMillis();
        List<RylCountSettlementChannelMappingCfgEntity> codeList = rylCountSettlementChannelMappingCfgMapper.queryChannelCode(settChnnelResultExportVo.getDetpList(),settChnnelResultExportVo.getChannelCodeList());
        if(codeList.isEmpty()){
            return;
        }
        Long threadId = Thread.currentThread().getId();
        String fileName = "退保率导出明细表-" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        String threadName = Thread.currentThread().getName();
        message.put("threadId", String.valueOf(threadId));
        message.put("fileName", fileName);
        SendMessageEvent sendMessageEvent = new SendMessageEvent(message);
        sendMessageEvent.setDataType(CANCEL_RATE_REPORT_REPROT);
        sendMessageEvent.setOperate(0);
        String unionId = UUID.randomUUID() + "-" + threadId;
        sendMessageEvent.setUnionId(unionId);
        applicationEventPublisher.publishEvent(sendMessageEvent);
        log.info("当前线程名称:{} 当前线程Id:{}", threadName, threadId);
        List<String> channelCodeList = new ArrayList<>();
        codeList.stream().forEach(x->{
            channelCodeList.addAll(Arrays.asList(StringUtil.split(x.getSubChannelCode(), "|")));
        });
        //获取存在退保率的渠道
        QueryWrapper<RylCountCancelRateEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("channel_code",channelCodeList);
        if(!StringUtil.isBlank(settChnnelResultExportVo.getRiskCode())){
            queryWrapper.eq("main_risk_code",settChnnelResultExportVo.getRiskCode());
        }
        queryWrapper.apply(" DATE_FORMAT(policy_valid_month,'%Y-%m') >= "+"'"+(startMonth)+"'");
        queryWrapper.apply(" DATE_FORMAT(policy_valid_month,'%Y-%m') <= "+"'"+(endMonth)+"'");
        List<RylCountCancelRateEntity> cancelRateList = rylCountCancelRateMapper.selectList(queryWrapper);
        List<String> cancelChannelList = cancelRateList.stream().map(RylCountCancelRateEntity::getChannelCode).distinct().collect(Collectors.toList());
        codeList = codeList.stream().filter(x-> cancelChannelList.contains(x.getChannelCode())).collect(Collectors.toList());

        Map<String, Map<String, String>> mainTemp = Maps.newLinkedHashMap();

        List<CountCancelDetailRateDTO> cancelDetailRateDTOList = new ArrayList<>();
//        CompletableFuture[] completableFutures = codeList.stream().map(key ->
//                        CompletableFuture.runAsync(() -> {
//                            countSettlementSchedule(mainTemp,key,settChnnelResultExportVo.getRiskCode(),channelMap,riskMap,startMonth,endMonth);
//                        }, CHANNEL_REPORT_THREAD_EXECUTOR))
//                .toArray(CompletableFuture[]::new);
//        // 通过allOf执行异步任务，里面是一个completableFuture数组。并调用join()阻塞等待所有任务执行完成。
//        CompletableFuture.allOf(completableFutures).join();
//        CHANNEL_REPORT_THREAD_EXECUTOR.shutdown();
        for(RylCountSettlementChannelMappingCfgEntity key : codeList){
            countSettlementSchedule(mainTemp,key,settChnnelResultExportVo.getRiskCode(),channelMap,riskMap,startMonth,endMonth,cancelDetailRateDTOList);
        }
        List<Map<String, String>> mainList = mainTemp.entrySet().stream().map(stringMapEntry -> stringMapEntry.getValue()).collect(Collectors.toList());
        if(mainList.isEmpty()){
            //无数据提示导出失败
            updateLog(unionId, fileUploadOutDto, CANCEL_RATE_REPORT_REPROT);
            return;
        }

        List<CountCancelRateDTO> cancelRateDTOList = mainList.stream()
                .map(map -> {
                    CountCancelRateDTO dto = new CountCancelRateDTO();
                    dto.setChannelName(map.get("channelName"));
                    dto.setMainRiskName(map.get("mainRiskName"));
                    dto.setPolicyValidMonth(map.get("policyValidMonth"));
                    dto.setFirstMioAmnt(map.get("firstMioAmnt"));
                    dto.setCommissionCharge(map.get("commissionCharge"));
                    if(!StringUtil.isBlank(map.get("T1"))){
                        dto.setCancelValueT1(map.get("T1"));
                    }
                    if(!StringUtil.isBlank(map.get("T2"))){
                        dto.setCancelValueT2(map.get("T2"));
                    }
                    if(!StringUtil.isBlank(map.get("T3"))){
                        dto.setCancelValueT3(map.get("T3"));
                    }
                    if(!StringUtil.isBlank(map.get("T4"))){
                        dto.setCancelValueT4(map.get("T4"));
                    }
                    if(!StringUtil.isBlank(map.get("T5"))){
                        dto.setCancelValueT5(map.get("T5"));
                    }
                    if(!StringUtil.isBlank(map.get("T6"))){
                        dto.setCancelValueT6(map.get("T6"));
                    }
                    if(!StringUtil.isBlank(map.get("T7"))){
                        dto.setCancelValueT7(map.get("T7"));
                    }
                    if(!StringUtil.isBlank(map.get("T8"))){
                        dto.setCancelValueT8(map.get("T8"));
                    }
                    if(!StringUtil.isBlank(map.get("T9"))){
                        dto.setCancelValueT9(map.get("T9"));
                    }
                    if(!StringUtil.isBlank(map.get("T10"))){
                        dto.setCancelValueT10(map.get("T10"));
                    }
                    if(!StringUtil.isBlank(map.get("T11"))){
                        dto.setCancelValueT11(map.get("T11"));
                    }
                    if(!StringUtil.isBlank(map.get("T12"))){
                        dto.setCancelValueT12(map.get("T12"));
                    }
                    if(!StringUtil.isBlank(map.get("T13"))){
                        dto.setCancelValueT13(map.get("T13"));
                    }
                    if(!StringUtil.isBlank(map.get("T14"))){
                        dto.setCancelValueT14(map.get("T14"));
                    }
                    if(!StringUtil.isBlank(map.get("M13"))){
                        dto.setContinueValueM13(map.get("M13"));
                    }
                    if(!StringUtil.isBlank(map.get("X1"))){
                        dto.setXyContunueValue(map.get("X1"));
                    }
                    if(!StringUtil.isBlank(map.get("commission"))){
                        dto.setCommission(map.get("commission"));
                    }
                    if(!StringUtil.isBlank(map.get("settMonth"))){
                        dto.setSettMonth(map.get("settMonth"));
                    }
                    return dto;
                })
                .collect(Collectors.toList());

        @Cleanup
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ExcelWriter excelWriter= EasyExcel.write(os).build();
        try{
            //表头样式策略
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            //设置表头居中对齐
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            //表头前景设置黑色
            headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setBold(true);
            headWriteFont.setFontName("宋体");
            headWriteFont.setFontHeightInPoints((short) 14);
            headWriteCellStyle.setWriteFont(headWriteFont);
            //内容样式策略策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            // 设置背景颜色白色
            contentWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
            // 设置垂直居中为居中对齐
            contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            // 设置左右对齐为靠左对齐
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            // 设置单元格上下左右边框为细边框
            contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
            contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
            contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
            contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
            //创建字体对象
            WriteFont contentWriteFont = new WriteFont();
            //内容字体大小
            contentWriteFont.setFontName("宋体");
            contentWriteFont.setFontHeightInPoints((short) 12);
            contentWriteCellStyle.setWriteFont(contentWriteFont);
            // 初始化表格样式
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            WriteSheet writeSheet = EasyExcel.writerSheet("退保率").registerWriteHandler(horizontalCellStyleStrategy).head(CountCancelRateDTO.class).build();
            excelWriter.write(cancelRateDTOList, writeSheet);

            writeSheet = EasyExcel.writerSheet("退保率明细").head(CountCancelDetailRateDTO.class).registerWriteHandler(horizontalCellStyleStrategy).build();
            excelWriter.write(cancelDetailRateDTOList, writeSheet);

            excelWriter.finish();
            byte[] content = os.toByteArray();
            InputStream is = new ByteArrayInputStream(content);
            fileUploadOutDto = fileUpoadUtil.uploadN(is, fileName, CONTNUE_RATE_REPROT_ADDR);
            log.info("结算中心--》退保率报表导出，查询明细上传oss结束，耗时:{}", System.currentTimeMillis() - time);
            updateLog(unionId, fileUploadOutDto, CANCEL_RATE_REPORT_REPROT);
        } catch (Exception e) {
            // 重置response
            Map<String, String> map = new HashMap<>(2);
            map.put("status", "failure");
            map.put("message", "上传文件失败" + e.getMessage());
            updateLog(unionId, fileUploadOutDto, CANCEL_RATE_REPORT_REPROT);
            log.error("error-message:{}", e);
        } finally {
            os.flush();
        }
        log.info("退保率报表导出结束，耗时:{}", System.currentTimeMillis() - time);

    }

    private void countSettlementSchedule(Map<String, Map<String, String>> temp, RylCountSettlementChannelMappingCfgEntity data,String riskCode,Map<String, String> channelMap,Map<String, String> riskMap,
                                         String startMonth,String endMonth,List<CountCancelDetailRateDTO> cancelDetailRateDTOList) {
        List<String> channelCodeList = Arrays.asList(StringUtil.split(data.getSubChannelCode(), "|"));
        QueryWrapper<RylCountCancelRateEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("channel_code",channelCodeList);
        if(!StringUtil.isBlank(riskCode)){
            queryWrapper.eq("main_risk_code",riskCode);
        }
        queryWrapper.apply(" DATE_FORMAT(policy_valid_month,'%Y-%m') >= "+"'"+(startMonth)+"'");
        queryWrapper.apply(" DATE_FORMAT(policy_valid_month,'%Y-%m') <= "+"'"+(endMonth)+"'");
        queryWrapper.orderByAsc("channel_code,main_risk_code,policy_valid_month,vital_date");
        List<RylCountCancelRateEntity> cancelRateList = rylCountCancelRateMapper.selectList(queryWrapper);
        if(!cancelRateList.isEmpty()){
            for(RylCountCancelRateEntity x : cancelRateList){
                String channelCode = x.getChannelCode();
                String mainRiskCode = x.getMainRiskCode();
                String policyValidMonth = DateUtil.format(x.getPolicyValidMonth(),"yyyyMMdd");
                String payIntv = x.getPayIntv().toString();

                String keyID = channelCode+mainRiskCode+policyValidMonth+payIntv ;
                if(!temp.containsKey(keyID)){
                    // 添加到目标列表的map
                    Map<String, String> nodeMap = handlerSetData(x,channelCodeList,channelMap,riskMap,startMonth,endMonth);
                    temp.put(keyID, nodeMap);
                }else{
                    Map<String, String> row = temp.get(keyID);
                    //处理T系数
                    int months = getMonths(x.getVitalDate(),x.getPolicyValidMonth());
                    BigDecimal rateValue = new BigDecimal(x.getRateValue()).multiply(new BigDecimal("100")).setScale(2);
                    row.put("T"+months,rateValue.toString()+"%");
                    if(months >= 13){
                        QueryWrapper<RylCountContinueRateEntity> rateQueryWrapper = new QueryWrapper<>();
                        rateQueryWrapper.eq("channel_code",x.getChannelCode());
                        rateQueryWrapper.eq("main_risk_code",x.getMainRiskCode());
                        rateQueryWrapper.eq("pay_intv",x.getPayIntv());
                        rateQueryWrapper.eq("rate_type",13);
                        rateQueryWrapper.apply(" DATE_FORMAT(policy_valid_month,'%Y-%m-%d') = "+"'"+(DateUtil.format(x.getPolicyValidMonth(),"yyyy-MM-dd"))+"'");
                        List<RylCountContinueRateEntity> rateList = rylCountContinueRateMapper.selectList(rateQueryWrapper);
                        if(!rateList.isEmpty()){
                            BigDecimal continueRateValue = new BigDecimal(rateList.get(0).getRateValue()).multiply(new BigDecimal("100")).setScale(2);
                            row.put("M"+months,continueRateValue.toString()+"%");
                        }
                    }
                }

                //处理明细数据
                List<CountCancelDetailRateDTO> detailRateDTOList = rylCountCancelBaseDataMapper.queryCancelDeatilList(x.getUnionKey());
                if(!detailRateDTOList.isEmpty()){
                    cancelDetailRateDTOList.addAll(detailRateDTOList);
                }
            }
        }

    }

    public Map<String,String> handlerSetData(RylCountCancelRateEntity data,List<String> channelCodeList,Map<String, String> channelMap,
                                             Map<String, String> riskMap,String startMonth,String endMonth){
        DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
        Map<String, String> nodeMap = new HashMap<>();
        String channelCode = data.getChannelCode();
        String mainRiskCode = data.getMainRiskCode();
        String policyValidMonth = DateUtil.format(data.getPolicyValidMonth(),"yyyyMMdd");
        String payIntv = data.getPayIntv().toString();
        String keyID = channelCode+mainRiskCode+policyValidMonth+payIntv ;
        nodeMap.put("keyID",keyID);
        nodeMap.put("channelName",channelMap.get(data.getChannelCode()));
        nodeMap.put("mainRiskName",riskMap.get(data.getMainRiskCode()));
        nodeMap.put("policyValidMonth",DateUtil.format(data.getPolicyValidMonth(),"yyyy-MM"));
        //汇总对应生效月的承保保费
        QueryWrapper<RylCountSettlementInitEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("channel_code",channelCodeList);
        queryWrapper.eq("op_code",1);
        queryWrapper.eq("op_sub_code",1);
        queryWrapper.ne("flags",1);
        queryWrapper.ne("flags",8);
        queryWrapper.ne("flags",12);
        queryWrapper.apply(" DATE_FORMAT(policy_valid_date,'%Y-%m') = "+"'"+(DateUtil.format(data.getPolicyValidMonth(),"yyyy-MM"))+"'");
        List<RylCountSettlementInitEntity> initList = rylCountSettlementInitMapper.selectList(queryWrapper);
        BigDecimal firstMioAmnt = initList.stream().map(RylCountSettlementInitEntity::getMioAmnt).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
        nodeMap.put("firstMioAmnt",decimalFormat.format(firstMioAmnt));
        //汇总手续费
        QueryWrapper<RylCountSettlementResult> resultQueryWrapper = new QueryWrapper<>();
        resultQueryWrapper.in("channel_code",channelCodeList);
        resultQueryWrapper.eq("op_code",1);
        resultQueryWrapper.eq("op_sub_code",1);
        resultQueryWrapper.ne("rate_type",14);
        resultQueryWrapper.ne("flags",1);
        resultQueryWrapper.ne("flags",8);
        resultQueryWrapper.ne("flags",12);
        resultQueryWrapper.apply(" DATE_FORMAT(policy_valid_date,'%Y-%m') = "+"'"+(DateUtil.format(data.getPolicyValidMonth(),"yyyy-MM"))+"'");
        List<RylCountSettlementResult> resultList = rylCountSettResultMapper.selectList(resultQueryWrapper);
        BigDecimal commissionCharge = resultList.stream().map(RylCountSettlementResult::getCommissionCharge).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
        nodeMap.put("commissionCharge",decimalFormat.format(commissionCharge));
        //处理T系数
        int months = getMonths(data.getVitalDate(),data.getPolicyValidMonth());
        BigDecimal rateValue = new BigDecimal(data.getRateValue()).multiply(new BigDecimal("100")).setScale(2);
        nodeMap.put("T"+months,rateValue.toString()+"%");
        if(months >= 13){
            QueryWrapper<RylCountContinueRateEntity> rateQueryWrapper = new QueryWrapper<>();
            rateQueryWrapper.eq("channel_code",data.getChannelCode());
            rateQueryWrapper.eq("main_risk_code",data.getMainRiskCode());
            rateQueryWrapper.eq("pay_intv",data.getPayIntv());
            rateQueryWrapper.eq("rate_type",13);
            rateQueryWrapper.apply(" DATE_FORMAT(policy_valid_month,'%Y-%m-%d') = "+"'"+(DateUtil.format(data.getPolicyValidMonth(),"yyyy-MM-dd"))+"'");
            List<RylCountContinueRateEntity> rateList = rylCountContinueRateMapper.selectList(rateQueryWrapper);
            if(!rateList.isEmpty()){
                BigDecimal continueRateValue = new BigDecimal(rateList.get(0).getRateValue()).multiply(new BigDecimal("100")).setScale(2);
                nodeMap.put("M"+months,continueRateValue.toString()+"%");
            }
        }
        //协议约定继续率暂不处理
        nodeMap.put("X1","");
        //本期应结手续费(生效月-至今的继续率补偿金)
        QueryWrapper<RylCountSettlementResult> rQueryWrapper = new QueryWrapper<>();
        rQueryWrapper.in("channel_code",channelCodeList);
        rQueryWrapper.eq("op_code",1);
        rQueryWrapper.eq("op_sub_code",1);
        rQueryWrapper.eq("rate_type",14);
        rQueryWrapper.apply(" DATE_FORMAT(policy_valid_date,'%Y-%m') >= "+"'"+(startMonth)+"'");
        rQueryWrapper.apply(" DATE_FORMAT(policy_valid_date,'%Y-%m') <= "+"'"+(endMonth)+"'");
        List<RylCountSettlementResult> resultSettList = rylCountSettResultMapper.selectList(resultQueryWrapper);
        BigDecimal commission = resultSettList.stream().map(RylCountSettlementResult::getCommissionCharge).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
        nodeMap.put("commission",decimalFormat.format(commission));
        //结算月
        nodeMap.put("settMonth",endMonth);
        return nodeMap;
    }

    private String getStartMonth(String endMonth) {
        LocalDate date = LocalDate.parse(endMonth + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate previousMonth = date.minusMonths(13);
        String startMonth = previousMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        return startMonth;
    }

    private Integer getMonths(Date endMonth,Date startMonth) {
        LocalDate startDate = LocalDate.parse(DateUtil.format(startMonth,"yyyy-MM-dd"), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate endDate = LocalDate.parse(DateUtil.format(endMonth,"yyyy-MM-dd"), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        Period period = Period.between(startDate, endDate);
        int months = (int) period.toTotalMonths();
        if (months == 0 && !startDate.equals(endDate)) {
            months = 1;
        }else{
            months++;
        }
        return months;
    }

    @Override
    public BaseResponse uploadContnoFile(MultipartFile file1,MultipartFile file2,MultipartFile file3,MultipartFile file4,MultipartFile file5) throws Exception {
        InputStream inputStream = null;
        FileUploadOutDto fileListDTO = null;
        if (file1 != null && !file1.isEmpty()) {
            inputStream = file1.getInputStream();
            fileListDTO = fileUpoadUtil.uploadCountnoFile(inputStream, file1.getOriginalFilename(), FileUploadAddrConstant.PROTOCOL_ADDR);
            if (fileListDTO == null) {
                return BaseResponse.fail("-1", "结算单上传文件失败");
            }
        }
        if (file2 != null && !file2.isEmpty()) {
            inputStream = file2.getInputStream();
            fileListDTO = fileUpoadUtil.uploadCountnoFile(inputStream, file2.getOriginalFilename(), FileUploadAddrConstant.PROTOCOL_ADDR);
            if (fileListDTO == null) {
                return BaseResponse.fail("-1", "结算单上传文件失败");
            }
        }
        if (file3 != null && !file3.isEmpty()) {
            inputStream = file3.getInputStream();
            fileListDTO = fileUpoadUtil.uploadCountnoFile(inputStream, file3.getOriginalFilename(), FileUploadAddrConstant.PROTOCOL_ADDR);
            if (fileListDTO == null) {
                return BaseResponse.fail("-1", "结算单上传文件失败");
            }
        }
        if (file4 != null && !file4.isEmpty()) {
            inputStream = file4.getInputStream();
            fileListDTO = fileUpoadUtil.uploadCountnoFile(inputStream, file4.getOriginalFilename(), FileUploadAddrConstant.PROTOCOL_ADDR);
            if (fileListDTO == null) {
                return BaseResponse.fail("-1", "结算单上传文件失败");
            }
        }
        if (file5 != null && !file5.isEmpty()) {
            inputStream = file5.getInputStream();
            fileListDTO = fileUpoadUtil.uploadCountnoFile(inputStream, file5.getOriginalFilename(), FileUploadAddrConstant.PROTOCOL_ADDR);
            if (fileListDTO == null) {
                return BaseResponse.fail("-1", "结算单上传文件失败");
            }
        }

        return  BaseResponse.success();
    }

    @Override
    public BaseResponse queryExceptionData(ExceptionReq exceptionReq) {
        Page<TCountExceptionDataLogEntity> page = new Page<>(exceptionReq.getCurrent(),exceptionReq.getSize());
        List<ExceptionRes> list = new ArrayList<>();
        log.info("异常数据拦截查询,查询{}",exceptionReq);
        IPage<TCountExceptionDataLogEntity> records = countExceptionDataLogMapper.selectPage(page,new QueryWrapper<TCountExceptionDataLogEntity>()
                .eq(StringUtils.isNotBlank(exceptionReq.getDataNo()),"data_no",exceptionReq.getDataNo())
                .eq(StringUtils.isNotBlank(exceptionReq.getDataType()),"data_type",exceptionReq.getDataType())
                .eq(StringUtils.isNotBlank(exceptionReq.getStatus()),"status",exceptionReq.getStatus()));
        records.getRecords().forEach(log->{
            ExceptionRes res = new ExceptionRes();
            BeanUtils.copyProperties(log,res);
            res.setStatus(BusiUtils.getNameByCode("data_status",log.getStatus().toString()));
            res.setDataType(BusiUtils.getNameByCode("data_type",log.getDataType()));
            list.add(res);
        });
        IPage<ExceptionRes> resPage = new Page<>();
        BeanUtils.copyProperties(records,resPage);
        log.info("异常数据拦截查询,数量{}",resPage.getTotal());
        return BaseResponse.success(resPage.setRecords(list));
    }

    @Override
    public void exportUnsettledAmount(RylCountReportDto rylCountReportDto) {
        Map<String, String> channelMap = Maps.newHashMap();
        Map<String, String> message = Maps.newHashMap();
        List<String> channelCodeList = new ArrayList<>();
        message.put("queryParam", JSONObject.toJSONString(rylCountReportDto));
        FileUploadOutDto fileUploadOutDto = null;
        ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
        singleThreadExecutor.execute(new Runnable() {
            @Override
            public void run() {
                unsettledExcelList(rylCountReportDto, fileUploadOutDto, message);
            }
        });
        singleThreadExecutor.shutdown();
    }

    @SneakyThrows
    public void unsettledExcelList(RylCountReportDto rylCountReportDto,FileUploadOutDto fileUploadOutDto, Map<String, String> message) {
        long time = System.currentTimeMillis();
        Long threadId = Thread.currentThread().getId();
        QueryWrapper<RylCountUnsettledAmountLogEntity> queryWrapper = new QueryWrapper<>();
        if(!rylCountReportDto.getDeptCodeList().isEmpty()){
            queryWrapper.in("dep_code",rylCountReportDto.getDeptCodeList());
        }
        if(!rylCountReportDto.getSettlementOperatorList().isEmpty()){
            queryWrapper.in("settlement_operator",rylCountReportDto.getSettlementOperatorList());
        }
        if(!rylCountReportDto.getSettlementTypeList().isEmpty()){
            queryWrapper.in("settlement_type",rylCountReportDto.getSettlementTypeList());
        }
        if(!rylCountReportDto.getChannelSettlementStatusList().isEmpty()){
            queryWrapper.in("channel_settlement_status",rylCountReportDto.getChannelSettlementStatusList());
        }
        if(StringUtil.isBlank(rylCountReportDto.getStartMonth()) && StringUtil.isBlank(rylCountReportDto.getEndMonth())){
            rylCountReportDto.setStartMonth("2023-01");
            rylCountReportDto.setEndMonth(DateUtil.format(new Date(), "yyyy-MM"));
        }
        queryWrapper.ge("busi_month", rylCountReportDto.getStartMonth());
        queryWrapper.le("busi_month", rylCountReportDto.getEndMonth());
        List<RylCountUnsettledAmountLogEntity> unsettLogList = rylCountUnsettledAmountLogMapper.selectList(queryWrapper);
        if (unsettLogList.isEmpty()) {
            return;
        }
        String fileName = "可结未付数据表-" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        String threadName = Thread.currentThread().getName();
        message.put("threadId", String.valueOf(threadId));
        message.put("fileName", fileName);
        SendMessageEvent sendMessageEvent = new SendMessageEvent(message);
        sendMessageEvent.setDataType(UNSETTLED_REPORT_REPROT);
        sendMessageEvent.setOperate(0);
        String unionId = UUID.randomUUID() + "-" + threadId;
        sendMessageEvent.setUnionId(unionId);
        applicationEventPublisher.publishEvent(sendMessageEvent);
        log.info("当前线程名称:{} 当前线程Id:{}", threadName, threadId);
        List<Map<String, Object>> mainTemp = new ArrayList<>();
        List<String> channelCodeList = unsettLogList.stream().map(RylCountUnsettledAmountLogEntity::getChannelCode).distinct().collect(Collectors.toList());
        for(String x : channelCodeList){
            countUnsettledSchedule(mainTemp,x,unsettLogList,rylCountReportDto.getStartMonth(),rylCountReportDto.getEndMonth());
        }
        if(mainTemp.isEmpty()){
            //无数据提示导出失败
            updateLog(unionId, fileUploadOutDto, UNSETTLED_REPORT_REPROT);
            return;
        }
        mainTemp = mainTemp.stream()
                .sorted(Comparator.comparingInt(m -> new BigDecimal(m.get("sumMoney").toString()).intValue()))
                .collect(Collectors.toList());
        try{
            SXSSFWorkbook workbook = new SXSSFWorkbook();
            CellStyle cellStyle = cellContentStyle(workbook);
            SXSSFSheet sheetBaseCharge = workbook.createSheet("可结未付清单");
            //表头格式
            CellStyle titleStyle = cellHandContentStyle(workbook);
            CellStyle borderStyle = workbook.createCellStyle();
            borderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            borderStyle.setBorderBottom(BorderStyle.THIN);
            borderStyle.setBorderLeft(BorderStyle.THIN);
            borderStyle.setBorderRight(BorderStyle.THIN);
            borderStyle.setBorderTop(BorderStyle.THIN);
            //提示行内容格式
            CellStyle hintStyle = workbook.createCellStyle();
            hintStyle.setAlignment(HorizontalAlignment.LEFT);
            hintStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
            hintStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            hintStyle.setBorderBottom(BorderStyle.THIN);
            hintStyle.setBorderLeft(BorderStyle.THIN);
            hintStyle.setBorderRight(BorderStyle.THIN);
            hintStyle.setBorderTop(BorderStyle.THIN);
            sheetBaseCharge.setDefaultColumnWidth(28);

            SXSSFRow titleRow = sheetBaseCharge.createRow(0);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue("可结未付数据报表");
            titleCell.setCellStyle(titleStyle);

            SXSSFRow hintRow2 = sheetBaseCharge.createRow(1);
            Cell hintCell2 = hintRow2.createCell(0);
            hintCell2.setCellValue("1、单元格中显示的手续费仅为可结未付的，0表示手续费为0，显示'/'表示无可结数据");
            hintCell2.setCellStyle(hintStyle);

            SXSSFRow hintRow3 = sheetBaseCharge.createRow(2);
            Cell hintCell3 = hintRow3.createCell(0);
            hintCell3.setCellValue("2、该报表每天跑批一次。");
            hintCell3.setCellStyle(hintStyle);

            SXSSFRow headRow = sheetBaseCharge.createRow(3);
            Map<String, String> headName = getRhUnsettledListMap(rylCountReportDto.getStartMonth(),rylCountReportDto.getEndMonth());
            int[] cells = {0};
            headName.forEach((key, value) -> {
                SXSSFCell headCell = headRow.createCell(cells[0]);
                headCell.setCellStyle(cellHeadStyle(workbook,"UNSETTLED"));
                headCell.setCellValue(value);
                cells[0]++;
            });
            int lastCellNum = headRow.getLastCellNum();
            //合并处理
            sheetBaseCharge.addMergedRegion(new CellRangeAddress(0, 0, 0, lastCellNum));
            sheetBaseCharge.addMergedRegion(new CellRangeAddress(1, 1, 0, lastCellNum));
            sheetBaseCharge.addMergedRegion(new CellRangeAddress(2, 2, 0, lastCellNum));
            for(int i = 0; i < 3; i++){
                SXSSFRow cell = sheetBaseCharge.getRow(i);
                cell.setRowStyle(borderStyle);
            }

            int textRows = 4;
            for (Map<String, Object> baseData : mainTemp) {
                SXSSFRow textRow = sheetBaseCharge.createRow(textRows);
                int[] textCells = {0};
                headName.forEach((key, value) -> {
                    SXSSFCell textCell = textRow.createCell(textCells[0]);
                    textCell.setCellStyle(cellStyle);
                    if (baseData.get(key) != null && (baseData.get(key) instanceof BigDecimal)) {
                        //如果是BigDecimal，则以数字格式输出，并且设置单元格为可以计算的格式
                        textCell.setCellValue(((BigDecimal) baseData.get(key)).doubleValue());
                        cellStyle.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));
                        if(new BigDecimal(baseData.get(key).toString()).compareTo(BigDecimal.ZERO) < 0){
                            CellStyle style = workbook.createCellStyle();
                            Font font1 = workbook.createFont();
                            font1.setBold(true);
                            font1.setFontName("宋体");
                            font1.setFontHeightInPoints((short) 12);//设置字体大小
                            font1.setColor(IndexedColors.RED.getIndex());
                            style.setFont(font1);
                            style.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));
                            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                            style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
                            style.setBorderBottom(BorderStyle.THIN);
                            style.setBorderLeft(BorderStyle.THIN);
                            style.setBorderRight(BorderStyle.THIN);
                            style.setBorderTop(BorderStyle.THIN);
                            textCell.setCellStyle(style);
                        }
                    } else {
                        textCell.setCellValue(baseData.get(key) == null ? "" : baseData.get(key).toString());
                    }
                    textCells[0]++;
                });
                textRows++;
            }
            ByteArrayOutputStream bufferedOutPut = new ByteArrayOutputStream();
            bufferedOutPut.flush();
            workbook.write(bufferedOutPut);
            bufferedOutPut.close();
            ByteArrayInputStream swapStream = new ByteArrayInputStream(bufferedOutPut.toByteArray());
            fileUploadOutDto = fileUpoadUtil.uploadN(swapStream, fileName, CONTNUE_RATE_REPROT_ADDR);
            log.info("可结未付数据表导出，查询明细上传oss结束，耗时:{}", System.currentTimeMillis() - time);
            updateLog(unionId, fileUploadOutDto, UNSETTLED_REPORT_REPROT);
        }catch (Exception e){
            Map<String, String> map = new HashMap<>(2);
            map.put("status", "failure");
            map.put("message", "上传文件失败" + e.getMessage());
            updateLog(unionId, fileUploadOutDto, UNSETTLED_REPORT_REPROT);
            log.error("error-message:{}", e);
        }
    }

    private void countUnsettledSchedule(List<Map<String, Object>> nodeMap, String channelCode,List<RylCountUnsettledAmountLogEntity> unsettLogList,String startMonth,String endMonth) {
        List<RylCountUnsettledAmountLogEntity> logList = unsettLogList.stream().filter(x-> x.getChannelCode().equals(channelCode)).sorted(Comparator.comparing(RylCountUnsettledAmountLogEntity::getBusiMonth)).collect(Collectors.toList());
        if(!logList.isEmpty()){
            List<String> manageComList = logList.stream().filter(x-> !StringUtil.isBlank(x.getManageCom())).map(RylCountUnsettledAmountLogEntity::getManageCom).distinct().collect(Collectors.toList());
            for(String manageCom : manageComList){
                List<RylCountUnsettledAmountLogEntity> manageLogList = logList.stream().filter(x-> x.getManageCom().equals(manageCom)).collect(Collectors.toList());
                Map<String, Object> temp = new HashMap<>();
                RylCountUnsettledAmountLogEntity logEntity = manageLogList.get(0);
                if(!StringUtil.isBlank(logEntity.getDepName())){
                    temp.put("depName",logEntity.getDepName());
                }
                if(!StringUtil.isBlank(logEntity.getChannelName())){
                    temp.put("channelName",logEntity.getChannelName());
                }
                if(!StringUtil.isBlank(logEntity.getSettlementType())){
                    if("1".equals(logEntity.getSettlementType())){
                        temp.put("settlementType","服务费");
                    }else{
                        temp.put("settlementType","手续费");
                    }
                }
                if(!StringUtil.isBlank(logEntity.getChannelSettlementStatus())){
                    temp.put("channelSettlementStatus",logEntity.getChannelSettlementStatus());
                }
                if(!StringUtil.isBlank(logEntity.getSettlementIntv())){
                    temp.put("settlementIntv",logEntity.getSettlementIntv());
                }
                if(!StringUtil.isBlank(logEntity.getSettlementOperator())){
                    temp.put("settlementOperator",logEntity.getSettlementOperator());
                }
                if(logEntity.getIsRylSettlement() != null){
                    if(0 == logEntity.getIsRylSettlement()){
                        temp.put("isRylSettlement","是");
                    }else{
                        temp.put("isRylSettlement","否");
                    }
                }
                temp.put("manageCom",CommonConstant.MANAGECOM_CONSTANTS.get("managecom",manageCom));
                BigDecimal commissionCharge = manageLogList.stream().map(RylCountUnsettledAmountLogEntity::getSumCommissionCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                temp.put("sumMoney",commissionCharge);
                List<String> monthList = getMonthBetweenDate(startMonth,endMonth);
                for(String x : monthList){
                    LocalDate date = LocalDate.parse(x+"-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    String formattedDate = date.format(DateTimeFormatter.ofPattern("yyyy年M月"));
                    List<RylCountUnsettledAmountLogEntity> logEntityList = manageLogList.stream().filter(a-> a.getBusiMonth().equals(x)).collect(Collectors.toList());
                    if(!logEntityList.isEmpty()){
                        temp.put(formattedDate,logEntityList.get(0).getSumCommissionCharge());
                    }else{
                        temp.put(formattedDate,"/");
                    }
                }
                nodeMap.add(temp);
            }
        }
    }

    private Map<String, String> getRhUnsettledListMap(String startMonth,String endMonth) {
        Map<String, String> aliasMap = new LinkedHashMap<>();
        aliasMap.put("depName", "部门");
        aliasMap.put("channelName", "渠道");
        aliasMap.put("settlementType", "分类");
        aliasMap.put("channelSettlementStatus", "渠道状态");
        aliasMap.put("isRylSettlement", "是否容易联自助结算");
        aliasMap.put("settlementIntv", "结算频次");
        aliasMap.put("settlementOperator", "负责人");
        aliasMap.put("manageCom", "管理机构");
        aliasMap.put("sumMoney", "总合计手续费");
        List<String> monthList = getMonthBetweenDate(startMonth,endMonth);
        for(String x : monthList){
            LocalDate date = LocalDate.parse(x+"-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String formattedDate = date.format(DateTimeFormatter.ofPattern("yyyy年M月"));
            aliasMap.put(formattedDate, formattedDate);
        }
        return aliasMap;
    }

    //定义： 内容 样式
    private CellStyle cellContentStyle(SXSSFWorkbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 12);//设置字体大小
        cellStyle.setFont(font);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        return cellStyle;
    }

    //定义：标题行 样式
    private CellStyle cellHeadStyle(SXSSFWorkbook workbook,String code) {
        CellStyle cellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 12);//设置字体大小
        if(!StringUtil.isBlank(code)){
            font.setColor(IndexedColors.WHITE1.getIndex());
        }
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        if(!StringUtil.isBlank(code)){
            cellStyle.setFillForegroundColor(IndexedColors.BLUE.getIndex());// 设置背景色
        }else{
            cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());// 设置背景色
        }
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return cellStyle;
    }

    @Override
    public void exportUnsettledDetail(RylCountReportDto rylCountReportDto) {
        Map<String, String> channelMap = Maps.newHashMap();
        Map<String, String> message = Maps.newHashMap();
        List<String> channelCodeList = new ArrayList<>();
        message.put("queryParam", JSONObject.toJSONString(rylCountReportDto));
        FileUploadOutDto fileUploadOutDto = null;
        ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
        singleThreadExecutor.execute(new Runnable() {
            @Override
            public void run() {
                unsettledDetailExcelList(rylCountReportDto, fileUploadOutDto, message);
            }
        });
        singleThreadExecutor.shutdown();
    }

    @SneakyThrows
    public void unsettledDetailExcelList(RylCountReportDto rylCountReportDto,FileUploadOutDto fileUploadOutDto, Map<String, String> message) {
        long time = System.currentTimeMillis();
        Long threadId = Thread.currentThread().getId();
        String fileName;
        if(!StringUtil.isBlank(rylCountReportDto.getStartDate()) && !StringUtil.isBlank(rylCountReportDto.getEndDate())){
            fileName = "可结未付明细"+rylCountReportDto.getStartDate()+"-"+rylCountReportDto.getEndDate()+".xlsx";
        }else {
            fileName = "可结未付明细-全量.xlsx";
        }
        if(StringUtil.isBlank(rylCountReportDto.getStartDate()) && StringUtil.isBlank(rylCountReportDto.getEndDate())){
            rylCountReportDto.setStartDate("2023-01-01");
            rylCountReportDto.setEndDate(DateUtil.format(new Date(), "yyyy-MM-dd"));
        }
        List<String> dayList = getDayBetweenDate(rylCountReportDto.getStartDate(),rylCountReportDto.getEndDate());
        if(dayList.isEmpty()){
            return;
        }

        String threadName = Thread.currentThread().getName();
        message.put("threadId", String.valueOf(threadId));
        message.put("fileName", fileName);
        SendMessageEvent sendMessageEvent = new SendMessageEvent(message);
        sendMessageEvent.setDataType(UNSETTLED_DETAIL_REPORT_REPROT);
        sendMessageEvent.setOperate(0);
        String unionId = UUID.randomUUID() + "-" + threadId;
        sendMessageEvent.setUnionId(unionId);
        applicationEventPublisher.publishEvent(sendMessageEvent);
        log.info("当前线程名称:{} 当前线程Id:{}", threadName, threadId);
        if (riskMap.size() < 1) {
            riskMap = rylCountSettlementRateMapper.getRiskList("").stream()
                    .collect(Collectors.toMap(RiskDTO::getRiskCode, RiskDTO::getRiskName));
        }
        if (channelMap.size() < 1) {
            LambdaQueryWrapper<RylCountSettlementChannelCfgEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(RylCountSettlementChannelCfgEntity::getIsDel, "0");
            List<RylCountSettlementChannelCfgEntity> channelList =
                    rylCountSettlementChannelCfgMapper.selectList(lambdaQueryWrapper);
            List<RylCountSettlementChannelCfgEntity> channelRepeatList = channelList.stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(RylCountSettlementChannelCfgEntity::getChannelCode))), ArrayList::new)
            );
            channelMap = channelRepeatList.stream().collect(Collectors.toMap(RylCountSettlementChannelCfgEntity::getChannelCode, RylCountSettlementChannelCfgEntity::getChannelName));
        }
        List<RylCountReportResDto> mainALLList = new ArrayList<>();
        for(String day : dayList){
            queryResultDetail(day,rylCountReportDto,mainALLList,riskMap,channelMap);
        }
        if(mainALLList.isEmpty()){
            //无数据提示导出失败
            updateLog(unionId, fileUploadOutDto, UNSETTLED_DETAIL_REPORT_REPROT);
            return;
        }
        @Cleanup
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ExcelWriter excelWriter= EasyExcel.write(os).build();
        try{
            //表头样式策略
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            //设置表头居中对齐
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            //表头前景设置黑色
            headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setBold(true);
            headWriteFont.setFontName("宋体");
            headWriteFont.setFontHeightInPoints((short) 14);
            headWriteCellStyle.setWriteFont(headWriteFont);
            //内容样式策略策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            // 设置背景颜色白色
            contentWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
            // 设置垂直居中为居中对齐
            contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            // 设置左右对齐为靠左对齐
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            // 设置单元格上下左右边框为细边框
            contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
            contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
            contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
            contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
            //创建字体对象
            WriteFont contentWriteFont = new WriteFont();
            //内容字体大小
            contentWriteFont.setFontName("宋体");
            contentWriteFont.setFontHeightInPoints((short) 12);
            contentWriteCellStyle.setWriteFont(contentWriteFont);
            // 初始化表格样式
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            WriteSheet writeSheet = EasyExcel.writerSheet("可结未付明细数据清单").registerWriteHandler(horizontalCellStyleStrategy).head(RylCountReportResDto.class).build();
            excelWriter.write(mainALLList, writeSheet);

            excelWriter.finish();
            byte[] content = os.toByteArray();
            InputStream is = new ByteArrayInputStream(content);
            fileUploadOutDto = fileUpoadUtil.uploadN(is, fileName, CONTNUE_RATE_REPROT_ADDR);
            log.info("结算中心--》可结未付明细数据清单报表导出，查询明细上传oss结束，耗时:{}", System.currentTimeMillis() - time);
            updateLog(unionId, fileUploadOutDto, UNSETTLED_DETAIL_REPORT_REPROT);
        } catch (Exception e) {
            // 重置response
            Map<String, String> map = new HashMap<>(2);
            map.put("status", "failure");
            map.put("message", "上传文件失败" + e.getMessage());
            updateLog(unionId, fileUploadOutDto, UNSETTLED_DETAIL_REPORT_REPROT);
            log.error("error-message:{}", e);
        } finally {
            os.flush();
        }
    }

    private Map<String, String> getRhCountResultDetailListMap() {
        Map<String, String> aliasMap = new LinkedHashMap<>();
        aliasMap.put("channelCode", "渠道编码");
        aliasMap.put("channelName", "渠道名称");
        aliasMap.put("departmentCode", "部门编码");
        aliasMap.put("departmentName", "部门名称");
        aliasMap.put("policyNo", "保单号");
        aliasMap.put("planName", "销售名称");
        aliasMap.put("mainRiskName", "主险名称");
        aliasMap.put("riskName", "险种名称");
        aliasMap.put("opName", "交易类型");
        aliasMap.put("premium", "标准保费");
        aliasMap.put("mioAmnt", "实收付保费");
        aliasMap.put("plnAmnt", "应收保费");
        aliasMap.put("businessApplyDate", "业务申请日期");
        aliasMap.put("businessDate", "业务生效日期");
        aliasMap.put("signDate", "签单日期");
        aliasMap.put("signDateM", "签单月");
        aliasMap.put("valiDate", "保单生效日期");
        aliasMap.put("payToDate", "应收日期");
        aliasMap.put("mioDate", "实收付日期");
        aliasMap.put("callbackFlag", "回访标识");
        aliasMap.put("receiptFlag", "回执标志");
        aliasMap.put("doubleRecordFlag", "双录状态");
        aliasMap.put("flags", "保单退保状态");
        aliasMap.put("payIntv", "缴费方式");
        aliasMap.put("payEndYear", "缴费年期");
        aliasMap.put("payCount", "缴费次数");
        aliasMap.put("baoDanNianDu", "保单年度");
        aliasMap.put("insuYear", "保障期间");
        aliasMap.put("firstInsuredApplyAge", "被保人投保年龄");
        aliasMap.put("appName", "投保人姓名");
        aliasMap.put("rateEl", "计算公式");
        aliasMap.put("rateType", "费率类型");
        aliasMap.put("rateValue", "费率");
        aliasMap.put("commissionCharge", "手续费");
        aliasMap.put("manageCom", "管理机构");
        aliasMap.put("agentCode", "代理人编码");
        aliasMap.put("agentName", "代理人姓名");
        aliasMap.put("getYear", "年金领取时间");
        aliasMap.put("settleStatus", "结算状态");
        aliasMap.put("settleStatusReason", "结算状态原因");
        aliasMap.put("selfMutual", "自互保标识");
        aliasMap.put("selfSell", "业务模式");
        aliasMap.put("countRemark", "备注");
        aliasMap.put("ensureYear", "保证年期");
        aliasMap.put("ensureYearFlag", "保证年期单位");
        return aliasMap;
    }

    public static String desensitize(String name) {
        String OVERLAY = "*";
        int START = 1;
        if (StringUtils.isEmpty(name)) {
            return "";
        }
        if (name.length() == 1 || name.length() == 2) {
            return StringUtils.overlay(name, OVERLAY, START, name.length());
        }
        int END = name.length() - 1;
        return StringUtils.overlay(name, OVERLAY, START, END);
    }

    @Override
    public void exportNewSchedule(RylCountReportDto rylCountReportDto) {
        Map<String, String> channelMap = Maps.newHashMap();
        Map<String, String> message = Maps.newHashMap();
        List<String> channelCodeList = new ArrayList<>();
        message.put("queryParam", JSONObject.toJSONString(rylCountReportDto));
        FileUploadOutDto fileUploadOutDto = null;
        ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
        singleThreadExecutor.execute(new Runnable() {
            @Override
            public void run() {
                newScheduleExcelList(rylCountReportDto, fileUploadOutDto, message);
            }
        });
        singleThreadExecutor.shutdown();
    }

    @SneakyThrows
    public void newScheduleExcelList(RylCountReportDto rylCountReportDto,FileUploadOutDto fileUploadOutDto, Map<String, String> message) {
        long time = System.currentTimeMillis();
        Long threadId = Thread.currentThread().getId();
        QueryWrapper<RylCountSettNewScheduleEntity> queryWrapper = new QueryWrapper<>();
        if(!rylCountReportDto.getDeptCodeList().isEmpty()){
            queryWrapper.in("dep_code",rylCountReportDto.getDeptCodeList());
        }
        if(!rylCountReportDto.getSettlementOperatorList().isEmpty()){
            queryWrapper.in("settlement_operator",rylCountReportDto.getSettlementOperatorList());
        }
        if(!rylCountReportDto.getSettlementTypeList().isEmpty()){
            queryWrapper.in("settlement_type",rylCountReportDto.getSettlementTypeList());
        }
        if(!rylCountReportDto.getChannelSettlementStatusList().isEmpty()){
            queryWrapper.in("channel_settlement_status",rylCountReportDto.getChannelSettlementStatusList());
        }
        String fileName = "";
        if (StringUtils.isEmpty(rylCountReportDto.getStartMonth()) && StringUtils.isEmpty(rylCountReportDto.getEndMonth())){
            queryWrapper.ge("busi_month", "2023-01");
            String formattedDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
            fileName = "结算进度表2023-01-"+formattedDate+".xlsx";
            rylCountReportDto.setStartMonth("2023-01-01");
            rylCountReportDto.setEndMonth(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }else {
            queryWrapper.ge("busi_month", rylCountReportDto.getStartMonth());
            queryWrapper.le("busi_month", rylCountReportDto.getEndMonth());
            fileName = "结算进度表"+rylCountReportDto.getStartMonth()+"-"+rylCountReportDto.getEndMonth()+".xlsx";
        }
        List<RylCountSettNewScheduleEntity> logList = rylCountSettNewScheduleMapper.selectList(queryWrapper);
        if (logList.isEmpty()) {
            return;
        }
        String threadName = Thread.currentThread().getName();
        message.put("threadId", String.valueOf(threadId));
        message.put("fileName", fileName);
        SendMessageEvent sendMessageEvent = new SendMessageEvent(message);
        sendMessageEvent.setDataType(NEWSCHEDULE_REPORT_REPROT);
        sendMessageEvent.setOperate(0);
        String unionId = UUID.randomUUID() + "-" + threadId;
        sendMessageEvent.setUnionId(unionId);
        applicationEventPublisher.publishEvent(sendMessageEvent);
        log.info("当前线程名称:{} 当前线程Id:{}", threadName, threadId);
        List<Map<String, Object>> mainTemp = new ArrayList<>();
        List<String> channelCodeList = logList.stream().map(RylCountSettNewScheduleEntity::getChannelCode).distinct().collect(Collectors.toList());
        for(String x : channelCodeList){
            countNewSchedule(mainTemp,x,logList,rylCountReportDto.getStartMonth(),rylCountReportDto.getEndMonth());
        }
        if(mainTemp.isEmpty()){
            //无数据提示导出失败
            updateLog(unionId, fileUploadOutDto, NEWSCHEDULE_REPORT_REPROT);
            return;
        }
        try{
            SXSSFWorkbook workbook = new SXSSFWorkbook();
            CellStyle cellStyle = cellContentStyle(workbook);
            SXSSFSheet sheetBaseCharge = workbook.createSheet("结算进度明细");
            //表头格式
            CellStyle titleStyle = cellHandContentStyle(workbook);
            CellStyle borderStyle = workbook.createCellStyle();
            borderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            borderStyle.setBorderBottom(BorderStyle.THIN);
            borderStyle.setBorderLeft(BorderStyle.THIN);
            borderStyle.setBorderRight(BorderStyle.THIN);
            borderStyle.setBorderTop(BorderStyle.THIN);
            //提示行内容格式
            CellStyle hintStyle = workbook.createCellStyle();
            hintStyle.setAlignment(HorizontalAlignment.LEFT);
            hintStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
            hintStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            hintStyle.setBorderBottom(BorderStyle.THIN);
            hintStyle.setBorderLeft(BorderStyle.THIN);
            hintStyle.setBorderRight(BorderStyle.THIN);
            hintStyle.setBorderTop(BorderStyle.THIN);
            sheetBaseCharge.setDefaultColumnWidth(28);

            SXSSFRow titleRow = sheetBaseCharge.createRow(0);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue("结算进度表");
            titleCell.setCellStyle(titleStyle);

            SXSSFRow hintRow2 = sheetBaseCharge.createRow(1);
            Cell hintCell2 = hintRow2.createCell(0);
            hintCell2.setCellValue("1、如单元格标底色是蓝色手续费指已付的，即已向渠道打款；如单元无底色，手续费指可结未付的，可能在对账或费控过程中；");
            hintCell2.setCellStyle(hintStyle);

            SXSSFRow hintRow3 = sheetBaseCharge.createRow(2);
            Cell hintCell3 = hintRow3.createCell(0);
            hintCell3.setCellValue("2、日期指业务月份，手续费实付时间与业务月份有1-2个月的时间差；");
            hintCell3.setCellStyle(hintStyle);

            SXSSFRow hintRow4 = sheetBaseCharge.createRow(3);
            Cell hintCell4 = hintRow4.createCell(0);
            hintCell4.setCellValue("3、缓结手续费所在月在后续付款后，会更新其所在月的手续费。比如24年1月有一新单1万元缓结，在5月结算后，24年1月的手续费会增加1万。");
            hintCell4.setCellStyle(hintStyle);

            SXSSFRow hintRow5 = sheetBaseCharge.createRow(4);
            Cell hintCell5 = hintRow5.createCell(0);
            hintCell5.setCellValue("4、该报表每天早上跑批一次。");
            hintCell5.setCellStyle(hintStyle);

            SXSSFRow headRow = sheetBaseCharge.createRow(5);
            Map<String, String> headName = getNewUnsettledListMap(rylCountReportDto.getStartMonth(),rylCountReportDto.getEndMonth());
            int[] cells = {0};
            headName.forEach((key, value) -> {
                SXSSFCell headCell = headRow.createCell(cells[0]);
                headCell.setCellStyle(cellHeadStyle(workbook,"UNSETTLED"));
                headCell.setCellValue(value);
                cells[0]++;
            });
            int lastCellNum = headRow.getLastCellNum();
            //合并处理
            sheetBaseCharge.addMergedRegion(new CellRangeAddress(0, 0, 0, lastCellNum));
            sheetBaseCharge.addMergedRegion(new CellRangeAddress(1, 1, 0, lastCellNum));
            sheetBaseCharge.addMergedRegion(new CellRangeAddress(2, 2, 0, lastCellNum));
            sheetBaseCharge.addMergedRegion(new CellRangeAddress(3, 3, 0, lastCellNum));
            sheetBaseCharge.addMergedRegion(new CellRangeAddress(4, 4, 0, lastCellNum));
            for(int i = 0; i < 5; i++){
                SXSSFRow cell = sheetBaseCharge.getRow(i);
                cell.setRowStyle(borderStyle);
            }

            int textRows = 6;
            for (Map<String, Object> baseData : mainTemp) {
                SXSSFRow textRow = sheetBaseCharge.createRow(textRows);
                int[] textCells = {0};
                headName.forEach((key, value) -> {
                    SXSSFCell textCell = textRow.createCell(textCells[0]);
                    textCell.setCellStyle(cellStyle);
                    if(key.contains("年") && key.contains("月")){
                        String commission = baseData.get(key).toString().substring(0, baseData.get(key).toString().lastIndexOf('|'));
                        BigDecimal commissionCharge = new BigDecimal(commission);
                        textCell.setCellValue((commissionCharge).doubleValue());
                        cellStyle.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));
                        if(baseData.get(key).toString().contains("BLUE")){
                            Font font1 = workbook.createFont();
                            font1.setBold(true);
                            font1.setFontName("宋体");
                            font1.setFontHeightInPoints((short) 12);//设置字体大小
                            font1.setColor(IndexedColors.WHITE1.getIndex());
                            CellStyle style = workbook.createCellStyle();
                            style.setFont(font1);
                            style.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));
                            style.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());
                            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                            textCell.setCellStyle(style);
                        }
                    }else{
                        textCell.setCellValue(baseData.get(key) == null ? "" : baseData.get(key).toString());
                    }
                    textCells[0]++;
                });
                textRows++;
            }
            ByteArrayOutputStream bufferedOutPut = new ByteArrayOutputStream();
            bufferedOutPut.flush();
            workbook.write(bufferedOutPut);
            bufferedOutPut.close();
            ByteArrayInputStream swapStream = new ByteArrayInputStream(bufferedOutPut.toByteArray());
            fileUploadOutDto = fileUpoadUtil.uploadN(swapStream, fileName, CONTNUE_RATE_REPROT_ADDR);
            log.info("结算进度表导出，查询明细上传oss结束，耗时:{}", System.currentTimeMillis() - time);
            updateLog(unionId, fileUploadOutDto, NEWSCHEDULE_REPORT_REPROT);
        }catch (Exception e){
            Map<String, String> map = new HashMap<>(2);
            map.put("status", "failure");
            map.put("message", "上传文件失败" + e.getMessage());
            updateLog(unionId, fileUploadOutDto, NEWSCHEDULE_REPORT_REPROT);
            log.error("error-message:{}", e);
        }
    }

    private void countNewSchedule(List<Map<String, Object>> nodeMap, String channelCode,List<RylCountSettNewScheduleEntity> unsettLogList,String startMonth,String endMonth) {
        List<RylCountSettNewScheduleEntity> logList = unsettLogList.stream().filter(x-> x.getChannelCode().equals(channelCode)).sorted(Comparator.comparing(RylCountSettNewScheduleEntity::getBusiMonth)).collect(Collectors.toList());
        if(!logList.isEmpty()){
            Map<String, Object> temp = new HashMap<>();
            RylCountSettNewScheduleEntity logEntity = logList.get(0);
            if(!StringUtil.isBlank(logEntity.getDepName())){
                temp.put("depName",logEntity.getDepName());
            }
            if(!StringUtil.isBlank(logEntity.getChannelName())){
                temp.put("channelName",logEntity.getChannelName());
            }
            if(!StringUtil.isBlank(logEntity.getSettlementType())){
                if("1".equals(logEntity.getSettlementType())){
                    temp.put("settlementType","服务费");
                }else{
                    temp.put("settlementType","手续费");
                }
            }
            if(!StringUtil.isBlank(logEntity.getChannelSettlementStatus())){
                temp.put("channelSettlementStatus",logEntity.getChannelSettlementStatus());
            }
            if(!StringUtil.isBlank(logEntity.getSettlementIntv())){
                temp.put("settlementIntv",logEntity.getSettlementIntv());
            }
            if(!StringUtil.isBlank(logEntity.getSettlementOperator())){
                temp.put("settlementOperator",logEntity.getSettlementOperator());
            }
            if(logEntity.getIsRylSettlement() != null){
                if(0 == logEntity.getIsRylSettlement()){
                    temp.put("isRylSettlement","是");
                }else{
                    temp.put("isRylSettlement","否");
                }
            }
            temp.put("settMonth",logEntity.getSettMonth());
            List<String> monthList = getMonthBetweenDate(startMonth,endMonth);
            for(String x : monthList){
                LocalDate date = LocalDate.parse(x+"-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                String formattedDate = date.format(DateTimeFormatter.ofPattern("yyyy年M月"));
                List<RylCountSettNewScheduleEntity> logEntityList = logList.stream().filter(a-> a.getBusiMonth().equals(x)).collect(Collectors.toList());
                if(!logEntityList.isEmpty()){
                    RylCountSettNewScheduleEntity logSchedule = logEntityList.get(0);
                    if(logSchedule.getCommissionCharge().compareTo(BigDecimal.ZERO) > 0 || logSchedule.getCommissionCharge().compareTo(BigDecimal.ZERO) < 0){
                        temp.put(formattedDate,logEntityList.get(0).getCommissionCharge()+"|BLUE");
                    }else{
                        temp.put(formattedDate,logEntityList.get(0).getUnsettledCommissionCharge()+"|NO");
                    }
                }else{
                    temp.put(formattedDate,BigDecimal.ZERO+"|NO");
                }
            }
            nodeMap.add(temp);
        }
    }

    private Map<String, String> getNewUnsettledListMap(String startMonth,String endMonth) {
        Map<String, String> aliasMap = new LinkedHashMap<>();
        aliasMap.put("depName", "部门");
        aliasMap.put("channelName", "渠道");
        aliasMap.put("settlementType", "分类");
        aliasMap.put("channelSettlementStatus", "渠道状态");
        aliasMap.put("isRylSettlement", "是否容易联自助结算");
        aliasMap.put("settlementIntv", "结算频次");
        aliasMap.put("settlementOperator", "结算负责人");
        aliasMap.put("settMonth", "结算至");
        List<String> monthList = getMonthBetweenDate(startMonth,endMonth);
        for(String x : monthList){
            LocalDate date = LocalDate.parse(x+"-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String formattedDate = date.format(DateTimeFormatter.ofPattern("yyyy年M月"));
            aliasMap.put(formattedDate, formattedDate);
        }
        return aliasMap;
    }

    @Override
    public BaseResponse queryUnsettledDetail(RylCountReportDto rylCountReportDto) {
        QueryWrapper<RylCountSettlementChannelMappingCfgEntity> cfgQueryWrapper = new QueryWrapper<>();
        cfgQueryWrapper.eq("channel_code",rylCountReportDto.getChannelCode());
        RylCountSettlementChannelMappingCfgEntity cfgEntity = rylCountSettlementChannelMappingCfgMapper.selectOne(cfgQueryWrapper);
        if(cfgEntity == null){
            return BaseResponse.fail("-1","渠道配置信息异常");
        }
        QueryWrapper<RylCountSettlementResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("channel_code",Arrays.asList(StringUtil.split(cfgEntity.getSubChannelCode(), "|")));
        queryWrapper.eq("settle_status","Y");
        queryWrapper.eq("count_pay_status",0);
        if(!StringUtil.isBlank(rylCountReportDto.getStartDate()) && !StringUtil.isBlank(rylCountReportDto.getEndDate())){
            queryWrapper.ge("business_date", rylCountReportDto.getStartDate());
            queryWrapper.le("business_date", rylCountReportDto.getEndDate());
        }
        int count = rylCountSettResultMapper.selectCount(queryWrapper);
        if (count == 0) {
            return BaseResponse.fail("-1","根据条件查询未查询到数据");
        }
        if (riskMap.size() < 1) {
            riskMap = rylCountSettlementRateMapper.getRiskList("").stream()
                    .collect(Collectors.toMap(RiskDTO::getRiskCode, RiskDTO::getRiskName));
        }
        IPage<RylCountReportResDto> page = new Page<>(rylCountReportDto.getPageNum(), rylCountReportDto.getPageSize());
        IPage<RylCountReportResDto> resultList = rylCountSettResultMapper.queryUnsettledList(page,queryWrapper);
        if(!resultList.getRecords().isEmpty()){
            resultList.getRecords().stream().forEach(x->{
                x.setChannelName(cfgEntity.getChannelName());
                x.setMainRiskName(riskMap.get(x.getMainRiskName()));
                if(!StringUtil.isBlank(x.getSettleStatusReason())){
                    JSONObject JSONA = JSONObject.parseObject(x.getSettleStatusReason());
                    JSONArray mess = (JSONArray) JSONA.get("messages");
                    ArrayList<String> list = mess.toJavaObject(ArrayList.class);
                    if (list.size() >= 1) {
                        String stringPipe = String.join("|", list);
                        x.setSettleStatusReason(stringPipe);
                    }
                }
            });
        }
        return BaseResponse.success(resultList);
    }

    @Override
    public BaseResponse queryAllOperator() {
        QueryWrapper<RylCountSettlementChannelLabEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.apply("settlement_operator is not null");
        List<RylCountSettlementChannelLabEntity> labList = rylCountSettlementChannelLabMapper.selectList(queryWrapper);
        if(labList.isEmpty()){
            return BaseResponse.fail("-1","未进行结算负责人配置");
        }
        List<String> operatorList = labList.stream().map(RylCountSettlementChannelLabEntity::getSettlementOperator).distinct().collect(Collectors.toList());
        return BaseResponse.success(operatorList);
    }

    @Override
    public String settBillPay(RylCountReportDto rylCountReportDto) {
        // 参数校验
        if (rylCountReportDto == null) {
            throw new IllegalArgumentException("导出参数不能为空");
        }
        if (rylCountReportDto.getUserId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }

        Map<String, String> channelMap = Maps.newHashMap();
        Map<String, String> message = Maps.newHashMap();
        List<String> channelCodeList = new ArrayList<>();
        message.put("queryParam", JSONObject.toJSONString(rylCountReportDto));
        FileUploadOutDto fileUploadOutDto = null;

        // 生成任务ID
        String taskId = UUID.randomUUID().toString();
        message.put("taskId", taskId);

        ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
        //条件查询导出
        singleThreadExecutor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    settBillPayExportList(rylCountReportDto, fileUploadOutDto, message);
                    log.info("借款与核销报表导出任务完成，taskId: {}", taskId);
                } catch (Exception e) {
                    log.error("借款与核销报表导出任务异常，taskId: {}", taskId, e);
                }
            }
        });
        singleThreadExecutor.shutdown();

        return taskId;
    }

    /**
     * 优化后的借款与核销报表导出方法
     * 采用仿流式处理，避免内存溢出和响应超时
     */
    @SneakyThrows
    public void settBillPayExportList(RylCountReportDto rylCountReportDto, FileUploadOutDto fileUploadOutDto, Map<String, String> message) {
        long time = System.currentTimeMillis();
        Long threadId = Thread.currentThread().getId();
        QueryWrapper<RylCountSettlementBillPayEntity> queryWrapper = new QueryWrapper<>();
        RylCountSettlementChannelMappingCfgEntity cfgEntity = null;
        queryWrapper.eq("asyn_fk_flag",1);
        String fileName = "";
        if(!StringUtil.isBlank(rylCountReportDto.getStartMonth()) && !StringUtil.isBlank(rylCountReportDto.getEndMonth())){
            // 参数校验，防止SQL注入
            String startMonth = validateAndFormatMonth(rylCountReportDto.getStartMonth());
            String endMonth = validateAndFormatMonth(rylCountReportDto.getEndMonth());

            queryWrapper.apply(" DATE_FORMAT(count_start_time,'%Y-%m') >= {0}", startMonth);
            queryWrapper.apply(" DATE_FORMAT(count_end_time,'%Y-%m') <= {0}", endMonth);
            fileName = "借款与核销数据报表" + startMonth + "-" + endMonth + ".xlsx";
        }else {
            queryWrapper.apply(" DATE_FORMAT(count_start_time,'%Y-%m') >= '2023-01'");
            String formattedDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
            fileName = "借款与核销数据报表2023-01-"+formattedDate+".xlsx";
        }
        //弘康融汇的投连产品结算 仍使用的结算的init数据,算出来的result及账单数据 没设置is_hkrh_sett,故需加channel_code='ronghui' 包含进这部分弘康融汇数据
        queryWrapper.apply("( is_hkrh_sett = '1' or is_pay_behalf = 1 or channel_code like '20000%'  or channel_code='ronghui')");
        int count = rylCountSettlementBillPayMapper.selectCount(queryWrapper);
        if (count == 0) {
            log.info("借款与核销报表导出：查询结果为空");
            return;
        }

        // 检查数据量，如果超过阈值则使用分页处理
        final int MAX_BATCH_SIZE = 10000; // 最大批处理大小
        if (count > MAX_BATCH_SIZE) {
            log.warn("数据量过大({})，建议使用分页导出", count);
        }

        String threadName = Thread.currentThread().getName();
        message.put("threadId", String.valueOf(threadId));
        message.put("fileName", fileName);
        message.put("totalCount", String.valueOf(count));

        SendMessageEvent sendMessageEvent = new SendMessageEvent(message);
        sendMessageEvent.setDataType(SETT_BILL_PAY_REPORT_REPROT);
        sendMessageEvent.setOperate(0);
        String unionId = UUID.randomUUID() + "-" + threadId;
        sendMessageEvent.setUnionId(unionId);
        applicationEventPublisher.publishEvent(sendMessageEvent);
        log.info("当前线程名称:{} 当前线程Id:{} 数据总量:{}", threadName, threadId, count);

        // 使用流式处理，避免内存溢出
        try {
            fileUploadOutDto = executeStreamExport(queryWrapper, fileName, count, unionId, time);
            updateLog(unionId, fileUploadOutDto, SETT_BILL_PAY_REPORT_REPROT);
            log.info("借款与核销数据报表导出完成，unionId: {}, 总耗时: {}ms", unionId, System.currentTimeMillis() - time);
        } catch (Exception e) {
            log.error("借款与核销数据报表导出异常，unionId: {}, 耗时: {}ms", unionId, System.currentTimeMillis() - time, e);
            updateLog(unionId, null, SETT_BILL_PAY_REPORT_REPROT);
            throw new RuntimeException("借款与核销数据报表导出失败", e);
        }
    }

    /**
     * 分页查询数据，避免内存溢出
     */
    private List<RylCountSettlementBillPayEntity> queryDataWithPagination(
            QueryWrapper<RylCountSettlementBillPayEntity> queryWrapper,
            int totalCount,
            int maxBatchSize) {

        List<RylCountSettlementBillPayEntity> allData = new ArrayList<>();

        if (totalCount <= maxBatchSize) {
            // 数据量不大，直接查询
            return rylCountSettlementBillPayMapper.selectList(queryWrapper);
        }

        // 分页查询
        int pageSize = Math.min(maxBatchSize, 5000); // 每页最多5000条
        int totalPages = (totalCount + pageSize - 1) / pageSize;

        for (int pageNum = 1; pageNum <= totalPages; pageNum++) {
            try {
                Page<RylCountSettlementBillPayEntity> page = new Page<>(pageNum, pageSize);
                IPage<RylCountSettlementBillPayEntity> pageResult =
                        rylCountSettlementBillPayMapper.selectPage(page, queryWrapper);

                List<RylCountSettlementBillPayEntity> pageData = pageResult.getRecords();
                if (CollectionUtils.isNotEmpty(pageData)) {
                    allData.addAll(pageData);
                    log.info("分页查询第{}/{}页完成，本页数据量：{}", pageNum, totalPages, pageData.size());
                }

                // 每查询几页后进行一次GC，释放内存
                if (pageNum % 3 == 0) {
                    System.gc();
                }

            } catch (Exception e) {
                log.error("分页查询第{}页失败", pageNum, e);
                throw new RuntimeException("分页查询数据失败", e);
            }
        }

        log.info("分页查询完成，总数据量：{}", allData.size());
        return allData;
    }

    /**
     * 带重试机制的外部接口调用（使用ApiLogUtil统一日志管理）
     */
    private List<FkBillPayDto> callExternalApiWithRetry(String url, List<String> params, String apiType) {
        final int MAX_RETRY = 3;
        final long RETRY_DELAY = 1000; // 1秒
        // 开始API调用，生成请求ID
        String requestId = ApiLogUtil.startApiCall(apiType);
        try {
            for (int attempt = 1; attempt <= MAX_RETRY; attempt++) {
                try {
                    ApiLogUtil.setAttempt(attempt);
                    long startTime = System.currentTimeMillis();
                    // 记录请求日志
                    ApiLogUtil.logRequest(url, params, attempt);
                    ResponseEntity<FkBillPayReq> resEntity = externalRestTemplate.exchange(
                            url, HttpMethod.POST, new HttpEntity<>(params),
                            new ParameterizedTypeReference<FkBillPayReq>() {
                            });
                    long costTime = System.currentTimeMillis() - startTime;
                    // 记录响应日志
                    ApiLogUtil.logResponse(url, resEntity.getBody(), costTime, resEntity.getStatusCodeValue());
                    if (resEntity.getBody() != null && "000000".equals(resEntity.getBody().getRspCode())) {
                        List<FkBillPayDto> data = resEntity.getBody().getRspData();
                        ApiLogUtil.logSuccess(data != null ? data.size() : 0);
                        return data != null ? data : new ArrayList<>();
                    } else {
                        String errorMsg = resEntity.getBody() != null ? resEntity.getBody().getRspCode() : "响应为空";
                        ApiLogUtil.logError("费控系统返回错误码：" + errorMsg, attempt, MAX_RETRY);
                        if (attempt == MAX_RETRY) {
                            throw new RuntimeException("费控系统返回错误：" + errorMsg);
                        }
                    }
                } catch (Exception e) {
                    ApiLogUtil.logError("调用异常：" + e.getMessage(), attempt, MAX_RETRY);
                    if (attempt == MAX_RETRY) {
                        throw new RuntimeException("调用费控系统失败，已重试" + MAX_RETRY + "次", e);
                    }
                    // 等待后重试
                    try {
                        int waitSeconds = (int) (RETRY_DELAY * attempt / 1000);
                        ApiLogUtil.logRetryWait(waitSeconds, attempt + 1);
                        Thread.sleep(RETRY_DELAY * attempt); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("重试等待被中断", ie);
                    }
                }
            }
            return new ArrayList<>();
        } finally {
            // 清理MDC，避免内存泄漏
            ApiLogUtil.clearMDC();
        }
    }

    /**
     * 校验和格式化月份参数，防止SQL注入
     */
    private String validateAndFormatMonth(String month) {
        if (StringUtil.isBlank(month)) {
            throw new IllegalArgumentException("月份参数不能为空");
        }
        // 校验格式：yyyy-MM
        if (!month.matches("^\\d{4}-\\d{2}$")) {
            throw new IllegalArgumentException("月份格式错误，应为yyyy-MM格式");
        }
        // 进一步校验月份范围
        try {
            String[] parts = month.split("-");
            int year = Integer.parseInt(parts[0]);
            int monthNum = Integer.parseInt(parts[1]);

            if (year < 2020 || year > 2030) {
                throw new IllegalArgumentException("年份超出有效范围(2020-2030)");
            }

            if (monthNum < 1 || monthNum > 12) {
                throw new IllegalArgumentException("月份超出有效范围(01-12)");
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("月份格式错误", e);
        }
        return month;
    }

    /**
     * 分页流式导出(要调外部接口 不好流式逐条都去调一次)
     * 采用分批查询+流式写入+异步外部接口调用
     */
    private FileUploadOutDto executeStreamExport(
            QueryWrapper<RylCountSettlementBillPayEntity> queryWrapper,
            String fileName,
            int totalCount,
            String unionId,
            long startTime) throws Exception {
        final int BATCH_SIZE = 1000; // 每批处理1000条，平衡内存和性能
        final int EXTERNAL_API_BATCH_SIZE = 50; // 外部接口批量调用大小
        // 创建临时文件，避免大文件占用内存
        File tempFile = File.createTempFile("export_" + unionId, ".xlsx");
        try (FileOutputStream fos = new FileOutputStream(tempFile);
             SXSSFWorkbook workbook = new SXSSFWorkbook(100)) { // 内存中只保留100行
            // 设置样式
            CellStyle cellStyle = cellContentStyle(workbook);
            // 创建工作表
            SXSSFSheet sheet = workbook.createSheet("借款与核销数据报表");
            // 写入表头
            createExcelHeader(sheet, cellStyle);
            int currentRow = 1; // 从第二行开始写数据
            int processedCount = 0;
            int totalPages = (totalCount + BATCH_SIZE - 1) / BATCH_SIZE;
            log.info("开始伪流式导出，总页数: {}, 每页大小: {}, unionId: {}", totalPages, BATCH_SIZE, unionId);
            // 分页处理数据
            for (int pageNum = 1; pageNum <= totalPages; pageNum++) {
                long pageStartTime = System.currentTimeMillis();
                // 分页查询当前批次数据
                Page<RylCountSettlementBillPayEntity> page = new Page<>(pageNum, BATCH_SIZE);
                IPage<RylCountSettlementBillPayEntity> pageResult =
                        rylCountSettlementBillPayMapper.selectPage(page, queryWrapper);
                List<RylCountSettlementBillPayEntity> batchData = pageResult.getRecords();
                if (CollectionUtils.isEmpty(batchData)) {
                    log.warn("第{}页数据为空，跳过处理", pageNum);
                    continue;
                }
                // 异步获取外部接口数据
                Map<String, FkBillPayDto> externalDataMap = getExternalDataAsync(batchData, EXTERNAL_API_BATCH_SIZE);
                // 写入当前批次数据到Excel
                currentRow = writeDataToExcel(sheet, batchData, externalDataMap, currentRow, cellStyle);
                processedCount += batchData.size();
                // 发布进度事件
                int progress = (int) ((processedCount * 100.0) / totalCount);
                publishProgressEvent(unionId, progress, processedCount, totalCount);

                long pageTime = System.currentTimeMillis() - pageStartTime;
                log.info("第{}/{}页处理完成，本页数据: {}, 累计: {}, 进度: {}%, 耗时: {}ms",
                        pageNum, totalPages, batchData.size(), processedCount, progress, pageTime);
                // 清理当前批次数据，释放内存
                batchData.clear();
                // 每处理5页强制GC一次
                if (pageNum % 5 == 0) {
                    System.gc();
                }
            }
            // 写入Excel文件
            workbook.write(fos);
            fos.flush();
            log.info("Excel写入完成，开始上传OSS，文件大小: {} bytes", tempFile.length());
            // 上传到OSS
            FileUploadOutDto uploadResult;
            try (FileInputStream fis = new FileInputStream(tempFile)) {
                uploadResult = fileUpoadUtil.uploadN(fis, fileName, CONTNUE_RATE_REPROT_ADDR);
            }
            long totalTime = System.currentTimeMillis() - startTime;
            log.info("流式导出完成，总处理: {}, 总耗时: {}ms, 文件: {}", processedCount, totalTime, uploadResult.getFileKey());
            return uploadResult;
        } finally {
            // 清理临时文件
            if (tempFile.exists()) {
                boolean deleted = tempFile.delete();
                log.info("临时文件清理: {}, 结果: {}", tempFile.getName(), deleted);
            }
        }
    }

    /**
     * 异步获取外部接口数据
     */
    private Map<String, FkBillPayDto> getExternalDataAsync(List<RylCountSettlementBillPayEntity> batchData, int batchSize) {
        Map<String, FkBillPayDto> resultMap = new ConcurrentHashMap<>();
        try {
            // 按用户分组，减少外部接口调用次数
            Map<Integer, List<String>> jkBillNoMap = batchData.stream()
                    .filter(x -> x.getIsPayBehalf() == 1 && StringUtils.isNotEmpty(x.getFkVerificationSheet()))
                    .collect(Collectors.groupingBy(RylCountSettlementBillPayEntity::getUserFkUuid,
                            Collectors.mapping(RylCountSettlementBillPayEntity::getFkVerificationSheet, Collectors.toList())));

            Map<Integer, List<String>> hxBillNoMap = batchData.stream()
                    .filter(x -> x.getIsPayBehalf() == 0 && StringUtils.isNotEmpty(x.getFkBillNo()))
                    .collect(Collectors.groupingBy(RylCountSettlementBillPayEntity::getUserFkUuid,
                            Collectors.mapping(RylCountSettlementBillPayEntity::getFkBillNo, Collectors.toList())));

            // 并行处理外部接口调用
            CompletableFuture<Void> jkFuture = CompletableFuture.runAsync(() ->
                    processExternalApiCalls(jkBillNoMap, resultMap, true, batchSize));
            CompletableFuture<Void> hxFuture = CompletableFuture.runAsync(() ->
                    processExternalApiCalls(hxBillNoMap, resultMap, false, batchSize));
            // 等待所有外部接口调用完成，设置超时时间
            CompletableFuture.allOf(jkFuture, hxFuture).get(30, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("获取外部接口数据异常", e);
            // 不抛出异常，继续处理，但记录错误
        }
        return resultMap;
    }

    /**
     * 处理外部接口调用
     */
    private void processExternalApiCalls(Map<Integer, List<String>> billNoMap,
                                         Map<String, FkBillPayDto> resultMap,
                                         boolean isJkBill,
                                         int batchSize) {
        billNoMap.forEach((userFkUuid, billNos) -> {
            try {
                RylCountSettlementBillMappingCfgEntity billMappingCfg =
                        rylCountSettlementBillMappingCfgMapper.selectById(userFkUuid);
                if (billMappingCfg == null || CollectionUtils.isEmpty(billNos)) {
                    return;
                }
                // 分批调用外部接口
                List<List<String>> batches = Lists.partition(billNos, batchSize);
                for (List<String> batch : batches) {
                    String apiUrl = buildApiUrl(billMappingCfg, isJkBill);
                    List<FkBillPayDto> apiData = callExternalApiWithRetry(apiUrl, batch, isJkBill ? "借款单据" : "核销单据");
                    // 将结果放入Map
                    if (CollectionUtils.isNotEmpty(apiData)) {
                        apiData.forEach(dto -> resultMap.put(dto.getBillNo(), dto));
                    }
                }
            } catch (Exception e) {
                log.error("处理用户{}的外部接口调用失败", userFkUuid, e);
            }
        });
    }

    /**
     * 构建API URL
     */
    private String buildApiUrl(RylCountSettlementBillMappingCfgEntity billMappingCfg, boolean isJkBill) {
        boolean isHk = billMappingCfg.getFkUnitName().startsWith(CommonConstant.FKUSERDEPT_HK);
        if (isJkBill) {
            return isHk ? fkHkDns + fkJkBillNoUrl : fkrhDns + fkJkBillNoUrl;
        } else {
            return isHk ? fkHkDns + fkHxBillNoUrl : fkrhDns + fkHxBillNoUrl;
        }
    }

    /**
     * 创建Excel表头
     */
    private void createExcelHeader(SXSSFSheet sheet, CellStyle cellStyle) {
        // 设置列宽
        for (int i = 0; i <= 10; i++) {
            sheet.setColumnWidth(i, 4500);
        }
        // 创建标题行
        SXSSFRow titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints(50);
        String[] headers = {"业务月份", "渠道名称", "单据类型", "单据金额", "单据编号",
                "描述", "手续费", "核销单据类型", "核销单据号", "核销金额", "待核销金额"};

        for (int i = 0; i < headers.length; i++) {
            SXSSFCell cell = titleRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(cellStyle);
        }
    }

    /**
     * 写入数据到Excel
     */
    private int writeDataToExcel(SXSSFSheet sheet,
                                 List<RylCountSettlementBillPayEntity> batchData,
                                 Map<String, FkBillPayDto> externalDataMap,
                                 int startRow,
                                 CellStyle cellStyle) {

        int currentRow = startRow;
        for (RylCountSettlementBillPayEntity entity : batchData) {
            try {
                // 查找对应的外部数据
                FkBillPayDto externalData = findExternalData(entity, externalDataMap);
                // 创建数据行
                SXSSFRow dataRow = sheet.createRow(currentRow++);
                // 填充数据
                fillRowData(dataRow, entity, externalData, cellStyle);
            } catch (Exception e) {
                log.error("写入Excel行数据异常，实体ID: {}", entity.getId(), e);
                // 继续处理下一行
            }
        }
        return currentRow;
    }

    /**
     * 查找外部数据
     */
    private FkBillPayDto findExternalData(RylCountSettlementBillPayEntity entity, Map<String, FkBillPayDto> externalDataMap) {
        // 先尝试通过单据号查找
        FkBillPayDto data = externalDataMap.get(entity.getFkBillNo());
        if (data != null) {
            return data;
        }
        // 再尝试通过核销单据号查找
        if (StringUtils.isNotEmpty(entity.getFkVerificationSheet())) {
            data = externalDataMap.get(entity.getFkVerificationSheet());
        }
        return data;
    }

    /**
     * 填充行数据
     */
    private void fillRowData(SXSSFRow dataRow, RylCountSettlementBillPayEntity entity, FkBillPayDto externalData, CellStyle cellStyle) {
        // 业务月份
        String countDatePeriod = SettlementUtil.dealSettDate(entity.getCountStartTime(), entity.getCountEndTime());
        dataRow.createCell(0).setCellValue(countDatePeriod);
        // 渠道名称
        dataRow.createCell(1).setCellValue(entity.getChannelName());
        if (externalData != null) {
            // 有外部数据时的处理逻辑
            fillRowWithExternalData(dataRow, entity, externalData, cellStyle);
        } else {
            // 没有外部数据时的默认处理
            fillRowWithDefaultData(dataRow, entity, cellStyle);
        }
    }

    /**
     * 使用外部数据填充行
     */
    private void fillRowWithExternalData(SXSSFRow dataRow, RylCountSettlementBillPayEntity entity, FkBillPayDto externalData, CellStyle cellStyle) {
        String amount = StringUtils.isBlank(entity.getCommissionChargeTotalAfter()) ?
                entity.getCommissionChargeTotal() : entity.getCommissionChargeTotalAfter();
        //单据金额
        String billAmount = externalData.getOriginalAmount() != null ? externalData.getOriginalAmount().toString() : "";
        if ("手续费借款单".equals(externalData.getBillName())) {
            dataRow.createCell(2).setCellValue(externalData.getBillName());
            dataRow.createCell(3).setCellValue(billAmount);
            dataRow.createCell(4).setCellValue(externalData.getBillNo());
            dataRow.createCell(5).setCellValue(externalData.getRemark());
            dataRow.createCell(6).setCellValue(amount);

            dataRow.createCell(7).setCellValue(externalData.getLendBillName());
            dataRow.createCell(8).setCellValue(externalData.getBusinessKey());
            dataRow.createCell(9).setCellValue(externalData.getUsedAmount() != null ? externalData.getUsedAmount().toString() : "");
            dataRow.createCell(10).setCellValue(externalData.getBalanceAmount() != null ? externalData.getBalanceAmount().toString() : "");
        } else {
            dataRow.createCell(2).setCellValue("手续费代付单");
            dataRow.createCell(3).setCellValue(billAmount);
            dataRow.createCell(4).setCellValue(entity.getFkBillNo());
            dataRow.createCell(5).setCellValue(externalData.getRemark());
            dataRow.createCell(6).setCellValue(amount);

            dataRow.createCell(7).setCellValue(externalData.getBillName());
            dataRow.createCell(8).setCellValue(externalData.getBillNo());
            //当手续费报销单据号的单据”非审批完成“时，待核销金额展示单据的金额，核销金额展示0.00
            dataRow.createCell(9).setCellValue((!"审批完成".equals(externalData.getBillStatus())) ? "0.00" : (externalData.getOriginalCurrencySum() != null ? externalData.getOriginalCurrencySum().toString() : ""));
            dataRow.createCell(10).setCellValue((!"审批完成".equals(externalData.getBillStatus())) ? billAmount : (externalData.getAvailAmount() != null ? externalData.getAvailAmount().toString() : ""));
        }
    }

    /**
     * 使用默认数据填充行
     */
    private void fillRowWithDefaultData(SXSSFRow dataRow, RylCountSettlementBillPayEntity entity, CellStyle cellStyle) {
        dataRow.createCell(2).setCellValue("未知类型");
        dataRow.createCell(3).setCellValue("");
        dataRow.createCell(4).setCellValue(entity.getFkBillNo());
        dataRow.createCell(5).setCellValue("外部数据获取失败");
        String amount = StringUtils.isBlank(entity.getCommissionChargeTotalAfter()) ?
                entity.getCommissionChargeTotal() : entity.getCommissionChargeTotalAfter();
        dataRow.createCell(6).setCellValue(amount);
        dataRow.createCell(7).setCellValue("");
        dataRow.createCell(8).setCellValue("");
        dataRow.createCell(9).setCellValue("");
        dataRow.createCell(10).setCellValue("");
    }

    /**
     * 发布进度事件
     */
    private void publishProgressEvent(String unionId, int progress, int processedCount, int totalCount) {
        try {
            Map<String, String> progressMessage = new HashMap<>();
            progressMessage.put("unionId", unionId);
            progressMessage.put("progress", String.valueOf(progress));
            progressMessage.put("processedCount", String.valueOf(processedCount));
            progressMessage.put("totalCount", String.valueOf(totalCount));

            SendMessageEvent progressEvent = new SendMessageEvent(progressMessage);
            progressEvent.setDataType(SETT_BILL_PAY_REPORT_REPROT);
            progressEvent.setOperate(1); // 进度更新
            progressEvent.setUnionId(unionId);

            applicationEventPublisher.publishEvent(progressEvent);
        } catch (Exception e) {
            log.warn("发布进度事件失败", e);
        }
    }

    //结算单明细sheet拆分-汇总sheet- 去掉了每类业务的 分别统计
    private void getSheetTotal(List<RylCountSettlementBillPayEntity> rylCountSettlementBillPayEntities, SXSSFWorkbook workbook) {
        SXSSFSheet sheetAllSum = workbook.createSheet("借款与核销数据报表");
        sheetAllSum.setDefaultColumnWidth(800);
        sheetAllSum.setColumnWidth(0, 4500);
        sheetAllSum.setColumnWidth(1, 4500);
        sheetAllSum.setColumnWidth(2, 4500);
        sheetAllSum.setColumnWidth(3, 4500);
        sheetAllSum.setColumnWidth(4, 4500);
        sheetAllSum.setColumnWidth(5, 4500);
        sheetAllSum.setColumnWidth(6, 4500);
        sheetAllSum.setColumnWidth(7, 4500);
        sheetAllSum.setColumnWidth(8, 4500);
        sheetAllSum.setColumnWidth(9, 4500);
        sheetAllSum.setColumnWidth(10, 4500);

        //创建首行
        SXSSFRow headRow = sheetAllSum.createRow(0);
        SXSSFCell SXSSFCell = headRow.createCell(0);
        SXSSFCell SXSSFCell1 = headRow.createCell(1);
        SXSSFCell SXSSFCell2 = headRow.createCell(2);
        SXSSFCell SXSSFCell3 = headRow.createCell(3);
        SXSSFCell SXSSFCell4 = headRow.createCell(4);
        SXSSFCell SXSSFCell5 = headRow.createCell(5);
        SXSSFCell SXSSFCell6 = headRow.createCell(6);
        SXSSFCell SXSSFCell7 = headRow.createCell(7);
        SXSSFCell SXSSFCell8 = headRow.createCell(8);
        SXSSFCell SXSSFCell9 = headRow.createCell(9);
        SXSSFCell SXSSFCell10 = headRow.createCell(10);
        SXSSFCell.setCellValue("借款与核销数据报表");
        CellStyle headStyle = handleHeadStyle(workbook);
        headRow.setHeightInPoints(50);
        SXSSFCell.setCellStyle(headStyle);
        SXSSFCell1.setCellStyle(headStyle);
        SXSSFCell2.setCellStyle(headStyle);
        SXSSFCell3.setCellStyle(headStyle);
        SXSSFCell4.setCellStyle(headStyle);
        SXSSFCell5.setCellStyle(headStyle);
        SXSSFCell6.setCellStyle(headStyle);
        SXSSFCell7.setCellStyle(headStyle);
        SXSSFCell8.setCellStyle(headStyle);
        SXSSFCell9.setCellStyle(headStyle);
        SXSSFCell10.setCellStyle(headStyle);
        sheetAllSum.addMergedRegion(new CellRangeAddress(0, 0, 0, 10));
        //创建描述行
        CellStyle msStyle = msStyle(workbook);
        SXSSFRow msRow = sheetAllSum.createRow(1);
        SXSSFCell msCell = msRow.createCell(0);
        msCell.setCellStyle(msStyle);
        SXSSFCell msCell1 = msRow.createCell(1);
        msCell1.setCellStyle(msStyle);
        SXSSFCell msCell2 = msRow.createCell(2);
        msCell2.setCellStyle(msStyle);
        SXSSFCell msCell3 = msRow.createCell(3);
        msCell3.setCellStyle(msStyle);
        SXSSFCell msCell4 = msRow.createCell(4);
        msCell4.setCellStyle(msStyle);
        SXSSFCell msCell5 = msRow.createCell(5);
        msCell5.setCellStyle(msStyle);
        SXSSFCell msCell6 = msRow.createCell(6);
        msCell6.setCellStyle(msStyle);
        SXSSFCell msCell7 = msRow.createCell(7);
        msCell7.setCellStyle(msStyle);
        SXSSFCell msCell8 = msRow.createCell(8);
        msCell8.setCellStyle(msStyle);
        SXSSFCell msCell9 = msRow.createCell(9);
        msCell9.setCellStyle(msStyle);
        SXSSFCell msCell10 = msRow.createCell(10);
        msCell10.setCellStyle(msStyle);
        msCell.setCellValue("1、银行大约从24年6月份开始；弘康融汇间预计从24年8月份呢开始；代付渠道预计从XX年XX月开始（待定）；");
        msRow.setHeightInPoints(30);
        sheetAllSum.addMergedRegion(new CellRangeAddress(1, 1, 0, 10));

        //创建标题行
        SXSSFRow headTwoRow = sheetAllSum.createRow(2);
        CellStyle cellStyle = cellHeadStyleHx(workbook,null);
        CellStyle cellStyleOne = cellHeadStyleHxOne(workbook,null);
        SXSSFCell cell1 = headTwoRow.createCell(0);
        cell1.setCellStyle(cellStyleOne);
        cell1.setCellValue("业务月份");
        SXSSFCell cell2 = headTwoRow.createCell(1);
        cell2.setCellStyle(cellStyle);
        cell2.setCellValue("渠道名称");
        SXSSFCell cell3 = headTwoRow.createCell(2);
        cell3.setCellStyle(cellStyle);
        cell3.setCellValue("单据类型");
        SXSSFCell cell4 = headTwoRow.createCell(3);
        cell4.setCellStyle(cellStyle);
        cell4.setCellValue("单据金额");
        SXSSFCell cell5 = headTwoRow.createCell(4);
        cell5.setCellStyle(cellStyle);
        cell5.setCellValue("单据编号");
        SXSSFCell cell6 = headTwoRow.createCell(5);
        cell6.setCellStyle(cellStyle);
        cell6.setCellValue("描述");
        SXSSFCell cell7 = headTwoRow.createCell(6);
        cell7.setCellStyle(cellStyle);
        cell7.setCellValue("手续费");
        SXSSFCell cell8 = headTwoRow.createCell(7);
        cell8.setCellStyle(cellStyle);
        cell8.setCellValue("核销单据类型");
        SXSSFCell cell9 = headTwoRow.createCell(8);
        cell9.setCellStyle(cellStyle);
        cell9.setCellValue("核销单据号");
        SXSSFCell cell10 = headTwoRow.createCell(9);
        cell10.setCellStyle(cellStyle);
        cell10.setCellValue("核销金额");
        SXSSFCell cell11 = headTwoRow.createCell(10);
        cell11.setCellStyle(cellStyle);
        cell11.setCellValue("待核销金额");

        int[] rowNumArray = new int[1];
        rowNumArray[0] = sheetAllSum.getLastRowNum();
        Map<String, List<BillPayStatementDto>> detailAllData = getBillPayDetailMap(rylCountSettlementBillPayEntities);
        LinkedHashMap<String, List<BillPayStatementDto>> collect = detailAllData.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new));
        CellStyle cellTextStyle = cellContStyle(workbook);
        CellStyle cellMoneyStyle = cellMoneyStyle(workbook);
        //数据 循环写入excel
        CellStyle cellTextStyleOne = cellContStyleOne(workbook);
        collect.forEach((key, value) -> {
            if (!"-".equals(key)) {
                int size = value.size();
                int notZeroSize = 0;
                for (int i = 0; i < size; i++) {
                    BillPayStatementDto billPayStatementDto = value.get(i);

                    SXSSFRow xqRow = sheetAllSum.createRow(rowNumArray[0] + 1 + notZeroSize);
                    SXSSFCell xqRowCell = xqRow.createCell(0);
                    xqRowCell.setCellStyle(cellTextStyleOne);
                    xqRowCell.setCellValue(value.get(0).getBusinessMonth());

                    SXSSFCell xqRowCell1 = xqRow.createCell(1);
                    xqRowCell1.setCellStyle(cellTextStyle);
                    xqRowCell1.setCellValue(billPayStatementDto.getChannelName());

                    SXSSFCell xqRowCell2 = xqRow.createCell(2);
                    xqRowCell2.setCellStyle(cellTextStyle);
                    xqRowCell2.setCellValue(billPayStatementDto.getBillType());

                    SXSSFCell xqRowCell3 = xqRow.createCell(3);
                    xqRowCell3.setCellStyle(cellMoneyStyle);
                    xqRowCell3.setCellValue(billPayStatementDto.getBillAmount()!=null ? billPayStatementDto.getBillAmount().toString():null);

                    SXSSFCell xqRowCell4 = xqRow.createCell(4);
                    xqRowCell4.setCellStyle(cellTextStyle);
                    xqRowCell4.setCellValue(billPayStatementDto.getFkBillNo());

                    SXSSFCell xqRowCell5 = xqRow.createCell(5);
                    xqRowCell5.setCellStyle(cellTextStyle);
                    xqRowCell5.setCellValue(billPayStatementDto.getDescription());

                    SXSSFCell xqRowCell6 = xqRow.createCell(6);
                    xqRowCell6.setCellStyle(cellMoneyStyle);
                    xqRowCell6.setCellValue(billPayStatementDto.getCommissionCharge());

                    SXSSFCell xqRowCell7 = xqRow.createCell(7);
                    xqRowCell7.setCellStyle(cellTextStyle);
                    xqRowCell7.setCellValue(billPayStatementDto.getVerificationBillType());

                    SXSSFCell xqRowCell8 = xqRow.createCell(8);
                    xqRowCell8.setCellStyle(cellTextStyle);
                    xqRowCell8.setCellValue(billPayStatementDto.getVerificationFkBillNo());

                    SXSSFCell xqRowCell9 = xqRow.createCell(9);
                    xqRowCell9.setCellStyle(cellMoneyStyle);
                    xqRowCell9.setCellValue(billPayStatementDto.getVerificationCommissionCharge()!=null ? billPayStatementDto.getVerificationCommissionCharge().toString():null);

                    SXSSFCell xqRowCell10 = xqRow.createCell(10);
                    xqRowCell10.setCellStyle(cellMoneyStyle);
                    xqRowCell10.setCellValue(billPayStatementDto.getNotVerificationCommissionCharge()!=null ? billPayStatementDto.getNotVerificationCommissionCharge().toString():null);
                    //手续费以数字格式输出，并且设置单元格为可以计算的格式
                    cellMoneyStyle.setDataFormat(workbook.createDataFormat().getFormat("0.00"));
                    notZeroSize++;
                }
                //手续费非0行 首列进行合并
                if (notZeroSize > 1) {
                    sheetAllSum.addMergedRegion(new CellRangeAddress(rowNumArray[0] + 1, rowNumArray[0] + notZeroSize, 0, 0));
                }
            }
            rowNumArray[0] = sheetAllSum.getLastRowNum();

        });
    }

    private Map<String, List<BillPayStatementDto>> getBillPayDetailMap(List<RylCountSettlementBillPayEntity> rylCountSettlementBillPayEntities) {
        Map<String, List<BillPayStatementDto>> map = new HashMap<>();
        List<FkBillPayDto> data = new ArrayList<>();
        /**
         * 因费控拆分弘康/融汇费控系统,故此按结算单的 推送费控账号所在部门 分别查不同费控 获取数据
         *   弘康的借款单 查弘康费控;融汇的借款单 查融汇费控;
         *   弘康的核销单 查弘康费控;融汇的核销单 查融汇费控;
         */
        Map<Integer, List<String>> fkBillNoJkMap = rylCountSettlementBillPayEntities.stream()
                .filter(x -> x.getIsPayBehalf() == 1 && StringUtils.isNotEmpty(x.getFkVerificationSheet()))
                .collect(Collectors.groupingBy(RylCountSettlementBillPayEntity::getUserFkUuid,
                        Collectors.mapping(RylCountSettlementBillPayEntity::getFkVerificationSheet, Collectors.toList())));
        fkBillNoJkMap.forEach((key, value) -> {
            RylCountSettlementBillMappingCfgEntity billMappingCfg = rylCountSettlementBillMappingCfgMapper.selectById(key);
            if (billMappingCfg != null && CollectionUtils.isNotEmpty(value)) {
                String fkJkUrl = billMappingCfg.getFkUnitName().startsWith(CommonConstant.FKUSERDEPT_HK) ? fkHkDns + fkJkBillNoUrl : fkrhDns + fkJkBillNoUrl;
                log.info("请求费控系统[{}]获取借款单据，请求参数：{}", fkJkUrl, JSONObject.toJSONString(value));
                try {
                    // 添加重试机制的外部接口调用
                    List<FkBillPayDto> jkData = callExternalApiWithRetry(fkJkUrl, value, "借款单据");
                    if (CollectionUtils.isNotEmpty(jkData)) {
                        data.addAll(jkData);
                    }
                } catch (Exception e) {
                    log.error("调用费控系统获取借款单据失败，URL: {}, 参数: {}", fkJkUrl, JSONObject.toJSONString(value), e);
                    // 不抛出异常，继续处理其他数据，但记录错误
                }
            }
        });

        Map<Integer, List<String>> fkBillNoHxMap = rylCountSettlementBillPayEntities.stream()
                .filter(x -> x.getIsPayBehalf() == 0)
                .collect(Collectors.groupingBy(RylCountSettlementBillPayEntity::getUserFkUuid,
                        Collectors.mapping(RylCountSettlementBillPayEntity::getFkBillNo, Collectors.toList())));
        fkBillNoHxMap.forEach((key, value) -> {
            RylCountSettlementBillMappingCfgEntity billMappingCfg = rylCountSettlementBillMappingCfgMapper.selectById(key);
            if (billMappingCfg != null && CollectionUtils.isNotEmpty(value)) {
                String fkHxUrl = billMappingCfg.getFkUnitName().startsWith(CommonConstant.FKUSERDEPT_HK) ? fkHkDns + fkHxBillNoUrl : fkrhDns + fkHxBillNoUrl;
                log.info("请求费控系统[{}]获取核销单据，请求参数：{}", fkHxUrl, JSONObject.toJSONString(value));
                ResponseEntity<FkBillPayReq> responseEntity = externalRestTemplate.exchange(fkHxBillNoUrl, HttpMethod.POST, new HttpEntity(value), new ParameterizedTypeReference<FkBillPayReq>() {
                });
                log.info("请求费控系统[{}]获取核销单据，返回参数：{}", fkHxUrl, JSONObject.toJSONString(responseEntity));
                if (responseEntity.getBody() != null && responseEntity.getBody().getRspCode().equals("000000")) {
                    List<FkBillPayDto> jkData = responseEntity.getBody().getRspData();
                    if (CollectionUtils.isNotEmpty(jkData)) {
                        data.addAll(jkData);
                    }
                }
            }
        });
        if (CollectionUtils.isNotEmpty(data)){
            for (FkBillPayDto rspDatum : data) {
                List<RylCountSettlementBillPayEntity> collect = rylCountSettlementBillPayEntities.stream().filter(x -> x.getFkBillNo().equals(rspDatum.getBillNo()) || rspDatum.getBillNo().equals(x.getFkVerificationSheet())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)){
                    RylCountSettlementBillPayEntity rylCountSettlementBillPayEntity = collect.get(0);
                    String countDatePeriod = SettlementUtil.dealSettDate(rylCountSettlementBillPayEntity.getCountStartTime(), rylCountSettlementBillPayEntity.getCountEndTime());
                    String countDatePeriodInt = SettlementUtil.dealSettDateInt(rylCountSettlementBillPayEntity.getCountStartTime(), rylCountSettlementBillPayEntity.getCountEndTime());
                    BillPayStatementDto billPayStatementDto = new BillPayStatementDto();
                    billPayStatementDto.setBusinessMonth(countDatePeriod);
                    billPayStatementDto.setChannelName(rylCountSettlementBillPayEntity.getChannelName());
                    billPayStatementDto.setBillAmount(rspDatum.getOriginalAmount());
                    billPayStatementDto.setDescription(rspDatum.getRemark());
                    String amount = StringUtils.isBlank(rylCountSettlementBillPayEntity.getCommissionChargeTotalAfter()) ? rylCountSettlementBillPayEntity.getCommissionChargeTotal() : rylCountSettlementBillPayEntity.getCommissionChargeTotalAfter();
                    billPayStatementDto.setCommissionCharge(amount);
                    if ("手续费借款单".equals(rspDatum.getBillName())){
                        billPayStatementDto.setBillType(rspDatum.getBillName());
                        billPayStatementDto.setFkBillNo(rspDatum.getBillNo());
                        billPayStatementDto.setVerificationBillType(rspDatum.getLendBillName());
                        billPayStatementDto.setVerificationFkBillNo(rspDatum.getBusinessKey());
                        billPayStatementDto.setVerificationCommissionCharge(rspDatum.getUsedAmount());
                        billPayStatementDto.setNotVerificationCommissionCharge(rspDatum.getBalanceAmount());
                    }else {
                        billPayStatementDto.setBillType("手续费代付单");
                        billPayStatementDto.setFkBillNo(rylCountSettlementBillPayEntity.getFkBillNo());
                        billPayStatementDto.setVerificationBillType(rspDatum.getBillName());
                        billPayStatementDto.setVerificationFkBillNo(rspDatum.getBillNo());
                        billPayStatementDto.setVerificationCommissionCharge(rspDatum.getOriginalCurrencySum());
                        billPayStatementDto.setNotVerificationCommissionCharge(rspDatum.getAvailAmount());
                    }
                    if (map.get(countDatePeriodInt) == null){
                        List<BillPayStatementDto> billPayStatementDtoList = new ArrayList<>();
                        billPayStatementDtoList.add(billPayStatementDto);
                        map.put(countDatePeriodInt,billPayStatementDtoList);
                    }else {
                        List<BillPayStatementDto> billPayStatementDtoList = map.get(countDatePeriodInt);
                        billPayStatementDtoList.add(billPayStatementDto);
                        map.put(countDatePeriodInt,billPayStatementDtoList);
                    }
                }
            }
        }
        return map;
    }

    //定义：标题行 样式
    private CellStyle cellHeadStyleHx(SXSSFWorkbook workbook,String code) {
        CellStyle cellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 12);//设置字体大小
        if(!StringUtil.isBlank(code)){
            font.setColor(IndexedColors.WHITE1.getIndex());
        }
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
//        cellStyle.setBorderLeft(BorderStyle.THICK);
        cellStyle.setBorderTop(BorderStyle.THIN);
        if(!StringUtil.isBlank(code)){
            cellStyle.setFillForegroundColor(IndexedColors.BLUE.getIndex());// 设置背景色
        }else{
            cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());// 设置背景色
        }
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return cellStyle;
    }

    //定义：标题行 样式
    private CellStyle cellHeadStyleHxOne(SXSSFWorkbook workbook,String code) {
        CellStyle cellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 12);//设置字体大小
        if(!StringUtil.isBlank(code)){
            font.setColor(IndexedColors.WHITE1.getIndex());
        }
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THICK);
        cellStyle.setBorderTop(BorderStyle.THIN);
        if(!StringUtil.isBlank(code)){
            cellStyle.setFillForegroundColor(IndexedColors.BLUE.getIndex());// 设置背景色
        }else{
            cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());// 设置背景色
        }
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return cellStyle;
    }

    //定义： 内容 样式
    private CellStyle cellContStyle(SXSSFWorkbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 12);//设置字体大小
        cellStyle.setFont(font);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
//        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        return cellStyle;
    }

    //定义： 内容 样式
    private CellStyle cellContStyleOne(SXSSFWorkbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 12);//设置字体大小
        cellStyle.setFont(font);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THICK);
//        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        return cellStyle;
    }

    //定义： 货币内容 样式
    private CellStyle cellMoneyStyle(SXSSFWorkbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 12);//设置字体大小
        cellStyle.setFont(font);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setDataFormat(workbook.createDataFormat().getFormat("0.00"));
        return cellStyle;
    }

    //定义：描述行 样式
    private CellStyle msStyle(SXSSFWorkbook workbook) {
        CellStyle headStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 12);//设置字体大小
        headStyle.setFont(font);
        headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headStyle.setBorderBottom(BorderStyle.THIN);
        headStyle.setBorderRight(BorderStyle.THIN);
        headStyle.setBorderLeft(BorderStyle.THICK);
        headStyle.setBorderTop(BorderStyle.THIN);
        return headStyle;
    }

    //定义：首行 样式
    private CellStyle handleHeadStyle(SXSSFWorkbook workbook) {
        CellStyle headStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 20);//设置字体大小
        headStyle.setFont(font);
        headStyle.setAlignment(HorizontalAlignment.CENTER);
        headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headStyle.setBorderBottom(BorderStyle.THIN);
        headStyle.setBorderRight(BorderStyle.THIN);
        headStyle.setBorderLeft(BorderStyle.THICK);
        headStyle.setBorderTop(BorderStyle.THICK);
        return headStyle;
    }

    @Async
    public void queryResultDetail(String day,RylCountReportDto rylCountReportDto,List<RylCountReportResDto> mainALLList,Map<String, String> riskMap,Map<String, String> channelMap) {
        QueryWrapper<RylCountSettlementResult> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotEmpty(rylCountReportDto.getChannelCode())){
            RylCountSettlementChannelMappingCfgEntity cfgEntity = null;
            QueryWrapper<RylCountSettlementChannelMappingCfgEntity> cfgQueryWrapper = new QueryWrapper<>();
            cfgQueryWrapper.eq("channel_code",rylCountReportDto.getChannelCode());
            cfgEntity = rylCountSettlementChannelMappingCfgMapper.selectOne(cfgQueryWrapper);
            if(cfgEntity == null){
                return;
            }
            queryWrapper.in("channel_code",Arrays.asList(StringUtil.split(cfgEntity.getSubChannelCode(), "|")));
        }
        queryWrapper.eq("settle_status","Y");
        queryWrapper.eq("count_pay_status",0);
        queryWrapper.eq("business_date",day);
        List<RylCountReportResDto> mainTemp = rylCountSettResultMapper.countUnsettledDetailToExcel(queryWrapper);
        if(!mainTemp.isEmpty()){
            mainTemp.forEach(x->{
                x.setMainRiskName(riskMap.get(x.getMainRiskName()));
                x.setChannelName(channelMap.get(x.getChannelCode()));
                if(!StringUtils.isBlank(x.getSettleStatusReason())){
                    JSONObject JSONA = JSONObject.parseObject(x.getSettleStatusReason());
                    JSONArray mess = (JSONArray) JSONA.get("messages");
                    ArrayList<String> list = mess.toJavaObject(ArrayList.class);
                    if (list.size() >= 1) {
                        String stringPipe = String.join("|", list);
                        x.setSettleStatusReason(stringPipe);
                    }
                }
                if(!StringUtils.isBlank(x.getAppName())){
                    String desensitized = desensitize(x.getAppName());
                    x.setAppName(desensitized);
                }
                if(!StringUtils.isBlank(x.getFirstInsuredName())){
                    String desensitizedIusuredName = desensitize(x.getFirstInsuredName());
                    x.setFirstInsuredName(desensitizedIusuredName);
                }
            });
            mainALLList.addAll(mainTemp);
        }
    }

    public static List<String> getDayBetweenDate(String startTime, String endTime) {
        LocalDate startDate = LocalDate.parse(startTime);;
        LocalDate endDate = LocalDate.parse(endTime);;
        // 创建日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 存储所有日期
        List<String> dateList = new ArrayList<>();
        // 遍历日期范围
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            dateList.add(currentDate.format(formatter));
            currentDate = currentDate.plusDays(1);
        }
        return dateList;
    }

    //定义： 表头内容样式
    private CellStyle cellHandContentStyle(SXSSFWorkbook workbook) {
        CellStyle titleStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 14);
        titleStyle.setFont(font);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setBorderBottom(BorderStyle.THIN);
        titleStyle.setBorderLeft(BorderStyle.THIN);
        titleStyle.setBorderRight(BorderStyle.THIN);
        titleStyle.setBorderTop(BorderStyle.THIN);
        return titleStyle;
    }

    @Override
    public void exportChannelRate(RylCountReportDto rylCountReportDto) {
        Map<String, String> channelMap = Maps.newHashMap();
        Map<String, String> message = Maps.newHashMap();
        List<String> channelCodeList = new ArrayList<>();
        message.put("queryParam", JSONObject.toJSONString(rylCountReportDto));
        FileUploadOutDto fileUploadOutDto = null;
        ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
        singleThreadExecutor.execute(new Runnable() {
            @Override
            public void run() {
                exportChannelRateList(rylCountReportDto, fileUploadOutDto, message);
            }
        });
        singleThreadExecutor.shutdown();
    }

    @SneakyThrows
    public void exportChannelRateList(RylCountReportDto rylCountReportDto,FileUploadOutDto fileUploadOutDto, Map<String, String> message) {
        long time = System.currentTimeMillis();
        Long threadId = Thread.currentThread().getId();
        String fileName = "渠道费率导出明细表" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";;
        String threadName = Thread.currentThread().getName();
        message.put("threadId", String.valueOf(threadId));
        message.put("fileName", fileName);
        SendMessageEvent sendMessageEvent = new SendMessageEvent(message);
        sendMessageEvent.setDataType(CHANNEL_RATE_DETAIL_REPROT);
        sendMessageEvent.setOperate(0);
        String unionId = UUID.randomUUID() + "-" + threadId;
        sendMessageEvent.setUnionId(unionId);
        applicationEventPublisher.publishEvent(sendMessageEvent);
        log.info("当前线程名称:{} 当前线程Id:{}", threadName, threadId);
        if (riskMap.size() < 1) {
            riskMap = rylCountSettlementRateMapper.getRiskList("").stream()
                    .collect(Collectors.toMap(RiskDTO::getRiskCode, RiskDTO::getRiskName));
        }
        if (channelMap.size() < 1) {
            LambdaQueryWrapper<RylCountSettlementChannelCfgEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(RylCountSettlementChannelCfgEntity::getIsDel, "0");
            List<RylCountSettlementChannelCfgEntity> channelList =
                    rylCountSettlementChannelCfgMapper.selectList(lambdaQueryWrapper);
            List<RylCountSettlementChannelCfgEntity> channelRepeatList = channelList.stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(RylCountSettlementChannelCfgEntity::getChannelCode))), ArrayList::new)
            );
            channelMap = channelRepeatList.stream().collect(Collectors.toMap(RylCountSettlementChannelCfgEntity::getChannelCode, RylCountSettlementChannelCfgEntity::getChannelName));
        }
        QueryWrapper<RylCountSettlementChannelLabEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del",0);
        if(!rylCountReportDto.getChannelCodeList().isEmpty()){
            queryWrapper.in("channel_code",rylCountReportDto.getChannelCodeList());
        }
        if(rylCountReportDto.getChannelCodeList().isEmpty() && !rylCountReportDto.getMerchantTypeList().isEmpty()){
            queryWrapper.in("merchant_type",rylCountReportDto.getMerchantTypeList());
        }
        List<RylCountSettlementChannelLabEntity> labEntityList = rylCountSettlementChannelLabMapper.selectList(queryWrapper);
        List<RylCountChannelRateReportDTO> rateALLList = new ArrayList<>();
        List<RylCountChannelRateLevelReportDTO> rateLevelALLList = new ArrayList<>();
        if(!labEntityList.isEmpty()){
            for(RylCountSettlementChannelLabEntity labEntity : labEntityList){
                queryRateDetail(labEntity,rylCountReportDto.getRiskCodeList(),rateALLList,riskMap,rateLevelALLList);
            }
        }
        if(rateALLList.isEmpty()){
            //无数据提示导出失败
            updateLog(unionId, fileUploadOutDto, CHANNEL_RATE_DETAIL_REPROT);
            return;
        }
        @Cleanup
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ExcelWriter excelWriter= EasyExcel.write(os).build();
        try{
            //表头样式策略
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            //设置表头居中对齐
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            //表头前景设置黑色
            headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setBold(true);
            headWriteFont.setFontName("宋体");
            headWriteFont.setFontHeightInPoints((short) 14);
            headWriteCellStyle.setWriteFont(headWriteFont);
            //内容样式策略策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            // 设置背景颜色白色
            contentWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
            // 设置垂直居中为居中对齐
            contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            // 设置左右对齐为靠左对齐
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            // 设置单元格上下左右边框为细边框
            contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
            contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
            contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
            contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
            //创建字体对象
            WriteFont contentWriteFont = new WriteFont();
            //内容字体大小
            contentWriteFont.setFontName("宋体");
            contentWriteFont.setFontHeightInPoints((short) 12);
            contentWriteCellStyle.setWriteFont(contentWriteFont);
            // 初始化表格样式
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            WriteSheet writeSheet = EasyExcel.writerSheet("渠道费率明细").registerWriteHandler(horizontalCellStyleStrategy).head(RylCountChannelRateReportDTO.class).build();
            excelWriter.write(rateALLList, writeSheet);
            if(!rateLevelALLList.isEmpty()){
                writeSheet = EasyExcel.writerSheet("渠道费率分档明细").registerWriteHandler(horizontalCellStyleStrategy).head(RylCountChannelRateLevelReportDTO.class).build();
                excelWriter.write(rateLevelALLList, writeSheet);
            }
            excelWriter.finish();
            byte[] content = os.toByteArray();
            InputStream is = new ByteArrayInputStream(content);
            fileUploadOutDto = fileUpoadUtil.uploadN(is, fileName, CONTNUE_RATE_REPROT_ADDR);
            log.info("结算中心--》渠道费率报表导出，查询明细上传oss结束，耗时:{}", System.currentTimeMillis() - time);
            updateLog(unionId, fileUploadOutDto, CHANNEL_RATE_DETAIL_REPROT);
        } catch (Exception e) {
            // 重置response
            Map<String, String> map = new HashMap<>(2);
            map.put("status", "failure");
            map.put("message", "上传文件失败" + e.getMessage());
            updateLog(unionId, fileUploadOutDto, CHANNEL_RATE_DETAIL_REPROT);
            log.error("error-message:{}", e);
        } finally {
            os.flush();
        }
    }

    @Async
    public void queryRateDetail(RylCountSettlementChannelLabEntity labEntity,List<String> riskCodeList,List<RylCountChannelRateReportDTO> mainALLList,Map<String, String> riskMap
            ,List<RylCountChannelRateLevelReportDTO> rateLevelALLList) {
        QueryWrapper<RylCountSettlementRateEntity> queryWrapper = new QueryWrapper<>();
        List<String> subChannelCodes = Arrays.asList(StringUtil.split(labEntity.getSubChannelCode(), "|"));
        queryWrapper.in("channel_code",subChannelCodes);
        if(!riskCodeList.isEmpty()){
            queryWrapper.in("main_risk_code",riskCodeList);
        }
        queryWrapper.eq("is_del",0);
        List<RylCountChannelRateReportDTO> rateTemp = rylCountSettlementRateMapper.selectRateDetailList(queryWrapper);
        if(!rateTemp.isEmpty()){
            rateTemp.forEach(x->{
                x.setChannelName(labEntity.getChannelName());
                x.setMainRiskName(riskMap.get(x.getMainRiskCode()));
                if(!StringUtils.isBlank(x.getRiskCode()) && !"DEFAULT".equals(x.getRiskCode())){
                    x.setRiskName(riskMap.get(x.getRiskCode()));
                }
            });
            mainALLList.addAll(rateTemp);
            List<String> rateIDList = rateTemp.stream().map(RylCountChannelRateReportDTO::getRateID).distinct().collect(Collectors.toList());
            QueryWrapper<RylCountSettlementRateLevelEntity> levelEntityQueryWrapper = new QueryWrapper<>();
            levelEntityQueryWrapper.eq("is_del",0);
            levelEntityQueryWrapper.in("rate_id",rateIDList);
            List<RylCountChannelRateLevelReportDTO> ratelevelList = rylCountSettlementRateLevelMapper.selectRateLevelDetailList(levelEntityQueryWrapper);
            if(!ratelevelList.isEmpty()){
                rateLevelALLList.addAll(ratelevelList);
            }
        }
    }


    @Override
    public void exportForecastCommission(RylCountReportDto rylCountReportDto) {
        Map<String, String> channelMap = Maps.newHashMap();
        Map<String, String> message = Maps.newHashMap();
        List<String> channelCodeList = new ArrayList<>();
        message.put("queryParam", JSONObject.toJSONString(rylCountReportDto));
        if(StringUtils.isBlank(rylCountReportDto.getStartMonth())){
            rylCountReportDto.setStartMonth(DateUtil.format(DateUtil.beginOfMonth(new Date()), "yyyy-MM-dd"));
        }
        LocalDate endDate = LocalDate.parse(rylCountReportDto.getStartMonth()).plusMonths(5);
        rylCountReportDto.setEndMonth(endDate.toString());
        FileUploadOutDto fileUploadOutDto = null;
        ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
        singleThreadExecutor.execute(new Runnable() {
            @Override
            public void run() {
                exportForecastCommissionList(rylCountReportDto, fileUploadOutDto, message);
            }
        });
        singleThreadExecutor.shutdown();
    }

    @SneakyThrows
    public void exportForecastCommissionList(RylCountReportDto rylCountReportDto,FileUploadOutDto fileUploadOutDto, Map<String, String> message) {
        long time = System.currentTimeMillis();
        Long threadId = Thread.currentThread().getId();
        String formattedDate = LocalDate.parse(rylCountReportDto.getStartMonth(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).format(DateTimeFormatter.ofPattern("yyyyMM"));
        String fileName = "手续费追佣预测报表+" + formattedDate + ".xlsx";;
        String threadName = Thread.currentThread().getName();
        message.put("threadId", String.valueOf(threadId));
        message.put("fileName", fileName);
        SendMessageEvent sendMessageEvent = new SendMessageEvent(message);
        sendMessageEvent.setDataType(FORECAST_COMMISSION_REPROT);
        sendMessageEvent.setOperate(0);
        String unionId = UUID.randomUUID() + "-" + threadId;
        sendMessageEvent.setUnionId(unionId);
        applicationEventPublisher.publishEvent(sendMessageEvent);
        log.info("当前线程名称:{} 当前线程Id:{}", threadName, threadId);
        if (channelMap.size() < 1) {
            LambdaQueryWrapper<RylCountSettlementChannelCfgEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(RylCountSettlementChannelCfgEntity::getIsDel, "0");
            List<RylCountSettlementChannelCfgEntity> channelList =
                    rylCountSettlementChannelCfgMapper.selectList(lambdaQueryWrapper);
            List<RylCountSettlementChannelCfgEntity> channelRepeatList = channelList.stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(RylCountSettlementChannelCfgEntity::getChannelCode))), ArrayList::new)
            );
            channelMap = channelRepeatList.stream().collect(Collectors.toMap(RylCountSettlementChannelCfgEntity::getChannelCode, RylCountSettlementChannelCfgEntity::getChannelName));
        }
        QueryWrapper<RylCountSettlementChannelLabEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del",0);
        queryWrapper.ne("merchant_type","1");
        if(!rylCountReportDto.getChannelCodeList().isEmpty()){
            queryWrapper.in("channel_code",rylCountReportDto.getChannelCodeList());
        }
        List<RylCountSettlementChannelLabEntity> labEntityList = rylCountSettlementChannelLabMapper.selectList(queryWrapper);
        List<Map<String, Object>> mainTemp = new ArrayList<>();
        if(!labEntityList.isEmpty()){
            for(RylCountSettlementChannelLabEntity labEntity : labEntityList){
                queryForecastDetail(labEntity,mainTemp,rylCountReportDto.getStartMonth(),rylCountReportDto.getEndMonth());
            }
        }
        if(mainTemp.isEmpty()){
            //无数据提示导出失败
            updateLog(unionId, fileUploadOutDto, FORECAST_COMMISSION_REPROT);
            return;
        }
        @Cleanup
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ExcelWriter excelWriter= EasyExcel.write(os).build();
        try{
            SXSSFWorkbook workbook = new SXSSFWorkbook();
            CellStyle cellStyle = cellContentStyle(workbook);
            SXSSFSheet sheetBaseCharge = workbook.createSheet("手续费预测报表明细");
            //表头格式
            CellStyle titleStyle = cellHandContentStyle(workbook);
            CellStyle borderStyle = workbook.createCellStyle();
            borderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            borderStyle.setBorderBottom(BorderStyle.THIN);
            borderStyle.setBorderLeft(BorderStyle.THIN);
            borderStyle.setBorderRight(BorderStyle.THIN);
            borderStyle.setBorderTop(BorderStyle.THIN);
            //提示行内容格式
            CellStyle hintStyle = workbook.createCellStyle();
            hintStyle.setAlignment(HorizontalAlignment.LEFT);
            hintStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
            hintStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            hintStyle.setBorderBottom(BorderStyle.THIN);
            hintStyle.setBorderLeft(BorderStyle.THIN);
            hintStyle.setBorderRight(BorderStyle.THIN);
            hintStyle.setBorderTop(BorderStyle.THIN);
            sheetBaseCharge.setDefaultColumnWidth(35);

            SXSSFRow titleRow = sheetBaseCharge.createRow(0);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue("管理部门");
            titleCell.setCellStyle(titleStyle);

            Cell titleCell1 = titleRow.createCell(1);
            titleCell1.setCellValue("渠道（结算维度）");
            titleCell1.setCellStyle(titleStyle);

            Cell titleCell2 = titleRow.createCell(2);
            titleCell2.setCellValue("业务板块处理意见");
            titleCell2.setCellStyle(titleStyle);

            Cell titleCell3 = titleRow.createCell(3);
            titleCell3.setCellValue("业务板块处理意见");
            titleCell3.setCellStyle(titleStyle);

            Cell titleCell4 = titleRow.createCell(4);
            titleCell4.setCellValue("业务管理部复核意见");
            titleCell4.setCellStyle(titleStyle);

            Cell titleCell5 = titleRow.createCell(5);
            titleCell5.setCellValue("应付手续费-预计可能追佣金额（单位：元）（应付月份数-应追月份数）");
            titleCell5.setCellStyle(titleStyle);

            Cell titleCell6 = titleRow.createCell(10);
            titleCell6.setCellValue("应付未付（续期业务不包括投连）评估条件：按照第二、三保单年度的续期预估手续费");
            titleCell6.setCellStyle(titleStyle);

            Cell titleCell7 = titleRow.createCell(17);
            titleCell7.setCellValue("追佣（退保及失效、申诉补偿、协议退保业务）评估条件：近6个月平均特殊保全、退保及失效防套利预估平均值");
            titleCell7.setCellStyle(titleStyle);

            SXSSFRow headRow = sheetBaseCharge.createRow(1);
            Map<String, String> headName = getForecastListMap(rylCountReportDto.getStartMonth(),rylCountReportDto.getEndMonth());
            // 合并A1列
            sheetBaseCharge.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));
            // 合并A2列
            sheetBaseCharge.addMergedRegion(new CellRangeAddress(0, 1, 1, 1));
            // 合并C1-D1列
            sheetBaseCharge.addMergedRegion(new CellRangeAddress(0, 0, 2, 3));
            // 合并E2列
            sheetBaseCharge.addMergedRegion(new CellRangeAddress(0, 1, 4, 4));
            // 合并F1-J1列
            sheetBaseCharge.addMergedRegion(new CellRangeAddress(0, 0, 5, 9));
            // 合并K1-Q1列
            sheetBaseCharge.addMergedRegion(new CellRangeAddress(0, 0, 10, 16));
            // 合并R1-X1列
            sheetBaseCharge.addMergedRegion(new CellRangeAddress(0, 0, 17, 23));
            // 设置行高
            titleRow.setHeightInPoints(50);
            headRow.setHeightInPoints(50);
            titleRow.setRowStyle(borderStyle);
            headRow.setRowStyle(borderStyle);
            int[] cells = {0};
            headName.forEach((key, value) -> {
                SXSSFCell headCell = headRow.createCell(cells[0]);
                headCell.setCellStyle(titleStyle);
                headCell.setCellValue(value);
                cells[0]++;
            });
            int textRows = 2;
            for (Map<String, Object> baseData : mainTemp) {
                SXSSFRow textRow = sheetBaseCharge.createRow(textRows);
                int[] textCells = {0};
                headName.forEach((key, value) -> {
                    SXSSFCell textCell = textRow.createCell(textCells[0]);
                    textCell.setCellStyle(cellStyle);
                    if(key.contains("value")){
                        String commission = baseData.get(key).toString().substring(0, baseData.get(key).toString().lastIndexOf('|'));
                        BigDecimal commissionCharge = new BigDecimal(commission);
                        textCell.setCellValue((commissionCharge).doubleValue());
                        cellStyle.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));
                        if(baseData.get(key).toString().contains("grey")){
                            Font font1 = workbook.createFont();
                            font1.setBold(true);
                            font1.setFontName("宋体");
                            font1.setFontHeightInPoints((short) 12);//设置字体大小
                            font1.setColor(IndexedColors.BLACK.getIndex());
                            CellStyle style = workbook.createCellStyle();
                            style.setFont(font1);
                            style.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));
//                            short fillColor = getColorIndex(166,166,166);
//                            style.setFillForegroundColor(fillColor);
                            style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
                            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                            style.setBorderBottom(BorderStyle.THIN);
                            style.setBorderLeft(BorderStyle.THIN);
                            style.setBorderRight(BorderStyle.THIN);
                            style.setBorderTop(BorderStyle.THIN);
                            textCell.setCellStyle(style);
                        }
                        if(baseData.get(key).toString().contains("green")){
                            Font font1 = workbook.createFont();
                            font1.setBold(true);
                            font1.setFontName("宋体");
                            font1.setFontHeightInPoints((short) 12);//设置字体大小
                            font1.setColor(IndexedColors.BLACK.getIndex());
                            CellStyle style = workbook.createCellStyle();
                            style.setFont(font1);
                            style.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));
//                            short fillColor = getColorIndex(125,212,50);
//                            style.setFillForegroundColor(fillColor);
                            style.setFillForegroundColor(IndexedColors.LIME.getIndex());
                            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                            style.setBorderBottom(BorderStyle.THIN);
                            style.setBorderLeft(BorderStyle.THIN);
                            style.setBorderRight(BorderStyle.THIN);
                            style.setBorderTop(BorderStyle.THIN);
                            textCell.setCellStyle(style);
                        }
                        if(baseData.get(key).toString().contains("pink")){
                            Font font1 = workbook.createFont();
                            font1.setBold(true);
                            font1.setFontName("宋体");
                            font1.setFontHeightInPoints((short) 12);//设置字体大小
                            font1.setColor(IndexedColors.BLACK.getIndex());
                            CellStyle style = workbook.createCellStyle();
                            style.setFont(font1);
                            style.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));
//                            short fillColor = getColorIndex(254,177,178);
//                            style.setFillForegroundColor(fillColor);
                            style.setFillForegroundColor(IndexedColors.ROSE.getIndex());
                            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                            style.setBorderBottom(BorderStyle.THIN);
                            style.setBorderLeft(BorderStyle.THIN);
                            style.setBorderRight(BorderStyle.THIN);
                            style.setBorderTop(BorderStyle.THIN);
                            textCell.setCellStyle(style);
                        }
                        if(baseData.get(key).toString().contains("yellow")){
                            Font font1 = workbook.createFont();
                            font1.setBold(true);
                            font1.setFontName("宋体");
                            font1.setFontHeightInPoints((short) 12);//设置字体大小
                            font1.setColor(IndexedColors.BLACK.getIndex());
                            CellStyle style = workbook.createCellStyle();
                            style.setFont(font1);
                            style.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));
//                            short fillColor = getColorIndex(255,219,67);
//                            style.setFillForegroundColor(fillColor);
                            style.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
                            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                            style.setBorderBottom(BorderStyle.THIN);
                            style.setBorderLeft(BorderStyle.THIN);
                            style.setBorderRight(BorderStyle.THIN);
                            style.setBorderTop(BorderStyle.THIN);
                            textCell.setCellStyle(style);
                        }
                        if(baseData.get(key).toString().contains("blue")){
                            Font font1 = workbook.createFont();
                            font1.setBold(true);
                            font1.setFontName("宋体");
                            font1.setFontHeightInPoints((short) 12);//设置字体大小
                            font1.setColor(IndexedColors.BLACK.getIndex());
                            CellStyle style = workbook.createCellStyle();
                            style.setFont(font1);
                            style.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));
//                            short fillColor = getColorIndex(0,178,246);
//                            style.setFillForegroundColor(fillColor);
                            style.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());
                            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                            style.setBorderBottom(BorderStyle.THIN);
                            style.setBorderLeft(BorderStyle.THIN);
                            style.setBorderRight(BorderStyle.THIN);
                            style.setBorderTop(BorderStyle.THIN);
                            textCell.setCellStyle(style);
                        }
                    }else if (baseData.get(key) != null && (baseData.get(key) instanceof BigDecimal)) {
                        //如果是BigDecimal，则以数字格式输出，并且设置单元格为可以计算的格式
                        textCell.setCellValue(((BigDecimal) baseData.get(key)).doubleValue());
                        cellStyle.setDataFormat(workbook.createDataFormat().getFormat("#,##0.00"));
                    }else{
                        textCell.setCellValue(baseData.get(key) == null ? "" : baseData.get(key).toString());
                    }
                    textCells[0]++;
                });
                textRows++;
            }
            ByteArrayOutputStream bufferedOutPut = new ByteArrayOutputStream();
            bufferedOutPut.flush();
            workbook.write(bufferedOutPut);
            bufferedOutPut.close();
            ByteArrayInputStream swapStream = new ByteArrayInputStream(bufferedOutPut.toByteArray());
            fileUploadOutDto = fileUpoadUtil.uploadN(swapStream, fileName, CONTNUE_RATE_REPROT_ADDR);
            log.info("结算进度表导出，查询明细上传oss结束，耗时:{}", System.currentTimeMillis() - time);
            updateLog(unionId, fileUploadOutDto, FORECAST_COMMISSION_REPROT);
        } catch (Exception e) {
            // 重置response
            Map<String, String> map = new HashMap<>(2);
            map.put("status", "failure");
            map.put("message", "上传文件失败" + e.getMessage());
            updateLog(unionId, fileUploadOutDto, FORECAST_COMMISSION_REPROT);
            log.error("error-message:{}", e);
        } finally {
            os.flush();
        }
    }

    @Async
    public void queryForecastDetail(RylCountSettlementChannelLabEntity labEntity,List<Map<String, Object>> mainTemp,String startMonth,String endMonth) throws Exception {
        Map<String, Object> temp = new HashMap<>();
        temp.put("depName",labEntity.getLabDepName());
        temp.put("channelName",labEntity.getChannelName());

        List<String> monthList = getMonthBetweenDate(startMonth,endMonth);
        //应付未付部分
        BigDecimal forecastMonth1 = new BigDecimal(0);
        BigDecimal forecastMonth2 = new BigDecimal(0);
        BigDecimal forecastMonth3 = new BigDecimal(0);
        BigDecimal forecastMonth4 = new BigDecimal(0);
        BigDecimal forecastMonth5 = new BigDecimal(0);
        BigDecimal forecastMonth6 = new BigDecimal(0);
        BigDecimal sumForecastMonth = new BigDecimal(0);
        //追佣部分
        BigDecimal chasingCommission1 = new BigDecimal(0);
        BigDecimal chasingCommission2 = new BigDecimal(0);
        BigDecimal chasingCommission3 = new BigDecimal(0);
        BigDecimal chasingCommission4 = new BigDecimal(0);
        BigDecimal chasingCommission5 = new BigDecimal(0);
        BigDecimal chasingCommission6 = new BigDecimal(0);
        BigDecimal sumChasingCommission = new BigDecimal(0);

        //应付未付数据组装
        SimpleDateFormat targetFormat = new SimpleDateFormat("yyyy-MM");
        List<String> subChannelCodes = Arrays.asList(StringUtil.split(labEntity.getSubChannelCode(), "|"));
        QueryWrapper<RylCountForecastResultEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("channel_code",subChannelCodes);
        Date startDate = Date.from(LocalDate.parse(startMonth).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(LocalDate.parse(endMonth).atStartOfDay(ZoneId.systemDefault()).toInstant());
        queryWrapper.between("forecast_month",targetFormat.format(startDate),targetFormat.format(endDate));
        queryWrapper.orderByAsc("forecast_month");
        List<RylCountForecastResultEntity> forecastResultList = rylCountForecastResultMapper.selectList(queryWrapper);

        //追佣数据组装
        QueryWrapper<RylCountCommissionForecastEntity> cfQueryWrapper = new QueryWrapper<>();
        cfQueryWrapper.eq("channel_code",labEntity.getChannelCode());
        cfQueryWrapper.between("month",targetFormat.format(startDate),targetFormat.format(endDate));
        cfQueryWrapper.orderByAsc("month");
        List<RylCountCommissionForecastEntity> cfResultList = rylCountCommissionForecastMapper.selectList(cfQueryWrapper);

        for(String x : monthList){
            if(!forecastResultList.isEmpty()){
                List<RylCountForecastResultEntity> forecastList = forecastResultList.stream().filter(a-> !StringUtils.isBlank(a.getForecastMonth()) && a.getForecastMonth().equals(x)).collect(Collectors.toList());
                if(!forecastList.isEmpty()){
                    BigDecimal sumCommission = forecastList.stream().map(RylCountForecastResultEntity::getCommissionCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                    LocalDate date = LocalDate.parse(x+"-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    String monthValue = String.valueOf(date.getMonthValue())+"月";
                    String key = "payable"+monthValue;
                    temp.put(key,sumCommission);
                    List<String> mList = getMonthBetweenDate(startMonth,x);
                    if(mList.size() == 1){
                        forecastMonth1 = forecastMonth1.add(sumCommission);
                        sumForecastMonth = sumForecastMonth.add(forecastMonth1);
                    }
                    if(mList.size() == 2){
                        forecastMonth2 = forecastMonth2.add(sumCommission);
                        sumForecastMonth = sumForecastMonth.add(forecastMonth2);
                    }
                    if(mList.size() == 3){
                        forecastMonth3 = forecastMonth3.add(sumCommission);
                        sumForecastMonth = sumForecastMonth.add(forecastMonth3);
                    }
                    if(mList.size() == 4){
                        forecastMonth4 = forecastMonth4.add(sumCommission);
                        sumForecastMonth = sumForecastMonth.add(forecastMonth4);
                    }
                    if(mList.size() == 5){
                        forecastMonth5 = forecastMonth5.add(sumCommission);
                        sumForecastMonth = sumForecastMonth.add(forecastMonth5);
                    }
                    if(mList.size() == 6){
                        forecastMonth6 = forecastMonth6.add(sumCommission);
                        sumForecastMonth = sumForecastMonth.add(forecastMonth6);
                    }
                }else{
                    LocalDate date = LocalDate.parse(x+"-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    String monthValue = String.valueOf(date.getMonthValue())+"月";
                    String key = "payable"+monthValue;
                    temp.put(key,"/");
                }
            }else{
                LocalDate date = LocalDate.parse(x+"-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                String monthValue = String.valueOf(date.getMonthValue())+"月";
                String key = "payable"+monthValue;
                temp.put(key,"/");
            }

            if(!cfResultList.isEmpty()){
                List<RylCountCommissionForecastEntity> newCfResultList = cfResultList.stream().filter(a-> !StringUtils.isBlank(a.getMonth()) && a.getMonth().equals(x)).collect(Collectors.toList());
                if(!newCfResultList.isEmpty()){
                    BigDecimal chasingCommission = new BigDecimal(0);
                    //校验是否存在实付追佣数据
                    List<RylCountCommissionForecastEntity> chasingList = newCfResultList.stream().filter(a-> a.getChasingCommission() != null).collect(Collectors.toList());
                    if(!chasingList.isEmpty()){
                        chasingCommission = chasingCommission.add(chasingList.get(0).getChasingCommission());
                    }else{
                        List<RylCountCommissionForecastEntity> unChasingList = newCfResultList.stream().filter(a-> a.getUnpaidChasingCommission() != null).collect(Collectors.toList());
                        if(!unChasingList.isEmpty()){
                            chasingCommission = chasingCommission.add(unChasingList.get(0).getUnpaidChasingCommission());
                        }
                    }
                    LocalDate date = LocalDate.parse(x+"-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    String monthValue = String.valueOf(date.getMonthValue())+"月";
                    String key = "chasing"+monthValue;
                    temp.put(key,chasingCommission);
                    List<String> mList = getMonthBetweenDate(startMonth,x);
                    if(mList.size() == 1){
                        chasingCommission1 = chasingCommission1.add(chasingCommission);
                        sumChasingCommission = sumChasingCommission.add(chasingCommission1);
                    }
                    if(mList.size() == 2){
                        chasingCommission2 = chasingCommission2.add(chasingCommission);
                        sumChasingCommission = sumChasingCommission.add(chasingCommission2);
                    }
                    if(mList.size() == 3){
                        chasingCommission3 = chasingCommission3.add(chasingCommission);
                        sumChasingCommission = sumChasingCommission.add(chasingCommission3);
                    }
                    if(mList.size() == 4){
                        chasingCommission4 = chasingCommission4.add(chasingCommission);
                        sumChasingCommission = sumChasingCommission.add(chasingCommission4);
                    }
                    if(mList.size() == 5){
                        chasingCommission5 = chasingCommission5.add(chasingCommission);
                        sumChasingCommission = sumChasingCommission.add(chasingCommission5);
                    }
                    if(mList.size() == 6){
                        chasingCommission6 = chasingCommission6.add(chasingCommission);
                        sumChasingCommission = sumChasingCommission.add(chasingCommission6);
                    }
                }else{
                    LocalDate date = LocalDate.parse(x+"-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    String monthValue = String.valueOf(date.getMonthValue())+"月";
                    String key = "chasing"+monthValue;
                    temp.put(key,"/");
                }
            }else{
                LocalDate date = LocalDate.parse(x+"-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                String monthValue = String.valueOf(date.getMonthValue())+"月";
                String key = "chasing"+monthValue;
                temp.put(key,"/");
            }
        }
        if(sumForecastMonth.compareTo(new BigDecimal(0)) == 0){
            temp.put("payableSum","/");
        }else{
            temp.put("payableSum",sumForecastMonth);
        }

        if(sumChasingCommission.compareTo(new BigDecimal(0)) == 0){
            temp.put("chasingSum","/");
        }else{
            temp.put("chasingSum",sumChasingCommission);
        }

        //预测数据组装
        BigDecimal value1 = forecastMonth1.add(chasingCommission1);
        if (value1.compareTo(BigDecimal.ZERO) < 0) {
            temp.put("value1",value1.toString()+"|"+"grey");
        }else{
            temp.put("value1",value1+"|NO");
        }
        BigDecimal value2 = forecastMonth1.add(forecastMonth2).add(forecastMonth3).add(chasingCommission1).add(chasingCommission2).add(chasingCommission3);
        if (value2.compareTo(BigDecimal.ZERO) < 0) {
            temp.put("value2",value2.toString()+"|"+"green");
        }else{
            temp.put("value2",value2+"|NO");
        }
        BigDecimal value3 = sumForecastMonth.add(sumChasingCommission);
        if (value3.compareTo(BigDecimal.ZERO) < 0) {
            temp.put("value3",value3.toString()+"|"+"pink");
        }else{
            temp.put("value3",value3+"|NO");
        }
        BigDecimal value4 = forecastMonth1.add(forecastMonth2).add(forecastMonth3).add(sumChasingCommission);
        if (value4.compareTo(BigDecimal.ZERO) < 0) {
            temp.put("value4",value4.toString()+"|"+"yellow");
        }else{
            temp.put("value4",value4+"|NO");
        }
        BigDecimal doubleChasing = sumChasingCommission.multiply(new BigDecimal(2));
        BigDecimal value5 = sumForecastMonth.add(doubleChasing);
        if (value5.compareTo(BigDecimal.ZERO) < 0) {
            temp.put("value5",value5.toString()+"|"+"blue");
        }else{
            temp.put("value5",value5+"|NO");
        }
        mainTemp.add(temp);
    }

    private Map<String, String> getForecastListMap(String startMonth,String endMonth) {
        Map<String, String> aliasMap = new LinkedHashMap<>();
        aliasMap.put("depName", "管理部门");
        aliasMap.put("channelName", "渠道（结算维度）");
        aliasMap.put("isPause", "是否缓结");
        aliasMap.put("remark", "陈述理由");
        aliasMap.put("depRemark", "业务管理部复核意见");
        aliasMap.put("value1", "1—1");
        aliasMap.put("value2", "3—3");
        aliasMap.put("value3", "6—6");
        aliasMap.put("value4", "3—6");
        aliasMap.put("value5", "6—12");
        List<String> monthList = getMonthBetweenDate(startMonth,endMonth);
        for(String x : monthList){
            LocalDate date = LocalDate.parse(x+"-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String monthValue = String.valueOf(date.getMonthValue())+"月";
            String key = "payable"+monthValue;
            aliasMap.put(key, monthValue);
        }
        aliasMap.put("payableSum", "合计");
        for(String x : monthList){
            LocalDate date = LocalDate.parse(x+"-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String monthValue = String.valueOf(date.getMonthValue())+"月";
            String key = "chasing"+monthValue;
            aliasMap.put(key, monthValue);
        }
        aliasMap.put("chasingSum", "合计");
        return aliasMap;
    }

    private short getColorIndex(int red,int green,int blue) {
        // 创建 Color 对象
        Color c = new Color(red, green, blue);
        if(c == null){
            return IndexedColors.WHITE.getIndex();
        }else{
            return (short) (c.getRed() + c.getGreen() + c.getBlue());
        }
    }

    @Override
    public void fhSettExport(RylCountReportDto rylCountReportDto) {
        Map<String, String> message = Maps.newHashMap();
        message.put("queryParam", JSONObject.toJSONString(rylCountReportDto));
        FileUploadOutDto fileUploadOutDto = null;
        ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
        //条件查询导出
        singleThreadExecutor.execute(new Runnable() {
            @Override
            public void run() {
                fhSettExportList(rylCountReportDto, fileUploadOutDto, message);
            }
        });
        singleThreadExecutor.shutdown();
    }

    @SneakyThrows
    private void fhSettExportList(RylCountReportDto rylCountReportDto, FileUploadOutDto fileUploadOutDto, Map<String, String> message) {
        long time = System.currentTimeMillis();
        Long threadId = Thread.currentThread().getId();
        QueryWrapper<RylCountSettlementFHUploadPayEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(rylCountReportDto.getChannelCodeList()),"channel_code",rylCountReportDto.getChannelCodeList());
        String fileName = "";
        if(!StringUtil.isBlank(rylCountReportDto.getStartMonth())){
            queryWrapper.apply(" DATE_FORMAT(sett_month,'%Y-%m') = "+"'"+rylCountReportDto.getStartMonth()+"'");
            fileName = "非弘结算数据报表"+rylCountReportDto.getStartMonth()+".xlsx";
        }else {
            queryWrapper.apply(" DATE_FORMAT(sett_month,'%Y-%m') >= "+"'2023-01'");
            String formattedDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
            fileName = "非弘结算数据报表2023-01-"+formattedDate+".xlsx";
        }
        if (StringUtils.isNotEmpty(rylCountReportDto.getStartDate()) &&  StringUtils.isNotEmpty(rylCountReportDto.getEndDate())){
            queryWrapper.apply(" DATE_FORMAT(create_time,'%Y-%m-%d') >= "+"'"+(rylCountReportDto.getStartDate())+"'");
            queryWrapper.apply(" DATE_FORMAT(create_time,'%Y-%m-%d') <= "+"'"+(rylCountReportDto.getEndDate())+"'");
        }
        int count = fhUploadPayMapper.selectCount(queryWrapper);
        if (count == 0) {
            return;
        }
        String threadName = Thread.currentThread().getName();
        message.put("threadId", String.valueOf(threadId));
        message.put("fileName", fileName);
        SendMessageEvent sendMessageEvent = new SendMessageEvent(message);
        sendMessageEvent.setDataType(FH_SETT_REPROT);
        sendMessageEvent.setOperate(0);
        String unionId = UUID.randomUUID() + "-" + threadId;
        sendMessageEvent.setUnionId(unionId);
        applicationEventPublisher.publishEvent(sendMessageEvent);
        log.info("当前线程名称:{} 当前线程Id:{}", threadName, threadId);
        List<RylCountFHSettReportDto> rylCountFHSettReportDtos = fhUploadPayMapper.selectFHUploadPayExport(queryWrapper);
        if(rylCountFHSettReportDtos.isEmpty()){
            //无数据提示导出失败
            updateLog(unionId, fileUploadOutDto, FH_SETT_REPROT);
            return;
        }
        try{
            //非弘结算数据处理
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//            ExcelWriter excelWriter= EasyExcel.write(outputStream).build();
            // 写入数据
            EasyExcel.write(outputStream,RylCountFHSettReportDto.class)
                    .sheet(fileName)
                    .doWrite(rylCountFHSettReportDtos);
            // 将输出流转换为InputStream
            byte[] content = outputStream.toByteArray();
            InputStream inputStream = new ByteArrayInputStream(content);
            // 关闭输出流
            outputStream.close();
            fileUploadOutDto = fileUpoadUtil.uploadN(inputStream, fileName, CONTNUE_RATE_REPROT_ADDR);
            log.info("非弘结算数据报表，查询明细上传oss结束，耗时:{}", System.currentTimeMillis() - time);
            updateLog(unionId, fileUploadOutDto, FH_SETT_REPROT);
        }catch (Exception e){
            Map<String, String> map = new HashMap<>(2);
            map.put("status", "failure");
            map.put("message", "上传文件失败" + e.getMessage());
            updateLog(unionId, fileUploadOutDto, FH_SETT_REPROT);
            log.error("error-message:{}", e);
        }
    }

    @Override
    public void commissionPaidAndUnpaidExport(RylCountCommissionDTO rylCountCommissionDTO) {
        Map<String, String> message = Maps.newHashMap();
        message.put("queryParam", JSONObject.toJSONString(rylCountCommissionDTO));
        FileUploadOutDto fileUploadOutDto = null;
        ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
        //条件查询导出
        singleThreadExecutor.execute(new Runnable() {
            @Override
            public void run() {
                commissionPaidAndUnpaidExportList(rylCountCommissionDTO, fileUploadOutDto, message);
            }
        });
        singleThreadExecutor.shutdown();
    }

    @SneakyThrows
    private void commissionPaidAndUnpaidExportList(RylCountCommissionDTO rylCountCommissionDTO, FileUploadOutDto fileUploadOutDto, Map<String, String> message) {
        long time = System.currentTimeMillis();
        Long threadId = Thread.currentThread().getId();
        String fileName = "手续费已付未付报表" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";;
        String threadName = Thread.currentThread().getName();
        message.put("threadId", String.valueOf(threadId));
        message.put("fileName", fileName);
        SendMessageEvent sendMessageEvent = new SendMessageEvent(message);
        sendMessageEvent.setDataType(COMMISSION_PAIDANDUNPAID_REPROT);
        sendMessageEvent.setOperate(0);
        String unionId = UUID.randomUUID() + "-" + threadId;
        sendMessageEvent.setUnionId(unionId);
        applicationEventPublisher.publishEvent(sendMessageEvent);
        log.info("当前线程名称:{} 当前线程Id:{}", threadName, threadId);
        if (riskMap.size() < 1) {
            riskMap = rylCountSettlementRateMapper.getRiskList("").stream()
                    .collect(Collectors.toMap(RiskDTO::getRiskCode, RiskDTO::getRiskName));
        }
        //默认时间处理
        if(StringUtil.isBlank(rylCountCommissionDTO.getStartMonth()) && StringUtils.isBlank(rylCountCommissionDTO.getEndMonth())){
            rylCountCommissionDTO.setStartMonth("2023-01-01");
            rylCountCommissionDTO.setEndMonth(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
        QueryWrapper<RylCountSettlementChannelLabEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del",0);
        if(!rylCountCommissionDTO.getMerchantTypeList().isEmpty()){
            if(rylCountCommissionDTO.getMerchantTypeList().size() == 3){
                queryWrapper.ne("merchant_type","1");
            }else{
                if(rylCountCommissionDTO.getMerchantTypeList().contains("4")){
                    if(rylCountCommissionDTO.getMerchantTypeList().size() == 1){
                        queryWrapper.eq("lab_channel_type","3");
                    }else{
                        String merchantType = rylCountCommissionDTO.getMerchantTypeList().stream().collect(Collectors.joining(","));
                        queryWrapper.apply("(lab_channel_type = '3' or merchant_type in (" + merchantType + ") )");
                    }
                }else{
                    queryWrapper.in("merchant_type",rylCountCommissionDTO.getMerchantTypeList());
                }
            }
        }
        if(!rylCountCommissionDTO.getDeptCodeList().isEmpty()){
            queryWrapper.in("lab_dep",rylCountCommissionDTO.getDeptCodeList());
        }
        if(!rylCountCommissionDTO.getChannelCodeList().isEmpty()){
            queryWrapper.in("channel_code",rylCountCommissionDTO.getChannelCodeList());
        }
        List<RylCountSettlementChannelLabEntity> labEntityList = rylCountSettlementChannelLabMapper.selectList(queryWrapper);
        List<RylCountCommissionResDTO> countDTOList = new ArrayList<>();
        if(!labEntityList.isEmpty()){
            for(RylCountSettlementChannelLabEntity labEntity : labEntityList){
                log.info("手续费已付未付报表组装数据渠道编码"+labEntity.getChannelCode()+"渠道名称"+labEntity.getChannelName());
                queryCommissionDetail(labEntity,countDTOList,riskMap,rylCountCommissionDTO);
            }
        }
        if(countDTOList.isEmpty()){
            //无数据提示导出失败
            updateLog(unionId, fileUploadOutDto, COMMISSION_PAIDANDUNPAID_REPROT);
            return;
        }
        @Cleanup
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ExcelWriter excelWriter= EasyExcel.write(os).build();
        try{
            //表头样式策略
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            //设置表头居中对齐
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            //表头前景设置黑色
            headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setBold(true);
            headWriteFont.setFontName("宋体");
            headWriteFont.setFontHeightInPoints((short) 14);
            headWriteCellStyle.setWriteFont(headWriteFont);
            //内容样式策略策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            // 设置背景颜色白色
            contentWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
            // 设置垂直居中为居中对齐
            contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            // 设置左右对齐为靠左对齐
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            // 设置单元格上下左右边框为细边框
            contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
            contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
            contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
            contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
            //创建字体对象
            WriteFont contentWriteFont = new WriteFont();
            //内容字体大小
            contentWriteFont.setFontName("宋体");
            contentWriteFont.setFontHeightInPoints((short) 12);
            contentWriteCellStyle.setWriteFont(contentWriteFont);
            // 初始化表格样式
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            WriteSheet writeSheet = EasyExcel.writerSheet("已付未付报表明细").registerWriteHandler(horizontalCellStyleStrategy).head(RylCountCommissionResDTO.class).build();
            excelWriter.write(countDTOList, writeSheet);
            excelWriter.finish();
            byte[] content = os.toByteArray();
            InputStream is = new ByteArrayInputStream(content);
            fileUploadOutDto = fileUpoadUtil.uploadN(is, fileName, CONTNUE_RATE_REPROT_ADDR);
            log.info("结算中心--》手续费已付未付报表导出，查询明细上传oss结束，耗时:{}", System.currentTimeMillis() - time);
            updateLog(unionId, fileUploadOutDto, COMMISSION_PAIDANDUNPAID_REPROT);
        } catch (Exception e) {
            // 重置response
            Map<String, String> map = new HashMap<>(2);
            map.put("status", "failure");
            map.put("message", "上传文件失败" + e.getMessage());
            updateLog(unionId, fileUploadOutDto, COMMISSION_PAIDANDUNPAID_REPROT);
            log.error("error-message:{}", e);
        } finally {
            os.flush();
        }
    }

    @Async
    public void queryCommissionDetail(RylCountSettlementChannelLabEntity labEntity,List<RylCountCommissionResDTO> countDTOList,Map<String, String> riskMap
        ,RylCountCommissionDTO rylCountCommissionDTO) {

        List<String> monthList = getMonthBetweenDate(rylCountCommissionDTO.getStartMonth(),rylCountCommissionDTO.getEndMonth());
        if(!monthList.isEmpty()){
            AtomicReference<String> vitalFlag = new AtomicReference<>();
            vitalFlag.set("1");
            for(String month : monthList){
                log.info("手续费已付未付报表组装数据月份"+month);
                //弘康融汇间特殊处理（非投连产品）
                if("ronghui".equals(labEntity.getChannelCode())){
                    QueryWrapper<RylCountSettlementHkrhResultEntity> hkrhQueryWrapper = new QueryWrapper<>();
                    //险种
                    if(!rylCountCommissionDTO.getRiskCodeList().isEmpty()){
                        hkrhQueryWrapper.in("risk_code",rylCountCommissionDTO.getRiskCodeList());
                    }
                    //费用类型
                    if(!rylCountCommissionDTO.getSettlementTypeList().isEmpty()){
                        hkrhQueryWrapper.in("rate_type",rylCountCommissionDTO.getSettlementTypeList());
                    }
                    //业务类型
                    if (!rylCountCommissionDTO.getOpCodeList().isEmpty()) {
                        List<String> opCode = new ArrayList<>();
                        List<String> opSubCode = new ArrayList<>();
                        rylCountCommissionDTO.getOpCodeList().forEach(opcode -> {
                            String[] opCodeArray = opcode.split("_");
                            opCode.add(opCodeArray[0]);
                            opSubCode.add(opCodeArray[1]);
                            if("5".equals(opCodeArray[0])){
                                vitalFlag.set("5");
                            }
                        });
                        hkrhQueryWrapper.in("op_code",opCode);
                        hkrhQueryWrapper.in("op_sub_code",opSubCode);
                    }
                    hkrhQueryWrapper.apply(" DATE_FORMAT(business_date,'%Y-%m') = "+"'"+(month)+"'");
                    List<RylCountSettlementHkrhResultEntity> hkrhResultEntities = rylCountSettlementHkrhResultMapper.queryCommissionHKRH(hkrhQueryWrapper);
                    if(!hkrhResultEntities.isEmpty()){
                        Map<String, List<RylCountSettlementHkrhResultEntity>> groupedhkrhResultList = hkrhResultEntities.stream()
                                .collect(Collectors.groupingBy(a ->
                                        a.getRiskCode() + "|" + a.getOpCode() + "|" + a.getOpSubCode() + "|" + a.getRateType() + "|" + a.getRateSubType() + "|" + a.getCountPayStatus()));

                        groupedhkrhResultList.forEach((k, v)->{
                            String[] keys = StrUtil.split(k, "|");
                            RylCountCommissionResDTO resDTO = new RylCountCommissionResDTO();
                            resDTO.setDeptCodeName(labEntity.getLabDepName());
                            resDTO.setChannelName(labEntity.getChannelName());
                            resDTO.setRiskName(riskMap.get(keys[0]));
                            resDTO.setOpCode(keys[1]);
                            resDTO.setOpSubCode(keys[2]);
                            resDTO.setCountPayStatus("未付");
                            if("1".equals(keys[5])){
                                resDTO.setCountPayStatus("已付");
                            }
                            resDTO.setSettMonth(month);
                            resDTO.setRateType(keys[3]);
                            if(!"DEFAULT".equals(keys[4])){
                                resDTO.setRateSubType(keys[4]);
                            }
                            BigDecimal commission = v.stream().map(RylCountSettlementHkrhResultEntity::getCommissionCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                            resDTO.setCommissionCharge(commission);
                            countDTOList.add(resDTO);
                        });
                    }

                    //弘康融汇间投连产品
                    QueryWrapper<RylCountSettlementResult> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("channel_code","ronghui");
                    //险种
                    if(!rylCountCommissionDTO.getRiskCodeList().isEmpty()){
                        queryWrapper.in("main_risk_code",rylCountCommissionDTO.getRiskCodeList());
                    }
                    //费用类型
                    if(!rylCountCommissionDTO.getSettlementTypeList().isEmpty()){
                        queryWrapper.in("rate_type",rylCountCommissionDTO.getSettlementTypeList());
                    }
                    //业务类型
                    if (!rylCountCommissionDTO.getOpCodeList().isEmpty()) {
                        List<String> opCode = new ArrayList<>();
                        List<String> opSubCode = new ArrayList<>();
                        rylCountCommissionDTO.getOpCodeList().forEach(opcode -> {
                            String[] opCodeArray = opcode.split("_");
                            opCode.add(opCodeArray[0]);
                            opSubCode.add(opCodeArray[1]);
                            if("5".equals(opCodeArray[0])){
                                vitalFlag.set("5");
                            }
                        });
                        queryWrapper.in("op_code",opCode);
                        queryWrapper.in("op_sub_code",opSubCode);
                    }
                    queryWrapper.apply(" DATE_FORMAT(business_date,'%Y-%m') = "+"'"+(month)+"'");
                    List<RylCountCommissionResDTO> resultList = rylCountSettResultMapper.queryCommissionPaidAndUnpaidList(queryWrapper);
                    if(!resultList.isEmpty()){
                        log.info("手续费已付未付报表组装交易量数据条数"+resultList.size());
                        Map<String, List<RylCountCommissionResDTO>> groupedResultList = resultList.stream()
                                .collect(Collectors.groupingBy(a ->
                                        a.getRiskCode() + "|" + a.getOpCode() + "|" + a.getOpSubCode() + "|" + a.getRateType() + "|" + a.getRateSubType() + "|" + a.getCountPayStatus()));

                        groupedResultList.forEach((k, v)->{
                            String[] keys = StrUtil.split(k, "|");
                            RylCountCommissionResDTO resDTO = new RylCountCommissionResDTO();
                            resDTO.setDeptCodeName(labEntity.getLabDepName());
                            resDTO.setChannelName(labEntity.getChannelName());
                            resDTO.setRiskName(riskMap.get(keys[0]));
                            resDTO.setOpCode(keys[1]);
                            resDTO.setOpSubCode(keys[2]);
                            resDTO.setCountPayStatus(keys[5]);
                            resDTO.setSettMonth(month);
                            resDTO.setRateType(keys[3]);
                            if(!"DEFAULT".equals(keys[4])){
                                resDTO.setRateSubType(keys[4]);
                            }
                            BigDecimal commission = v.stream().map(RylCountCommissionResDTO::getCommissionCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                            resDTO.setCommissionCharge(commission);
                            countDTOList.add(resDTO);
                        });
                    }

                    //弘康融汇间存量
                    if(!StringUtils.isBlank(vitalFlag.get()) && vitalFlag.get().equals("5")){
                        QueryWrapper<RylCountSettResultVitalEntity> hkrhVitalEntityQueryWrapper = new QueryWrapper<>();
                        hkrhVitalEntityQueryWrapper.eq("channel_code","ronghui");
                        //险种
                        if(!rylCountCommissionDTO.getRiskCodeList().isEmpty()){
                            hkrhVitalEntityQueryWrapper.in("main_risk_code",rylCountCommissionDTO.getRiskCodeList());
                        }
                        hkrhVitalEntityQueryWrapper.apply(" DATE_FORMAT(vital_date,'%Y-%m') = "+"'"+(month)+"'");
                        List<RylCountSettResultVitalEntity> hkrhVitalEntities = rylCountSettResultVitalMapper.selectList(hkrhVitalEntityQueryWrapper);
                        if(!hkrhVitalEntities.isEmpty()){
                            Map<String, List<RylCountSettResultVitalEntity>> groupedhkrhVitalList = hkrhVitalEntities.stream()
                                    .collect(Collectors.groupingBy(a ->
                                            a.getMainRiskCode() + "|" + a.getCountPayStatus()));

                            groupedhkrhVitalList.forEach((k, v)->{
                                String[] keys = StrUtil.split(k, "|");
                                RylCountCommissionResDTO resDTO = new RylCountCommissionResDTO();
                                resDTO.setDeptCodeName(labEntity.getLabDepName());
                                resDTO.setChannelName(labEntity.getChannelName());
                                resDTO.setRiskName(riskMap.get(keys[0]));
                                resDTO.setOpCode("存量");
                                resDTO.setOpSubCode("存量");
                                resDTO.setCountPayStatus("未付");
                                if("1".equals(keys[1])){
                                    resDTO.setCountPayStatus("已付");
                                }
                                resDTO.setSettMonth(month);
                                resDTO.setRateType("基础费用");
                                resDTO.setRateSubType("基础费用");
                                BigDecimal commission = v.stream().map(RylCountSettResultVitalEntity::getCommissionCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                                resDTO.setCommissionCharge(commission);
                                countDTOList.add(resDTO);
                            });
                        }
                    }

                }else{
                    List<String> channelCodeList = new ArrayList<>();
                    if(!StringUtils.isBlank(labEntity.getSubChannelCode())){
                        channelCodeList = Arrays.asList(StringUtil.split(labEntity.getSubChannelCode(), "|"));
                    }else{
                        channelCodeList.add(labEntity.getChannelCode());
                    }
                    QueryWrapper<RylCountSettlementResult> queryWrapper = new QueryWrapper<>();
                    queryWrapper.in("channel_code",channelCodeList);
                    //险种
                    if(!rylCountCommissionDTO.getRiskCodeList().isEmpty()){
                        queryWrapper.in("main_risk_code",rylCountCommissionDTO.getRiskCodeList());
                    }
                    //费用类型
                    if(!rylCountCommissionDTO.getSettlementTypeList().isEmpty()){
                        queryWrapper.in("rate_type",rylCountCommissionDTO.getSettlementTypeList());
                    }
                    //业务类型
                    if (!rylCountCommissionDTO.getOpCodeList().isEmpty()) {
                        List<String> opCode = new ArrayList<>();
                        List<String> opSubCode = new ArrayList<>();
                        rylCountCommissionDTO.getOpCodeList().forEach(opcode -> {
                            String[] opCodeArray = opcode.split("_");
                            opCode.add(opCodeArray[0]);
                            opSubCode.add(opCodeArray[1]);
                            if("5".equals(opCodeArray[0])){
                                vitalFlag.set("5");
                            }
                        });
                        queryWrapper.in("op_code",opCode);
                        queryWrapper.in("op_sub_code",opSubCode);
                    }
                    queryWrapper.apply(" DATE_FORMAT(business_date,'%Y-%m') = "+"'"+(month)+"'");
                    List<RylCountCommissionResDTO> resultList = rylCountSettResultMapper.queryCommissionPaidAndUnpaidList(queryWrapper);
                    if(!resultList.isEmpty()){
                        log.info("手续费已付未付报表组装交易量数据条数"+resultList.size());
                        Map<String, List<RylCountCommissionResDTO>> groupedResultList = resultList.stream()
                                .collect(Collectors.groupingBy(a ->
                                        a.getRiskCode() + "|" + a.getOpCode() + "|" + a.getOpSubCode() + "|" + a.getRateType() + "|" + a.getRateSubType() + "|" + a.getCountPayStatus()));

                        groupedResultList.forEach((k, v)->{
                            String[] keys = StrUtil.split(k, "|");
                            RylCountCommissionResDTO resDTO = new RylCountCommissionResDTO();
                            resDTO.setDeptCodeName(labEntity.getLabDepName());
                            resDTO.setChannelName(labEntity.getChannelName());
                            resDTO.setRiskName(riskMap.get(keys[0]));
                            resDTO.setOpCode(keys[1]);
                            resDTO.setOpSubCode(keys[2]);
                            resDTO.setCountPayStatus(keys[5]);
                            resDTO.setSettMonth(month);
                            resDTO.setRateType(keys[3]);
                            if(!"DEFAULT".equals(keys[4])){
                                resDTO.setRateSubType(keys[4]);
                            }
                            BigDecimal commission = v.stream().map(RylCountCommissionResDTO::getCommissionCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                            resDTO.setCommissionCharge(commission);
                            countDTOList.add(resDTO);
                        });
                    }

                    //存量数据组装
                    if(!StringUtils.isBlank(vitalFlag.get()) && vitalFlag.get().equals("5")){
                        QueryWrapper<RylCountSettResultVitalEntity> vitalEntityQueryWrapper = new QueryWrapper<>();
                        vitalEntityQueryWrapper.in("channel_code",channelCodeList);
                        //险种
                        if(!rylCountCommissionDTO.getRiskCodeList().isEmpty()){
                            vitalEntityQueryWrapper.in("main_risk_code",rylCountCommissionDTO.getRiskCodeList());
                        }
                        vitalEntityQueryWrapper.apply(" DATE_FORMAT(vital_date,'%Y-%m') = "+"'"+(month)+"'");
                        List<RylCountSettResultVitalEntity> vitalEntities = rylCountSettResultVitalMapper.selectList(vitalEntityQueryWrapper);
                        if(!vitalEntities.isEmpty()){
                            log.info("手续费已付未付报表组装存量数据条数"+resultList.size());
                            Map<String, List<RylCountSettResultVitalEntity>> groupedVitalList = vitalEntities.stream()
                                    .collect(Collectors.groupingBy(a ->
                                            a.getMainRiskCode() + "|" + a.getCountPayStatus()));

                            groupedVitalList.forEach((k, v)->{
                                String[] keys = StrUtil.split(k, "|");
                                RylCountCommissionResDTO resDTO = new RylCountCommissionResDTO();
                                resDTO.setDeptCodeName(labEntity.getLabDepName());
                                resDTO.setChannelName(labEntity.getChannelName());
                                resDTO.setRiskName(riskMap.get(keys[0]));
                                resDTO.setOpCode("存量");
                                resDTO.setOpSubCode("存量");
                                resDTO.setCountPayStatus("未付");
                                if("1".equals(keys[1])){
                                    resDTO.setCountPayStatus("已付");
                                }
                                resDTO.setSettMonth(month);
                                resDTO.setRateType("基础费用");
                                resDTO.setRateSubType("基础费用");
                                BigDecimal commission = v.stream().map(RylCountSettResultVitalEntity::getCommissionCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                                resDTO.setCommissionCharge(commission);
                                countDTOList.add(resDTO);
                            });
                        }
                    }
                }
            }
        }
    }

    @Override
    public void commissionBusiAnalysisExport(RylCountStockSettDTO rylCountStockSettDTO) {
        Map<String, String> message = Maps.newHashMap();
        message.put("queryParam", JSONObject.toJSONString(rylCountStockSettDTO));
        FileUploadOutDto fileUploadOutDto = null;
        ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
        //条件查询导出
        singleThreadExecutor.execute(new Runnable() {
            @Override
            public void run() {
                commissionBusiAnalysisExportList(rylCountStockSettDTO, fileUploadOutDto, message);
            }
        });
        singleThreadExecutor.shutdown();
    }

    @SneakyThrows
    private void commissionBusiAnalysisExportList(RylCountStockSettDTO rylCountStockSettDTO, FileUploadOutDto fileUploadOutDto, Map<String, String> message) {
        long time = System.currentTimeMillis();
        Long threadId = Thread.currentThread().getId();
        String fileName = "手续费结算分析报表" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";;
        String threadName = Thread.currentThread().getName();
        message.put("threadId", String.valueOf(threadId));
        message.put("fileName", fileName);
        SendMessageEvent sendMessageEvent = new SendMessageEvent(message);
        sendMessageEvent.setDataType(COMMISSION_BUSI_ANALYSIS_REPROT);
        sendMessageEvent.setOperate(0);
        String unionId = UUID.randomUUID() + "-" + threadId;
        sendMessageEvent.setUnionId(unionId);
        applicationEventPublisher.publishEvent(sendMessageEvent);
        log.info("当前线程名称:{} 当前线程Id:{}", threadName, threadId);
        //子渠道处理
        List<String> subChannelCodeList = new ArrayList<>();
        QueryWrapper<RylCountSettlementChannelLabEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del",0);
        if(!rylCountStockSettDTO.getChannelCodeList().isEmpty()){
            queryWrapper.in("channel_code",rylCountStockSettDTO.getChannelCodeList());
        }
        List<RylCountSettlementChannelLabEntity> labEntityList = rylCountSettlementChannelLabMapper.selectList(queryWrapper);
        if(!labEntityList.isEmpty()){
            labEntityList.stream().forEach(x->{
                if(!StringUtils.isBlank(x.getSubChannelCode())){
                    subChannelCodeList.addAll(Arrays.asList(StringUtil.split(x.getSubChannelCode(), "|")));
                }else{
                    subChannelCodeList.add(x.getChannelCode());
                }
            });
        }
        //交易类型拆分
        List<String> opCodeList = new ArrayList<>();
        List<String> opSubCodeList = new ArrayList<>();
        if (!rylCountStockSettDTO.getOpCodeList().isEmpty()) {
            rylCountStockSettDTO.getOpCodeList().forEach(opcode -> {
                String[] opCodeArray = opcode.split("_");
                opCodeList.add(opCodeArray[0]);
                opSubCodeList.add(opCodeArray[1]);
            });
        }
        try{
            Page<RylCountChargeBusinessAnalysisEntity> page = new Page<>(1, pageMaxNum);

            int countCharge = rylCountChargeBusinessAnalysisMapper.queryCountChargeList(rylCountStockSettDTO.getBusinessDateStart(), rylCountStockSettDTO.getBusinessDateEnd(),
                    subChannelCodeList, rylCountStockSettDTO.getDeptCodeList(), rylCountStockSettDTO.getChannelTypeList(),
                    rylCountStockSettDTO.getRiskCodeList(), rylCountStockSettDTO.getSettlementTypeList(), rylCountStockSettDTO.getCountPayStatus(),
                    rylCountStockSettDTO.getSignDateStart(), rylCountStockSettDTO.getSignDateEnd(),
                    opCodeList, opSubCodeList,rylCountStockSettDTO.getRiskType(), page);
            if (countCharge <= 0) {
                // 无数据提示导出失败
                updateLog(unionId, fileUploadOutDto, COMMISSION_BUSI_ANALYSIS_REPROT);
                return;
            }

            int pageSize = 1000000;  // 每个Sheet最多100万条数据
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            SXSSFWorkbook workbook = new SXSSFWorkbook(1000);
            CellStyle cellStyle = cellContentStyle(workbook);

            Map<String, String> headerMap = getCommissionSettDetailListMap(rylCountStockSettDTO.getCustomizeList());

            int totalRows = countCharge;
            int sheetIndex = 0;
            int offset = 0;

            while (offset < totalRows) {
                // 创建新的Sheet
                Sheet sheet = workbook.createSheet("Sheet" + (sheetIndex + 1));
                Row headerRow = sheet.createRow(0);
                int colNum = 0;
                for (String columnName : headerMap.keySet()) {
                    Cell cell = headerRow.createCell(colNum++);
                    cell.setCellValue(headerMap.get(columnName));  // 设置标题为Map的值
                }

                int rowNum = 1;  // 从第二行开始写数据
                int limit = Math.min(pageSize, totalRows - offset);

                while (offset < totalRows && rowNum <= limit ) {
                    Page<RylCountChargeBusinessAnalysisEntity> chargePage = new Page<>(offset / pageMaxNum + 1, pageMaxNum);
                    // 获取当前分页的数据
                    List<Map<String, Object>> data = rylCountChargeBusinessAnalysisMapper.queryCountChargeDatailList(rylCountStockSettDTO.getBusinessDateStart(), rylCountStockSettDTO.getBusinessDateEnd(),
                            subChannelCodeList, rylCountStockSettDTO.getDeptCodeList(), rylCountStockSettDTO.getChannelTypeList(),
                            rylCountStockSettDTO.getRiskCodeList(), rylCountStockSettDTO.getSettlementTypeList(), rylCountStockSettDTO.getCountPayStatus(),
                            rylCountStockSettDTO.getSignDateStart(), rylCountStockSettDTO.getSignDateEnd(),
                            opCodeList, opSubCodeList,rylCountStockSettDTO.getRiskType(), chargePage);

                    // 写入每一页的数据
                    for (Map<String, Object> rowData : data) {
                        Row row = sheet.createRow(rowNum++);
                        colNum = 0;

                        // 将每一行的数据填充到表格中
                        for (String columnName : headerMap.keySet()) {
                            Cell cell = row.createCell(colNum++);
                            Object value = rowData.get(columnName);  // 根据列名取值
                            if (value != null) {
                                cell.setCellValue(value.toString());
                            }
                        }
                        offset++;
                    }
                }
                sheetIndex++;
            }

            workbook.write(byteArrayOutputStream);
            workbook.dispose();
            byteArrayOutputStream.close();
            ByteArrayInputStream swapStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            fileUploadOutDto = fileUpoadUtil.uploadN(swapStream, fileName, CONTNUE_RATE_REPROT_ADDR);
            log.info("手续费结算分析报表导出，查询明细上传oss结束，耗时:{}", System.currentTimeMillis() - time);
            updateLog(unionId, fileUploadOutDto, COMMISSION_BUSI_ANALYSIS_REPROT);
        } catch (Exception e) {
            // 重置response
            Map<String, String> map = new HashMap<>(2);
            map.put("status", "failure");
            map.put("message", "上传文件失败" + e.getMessage());
            updateLog(unionId, fileUploadOutDto, COMMISSION_BUSI_ANALYSIS_REPROT);
            log.error("error-message:{}", e);
        }
    }

    private Map<String, String> getCommissionSettDetailListMap(List<String> customizeList) {
        Map<String, String> aliasMap = new LinkedHashMap<>();
        customizeList.stream().forEach(x->{
            if(x.equals("signDate")){
                aliasMap.put("signDate", "保单签单日");
            }
            if(x.equals("signMonth")){
                aliasMap.put("signMonth", "签单月");
            }
            if(x.equals("businessDate")){
                aliasMap.put("businessDate", "业务生效日");
            }
            if(x.equals("businessMonth")){
                aliasMap.put("businessMonth", "业务生效月");
            }
            if(x.equals("channelType")){
                aliasMap.put("channelType", "渠道类型");
            }
            if(x.equals("channelCode")){
                aliasMap.put("channelCode", "渠道编码");
            }
            if(x.equals("channelName")){
                aliasMap.put("channelName", "渠道名称");
            }
            if(x.equals("depCode")){
                aliasMap.put("depCode", "管理部门");
            }
            if(x.equals("riskType")){
                aliasMap.put("riskType", "险种大类");
            }
            if(x.equals("riskCode")){
                aliasMap.put("mainRiskCode", "险种编码");
            }
            if(x.equals("riskName")){
                aliasMap.put("riskName", "险种名称");
            }
            if(x.equals("planCode")){
                aliasMap.put("planCode", "产品编码");
            }
            if(x.equals("planName")){
                aliasMap.put("planName", "产品名称");
            }
            if(x.equals("opCode")){
                aliasMap.put("opCode", "交易类型");
            }
            if(x.equals("opSubCode")){
                aliasMap.put("opSubCode", "交易子类型");
            }
            if(x.equals("rateType")){
                aliasMap.put("rateType", "费用类型");
            }
            if(x.equals("rateSubType")){
                aliasMap.put("rateSubType", "费用子类型");
            }
            if(x.equals("manageCom")){
                aliasMap.put("manageCom", "管理机构");
            }
            if(x.equals("countPayStatus")){
                aliasMap.put("countPayStatus", "支付状态");
            }
            if(x.equals("rateId")){
                aliasMap.put("rateValue", "费率");
            }
            if(x.equals("commissionCharge")){
                aliasMap.put("commissionCharge", "手续费金额");
            }
        });
        aliasMap.put("mioAmnt", "实收保费");
        return aliasMap;
    }

    @Override
    public void investmentLinkStockExport(RylCountStockSettDTO rylCountStockSettDTO) {
        Map<String, String> message = Maps.newHashMap();
        message.put("queryParam", JSONObject.toJSONString(rylCountStockSettDTO));
        FileUploadOutDto fileUploadOutDto = null;
        ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
        //条件查询导出
        singleThreadExecutor.execute(new Runnable() {
            @Override
            public void run() {
                investmentLinkStockExportList(rylCountStockSettDTO, fileUploadOutDto, message);
            }
        });
        singleThreadExecutor.shutdown();
    }

    @SneakyThrows
    private void investmentLinkStockExportList(RylCountStockSettDTO rylCountStockSettDTO, FileUploadOutDto fileUploadOutDto, Map<String, String> message) {
        long time = System.currentTimeMillis();
        Long threadId = Thread.currentThread().getId();
        String fileName = "投连存量明细报表-" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";;
        String threadName = Thread.currentThread().getName();
        message.put("threadId", String.valueOf(threadId));
        message.put("fileName", fileName);
        SendMessageEvent sendMessageEvent = new SendMessageEvent(message);
        sendMessageEvent.setDataType(INVESTMENT_LINK_STOCK_REPROT);
        sendMessageEvent.setOperate(0);
        String unionId = UUID.randomUUID() + "-" + threadId;
        sendMessageEvent.setUnionId(unionId);
        applicationEventPublisher.publishEvent(sendMessageEvent);
        log.info("当前线程名称:{} 当前线程Id:{}", threadName, threadId);
        if (riskMap.size() < 1) {
            riskMap = rylCountSettlementRateMapper.getRiskList("").stream()
                    .collect(Collectors.toMap(RiskDTO::getRiskCode, RiskDTO::getRiskName));
        }
        List<RylCountSettlementChannelCfgEntity> cfgEntityList = rylCountSettlementChannelCfgMapper.selectList(new QueryWrapper<RylCountSettlementChannelCfgEntity>().eq("is_del", 0));
        channelMap = cfgEntityList.stream().collect(Collectors.toMap(RylCountSettlementChannelCfgEntity::getChannelCode,
                RylCountSettlementChannelCfgEntity::getChannelName));
        QueryWrapper<RylCountSettlementRateEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("op_code", "5");
        queryWrapper.eq("is_del", "0");
        if(!rylCountStockSettDTO.getChannelCodeList().isEmpty()){
            List<String> channelCodeList = new ArrayList<>();
            //渠道子码处理
            QueryWrapper<RylCountSettlementChannelLabEntity> labQueryWrapper = new QueryWrapper<>();
            labQueryWrapper.eq("is_del",0);
            if(!rylCountStockSettDTO.getChannelCodeList().isEmpty()){
                labQueryWrapper.in("channel_code",rylCountStockSettDTO.getChannelCodeList());
            }
            List<RylCountSettlementChannelLabEntity> labEntityList = rylCountSettlementChannelLabMapper.selectList(labQueryWrapper);
            if(!labEntityList.isEmpty()){
                labEntityList.stream().forEach(x->{
                    if(!StringUtils.isBlank(x.getSubChannelCode())){
                        channelCodeList.addAll(Arrays.asList(StringUtil.split(x.getSubChannelCode(), "|")));
                    }else{
                        channelCodeList.add(x.getChannelCode());
                    }
                });
            }else{
                channelCodeList.addAll(rylCountStockSettDTO.getChannelCodeList());
            }
            queryWrapper.in("channel_code",channelCodeList);
        }
        if(!rylCountStockSettDTO.getRiskCodeList().isEmpty()){
            queryWrapper.in("main_risk_code",rylCountStockSettDTO.getRiskCodeList());
        }
        //账户价值无法提数
        queryWrapper.ne("rate_type_param", CommonConstant.STOCK_INSUVALUE);
        queryWrapper.ne("rate_type_param", CommonConstant.STOCK_NETVALUE);
        queryWrapper.isNotNull(" vital_start_date ");
        queryWrapper.isNotNull(" vital_end_date ");
        queryWrapper.ne("count_date_type","3");
        List<RylCountSettlementRateEntity> rateList = rylCountSettlementRateMapper.selectList(queryWrapper);
        List<RylCountStockDetailDTO> countDTOList = new ArrayList<>();
        if(!rateList.isEmpty()){
            for(RylCountSettlementRateEntity rateEntity : rateList){
                log.info("投连存量明细报表汇总数据明细"+rateEntity.getChannelCode()+"费率ID"+rateEntity.getRateId());
                queryChannelStockListDetail(rateEntity,countDTOList,riskMap,channelMap);
            }
        }
        if(countDTOList.isEmpty()){
            //无数据提示导出失败
            updateLog(unionId, fileUploadOutDto, INVESTMENT_LINK_STOCK_REPROT);
            return;
        }
        @Cleanup
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ExcelWriter excelWriter= EasyExcel.write(os).build();
        try{
            //表头样式策略
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            //设置表头居中对齐
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            //表头前景设置黑色
            headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setBold(true);
            headWriteFont.setFontName("宋体");
            headWriteFont.setFontHeightInPoints((short) 14);
            headWriteCellStyle.setWriteFont(headWriteFont);
            //内容样式策略策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            // 设置背景颜色白色
            contentWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
            // 设置垂直居中为居中对齐
            contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            // 设置左右对齐为靠左对齐
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            // 设置单元格上下左右边框为细边框
            contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
            contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
            contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
            contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
            //创建字体对象
            WriteFont contentWriteFont = new WriteFont();
            //内容字体大小
            contentWriteFont.setFontName("宋体");
            contentWriteFont.setFontHeightInPoints((short) 12);
            contentWriteCellStyle.setWriteFont(contentWriteFont);
            // 初始化表格样式
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            WriteSheet writeSheet = EasyExcel.writerSheet("投连存量明细报表明细").registerWriteHandler(horizontalCellStyleStrategy).head(RylCountStockDetailDTO.class).build();
            excelWriter.write(countDTOList, writeSheet);
            excelWriter.finish();
            byte[] content = os.toByteArray();
            InputStream is = new ByteArrayInputStream(content);
            fileUploadOutDto = fileUpoadUtil.uploadN(is, fileName, CONTNUE_RATE_REPROT_ADDR);
            log.info("结算中心-->投连存量明细报表导出，查询明细上传oss结束，耗时:{}", System.currentTimeMillis() - time);
            updateLog(unionId, fileUploadOutDto, INVESTMENT_LINK_STOCK_REPROT);
        } catch (Exception e) {
            // 重置response
            Map<String, String> map = new HashMap<>(2);
            map.put("status", "failure");
            map.put("message", "上传文件失败" + e.getMessage());
            updateLog(unionId, fileUploadOutDto, INVESTMENT_LINK_STOCK_REPROT);
            log.error("error-message:{}", e);
        } finally {
            os.flush();
        }
    }

    @Async
    public void queryChannelStockListDetail(RylCountSettlementRateEntity rateEntity,List<RylCountStockDetailDTO> countDTOList,Map<String, String> riskMap,Map<String, String> channelMap) {
        LocalDate calDate = LocalDate.now();
        String manageCom = null;
        String planCode = null;
        String sellType = null;
        if(!"DEFAULT".equals(rateEntity.getManageCom())){
            manageCom = rateEntity.getManageCom();
        }
        if(!"DEFAULT".equals(rateEntity.getPlanCode())){
            planCode = rateEntity.getPlanCode();
        }
        if(!"DEFAULT".equals(rateEntity.getSellType())){
            sellType = rateEntity.getSellType();
        }
        List<RylCountStockDetailDTO> stockDetailList = rylCountSettlementInitMapper.queryChannelStockListDetailList(rateEntity.getChannelCode(),rateEntity.getMainRiskCode(),
                manageCom,planCode,rateEntity.getVitalStartDate(),rateEntity.getVitalEndDate(),calDate,sellType);
        if(!stockDetailList.isEmpty()){
            stockDetailList.stream().forEach(x->{
                x.setChannelName(channelMap.get(x.getChannelCode()));
                x.setRiskName(riskMap.get(x.getRiskCode()));
                x.setRateId(rateEntity.getRateId());
                x.setVitalStartDate(rateEntity.getVitalStartDate());
                x.setVitalEndDate(rateEntity.getVitalEndDate());
                x.setContYearsStart(rateEntity.getContYearsStart());
                x.setContYearsEnd(rateEntity.getContYearsEnd());
            });
            countDTOList.addAll(stockDetailList);
        }
    }

}
