package com.huida.platform.settlement.service.reconciliationpay.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huida.platform.common.exception.BaseException;
import com.huida.platform.common.model.MyExcelSheet;
import com.huida.platform.common.msg.BaseResponse;
import com.huida.platform.common.util.ObjConverUtil;
import com.huida.platform.common.util.OssFileUtil;
import com.huida.platform.settlement.constant.*;
import com.huida.platform.settlement.dto.*;
import com.huida.platform.settlement.dto.protocol.FileUploadOutDto;
import com.huida.platform.settlement.dto.reconciliationpay.RylCountSettBillDiffVitalDto;
import com.huida.platform.settlement.dto.reconciliationpay.RylCountSettlementBillDiffPayDto;
import com.huida.platform.settlement.entity.approve.SysUserDto;
import com.huida.platform.settlement.entity.approve.WeChatApproveTemplateEntity;
import com.huida.platform.settlement.entity.count.RylCountSettlementBillMappingCfgEntity;
import com.huida.platform.settlement.entity.countpay.*;
import com.huida.platform.settlement.entity.settlement.RylCountSettlementChannelLabEntity;
import com.huida.platform.settlement.entity.settlement.RylDictCfgEntity;
import com.huida.platform.settlement.mapper.RylCountSettlementUserDepCfgMapper;
import com.huida.platform.settlement.mapper.approve.WeChatApproveTemplateMapper;
import com.huida.platform.settlement.mapper.reconciliationpay.*;
import com.huida.platform.settlement.mapper.settlement.RylCountSettlementChannelLabMapper;
import com.huida.platform.settlement.mapper.settlement.RylCountSettlementRateMapper;
import com.huida.platform.settlement.mapper.settlement.RylDictCfgMapper;
import com.huida.platform.settlement.service.approve.WeChatApproveService;
import com.huida.platform.settlement.service.reconciliationpay.CountPayService;
import com.huida.platform.settlement.service.reconciliationpay.SettBillService;
import com.huida.platform.settlement.util.CollectionCopyUtil;
import com.huida.platform.settlement.util.FileUpoadUtil;
import com.huida.platform.settlement.util.RhExcelUtil;
import com.huida.platform.settlement.util.SettlementUtil;
import com.huida.platform.settlement.vo.reconciliation.QueryCountData;
import com.huida.platform.settlement.vo.reconciliation.RylBillPayVo;
import com.huida.platform.settlement.vo.reconciliation.RylCountSettlementBillDiffExVO;
import com.huida.platform.settlement.vo.reconciliation.RylCountSettlementBillDiffVitalExVO;
import com.huida.platform.settlement.vo.reconciliation.RylVitalDiffVo;
import com.huida.platform.settlement.vo.reconciliationpay.RylCountSettBillDetailFileVo;
import com.huida.platform.settlement.vo.reconciliationpay.RylCountSettBillVitalFileVo;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 结算自动化流程(含沉淀量) 部分逻辑调整后新写方法
 * <AUTHOR>
 */
@Service
@Slf4j
public class CountPayServiceImpl implements CountPayService {
    private Map<String,String> riskMap=new HashMap<>();
    private Map<String,String> channelMap=new HashMap<>();
    @Autowired
    RylCountSettlementRateMapper rylCountSettlementRateMapper;
    @Autowired
    RylCountSettlementChannelCfgMapper rylCountSettlementChannelCfgMapper;
    @Autowired
    RylCountSettlementUploadPayMapper rylCountSettlementUploadPayMapper;
    @Autowired
    RylCountSettlementBillDetailPayMapper rylCountSettlementBillDetailPayMapper;
    @Autowired
    RylCountSettlementBillPayMapper rylCountSettlementBillPayMapper;
    @Autowired
    RylCountSettlementBillDiffPayMapper rylCountSettlementBillDiffPayMapper;
    @Autowired
    private RylCountSettBillDetailVitalMapper rylCountSettBillDetailVitalMapper;
    @Autowired
    private RylCountSettBillDiffVitalMapper rylCountSettBillDiffVitalMapper;

    @Autowired
    private FileUpoadUtil fileUpoadUtil;
    @Autowired
    private OssFileUtil ossFileUtil;

    public static final String BILL_BATCH_DIFF_ADDR = "count/batchDiff/";
    @Value("${imagesOSSUrlChange}")
    private String imagesOSSUrlChange;
    @Value("${imagesPortCatalogChange}")
    private String imagesPortCatalogChange;
    @Autowired
    private SettBillService settBillService;
    @Autowired
    private RylCountSettlementCountLogMapper rylCountSettlementCountLogMapper;
    @Autowired
    private RylDictCfgMapper rylDictCfgMapper;
    @Value("${settStatusList}")
    private String settStatusList;
    @Autowired
    private RestTemplate externalRestTemplate;
    @Value("${fk.hk.domain}")
    private String fkHkDns;
    @Value("${fk.rh.domain}")
    private String fkrhDns;
    @Value("${fk.queryHistoryOfApprovalUrl}")
    private String fkQueryWfHistoryUrl;
    @Autowired
    private RylCountSettlementUserDepCfgMapper rylCountSettlementUserDepCfgMapper;
    @Autowired
    private RylCountSettlementChannelLabMapper rylCountSettlementChannelLabMapper;
    @Autowired
    private RylCountSettlementOverallDiffMapper rylCountSettlementOverallDiffMapper;
    @Autowired
    private RylCountSettlementOverallDiffDetailMapper rylCountSettlementOverallDiffDetailMapper;

    @Autowired
    private WeChatApproveTemplateMapper weChatApproveTemplateMapper;

    @Value("${hrm.getUserUrl}")
    private String getUserUrl;

    @Autowired
    private WeChatApproveService weChatApproveService;
    @Autowired
    private RylCountSettlementBillMappingCfgMapper rylCountSettlementBillMappingCfgMapper;

    /**
     * 结算单明细导出excel 【含总的汇总】
     *
     * @param queryCountData
     * @param response
     * @throws IOException
     */
    @Override
    public BaseResponse countDetailToExcelNew(QueryCountData queryCountData, HttpServletResponse response) throws IOException {
        List<RylCountSettlementCountLogEntity> rylCountLogList = rylCountSettlementCountLogMapper.selectList(new QueryWrapper<RylCountSettlementCountLogEntity>().eq("count_no", queryCountData.getCountNo()));
        String fileKey = null;
        RylCountSettlementBillPayEntity billPay = rylCountSettlementBillPayMapper.queryByCountNo(queryCountData.getCountNo());
        if (billPay == null) {
            return BaseResponse.fail("-1", "参数有误，请核实。");
        }
        RylCountSettlementChannelCfgEntity channelCfgEntity = rylCountSettlementChannelCfgMapper.queryChannelNameByCode(billPay.getChannelCode());
        String filename = null;
        if (!rylCountLogList.isEmpty()) {
            for (RylCountSettlementCountLogEntity rylcount : rylCountLogList) {
                fileKey = rylcount.getUrl();
                if(!StringUtil.isBlank(rylcount.getFileName())){
                    filename = rylcount.getFileName();
                }
            }
        }
        if(filename == null){
            String countDatePeriod = SettlementUtil.dealSettDate(billPay.getCountStartTime(), billPay.getCountEndTime());
            filename = "("+queryCountData.getCountNo()+")"+channelCfgEntity.getProtocolBouncersName() + "-" + channelCfgEntity.getChannelName() + "_" + countDatePeriod + "结算单明细" + ".xlsx";
            if(channelCfgEntity.getIsDoubleSign() != null){
                if(1 == channelCfgEntity.getIsDoubleSign()){
                    List<String> manageList = rylCountSettlementUploadPayMapper.selectCountManages(queryCountData.getCountNo());
                    String settBouncers = null;
                    if (manageList != null){
                        //双签 8641  8631 是弘康，8611  8632 是融汇
                        if (manageList.size()==1){
                            if (manageList.containsAll(Arrays.asList("8611")) || manageList.containsAll(Arrays.asList("8632"))){
                                settBouncers="融汇";
                            }else if (manageList.containsAll(Arrays.asList("8631")) || manageList.containsAll(Arrays.asList("8641"))){
                                settBouncers="弘康";
                            }
                        }else if(manageList.size()==2){
                            if (manageList.containsAll(Arrays.asList("8611", "8632"))){
                                settBouncers="融汇";
                            }else if (manageList.containsAll(Arrays.asList("8631", "8641"))){
                                settBouncers="弘康";
                            }
                        }
                    }
                    if(settBouncers != null){
                        filename = "("+queryCountData.getCountNo()+")"+settBouncers + "-" + channelCfgEntity.getChannelName() + "_" + countDatePeriod + "结算单明细" + ".xlsx";
                    }
                }
            }
        }

        List<FileUploadOutDto> fileList = fileUpoadUtil.getFileList(fileKey, filename, FileUploadAddrConstant.PROTOCOL_ADDR);
        return BaseResponse.success(fileList);
    }

    /**
     * 手动生成结算单EXCEL
     * @param queryCountData
     * @throws IOException
     */
    @Override
    public BaseResponse countDetailToExcelTemporary(QueryCountData queryCountData) throws IOException {
        QueryWrapper<RylCountSettlementBillPayEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("count_no",queryCountData.getCountNo());
        RylCountSettlementBillPayEntity billPay = rylCountSettlementBillPayMapper.selectOne(queryWrapper);
        settBillService.handlerExportData(queryCountData.getCountNo(), "手动生成EXCEL",billPay.getCountStartTime(),billPay.getCountEndTime());
        return BaseResponse.success();
    }

    @Override
    public BaseResponse importAccountDataNew(MultipartFile file, String countNo) throws Exception {
        log.info("导入结算单号{}对账文件，开始对账。。。。", countNo);
        long time = System.currentTimeMillis();
        checkBaseExcel(file);
        //（一）、删除 上一次的对账记录
        //二次对账，只对失败的；不再对全部（就不能清除上次对账成功的记录）
        rylCountSettlementBillDiffPayMapper.delete(new QueryWrapper<RylCountSettlementBillDiffPayEntity>().eq("count_no", countNo));
        //现对账改为按保单级对账，需将上次对账失败的保单的所有交易的 渠道方费用都清除
        List<String> notSuccPolicys = rylCountSettlementBillDetailPayMapper.selectNotSuccPolicys(countNo);
        rylCountSettlementBillDetailPayMapper.updateChannelAmntDefault(countNo, notSuccPolicys);
        rylCountSettBillDetailVitalMapper.delAccountVitalDetail(countNo);
        rylCountSettBillDiffVitalMapper.deleteVitalDiff(countNo);
        //(二)、解析对账文件里，渠道上传的佣金，落库
        //1、解析exce里 保单级数据（基础手续费+继续率奖励）
        List<RylCountSettlementBillDetailPayEntity> channelChargeList = readExcelPolicyCharge(file, countNo);
        if (channelChargeList != null && channelChargeList.size() > 0) {
            QueryWrapper<RylCountSettlementBillDetailPayEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("count_no", countNo);
            List<String> countPolicyList = rylCountSettlementBillDetailPayMapper.getPolicyByCountNo(queryWrapper);
            if (!countPolicyList.containsAll(channelChargeList.stream().map(RylCountSettlementBillDetailPayEntity::getPolicyNo).collect(Collectors.toList()))) {
                throw new BaseException("-1", "对账文件里含有非结算单内的保单");
            }
            List<RylCountSettlementBillDetailPayEntity> allDetailList = rylCountSettlementBillDetailPayMapper.selectList(queryWrapper);
            //定义一个 对账成功的保单 detail 集合
            List<RylCountSettlementBillDetailPayEntity> allSuccPolicyList = new ArrayList<>();
            //汇总保单级别手续费,将已对账成功的剔除
            List<RylCountSettlementBillDetailPayEntity> billDetailPayEntityList = rylCountSettlementBillDetailPayMapper.queryBillDetailPayList(countNo);
            if (!billDetailPayEntityList.isEmpty()) {
                for (RylCountSettlementBillDetailPayEntity billDetailPay : billDetailPayEntityList) {
                    //手续费一致情况下视为对账成功进行剔除
                    if (new BigDecimal(billDetailPay.getCommissionChargeTotal()).intValue() != 0
                            && new BigDecimal(billDetailPay.getCommissionChargeTotalCount()).intValue() != 0
                            && new BigDecimal(billDetailPay.getCommissionChargeTotalChannel()).intValue() != 0) {

                        if (billDetailPay.getCommissionChargeTotal().equals(billDetailPay.getCommissionChargeTotalCount())
                                && billDetailPay.getCommissionChargeTotal().equals(billDetailPay.getCommissionChargeTotalChannel())
                                && billDetailPay.getCommissionChargeTotalCount().equals(billDetailPay.getCommissionChargeTotalChannel())) {
                            allSuccPolicyList.add(billDetailPay);
                        }
                    }
                }
                if (!allSuccPolicyList.isEmpty()) {
                    List<String> sussPolicyList = allSuccPolicyList.stream().map(RylCountSettlementBillDetailPayEntity::getPolicyNo).collect(Collectors.toList());
                    for (String a : sussPolicyList) {
                        billDetailPayEntityList = billDetailPayEntityList.stream().filter(s -> !s.getPolicyNo().equals(a)).collect(Collectors.toList());
                    }
                }
                //对账成功的保单，从对账文件里剔除；也可支持全量对账
                List<RylCountSettlementBillDetailPayEntity> fileBakList = new ArrayList<>();
                CollectionUtils.addAll(fileBakList, new Object[channelChargeList.size()]);
                Collections.copy(fileBakList, channelChargeList);
                for (RylCountSettlementBillDetailPayEntity fileBillDeatilPay : fileBakList) {
                    // 系统对账失败的保单交易集合billDetailPayEntityList  VS  渠道方对账文件的保单集合fileBakList
                    List<RylCountSettlementBillDetailPayEntity> payEntityList = billDetailPayEntityList.stream().filter(x-> x.getPolicyNo().equals(fileBillDeatilPay.getPolicyNo())).collect(Collectors.toList());
                    if(!payEntityList.isEmpty()) {
                        BigDecimal sysSumCommission = new BigDecimal(payEntityList.get(0).getCommissionChargeTotalCount());
                        List<RylCountSettlementBillDetailPayEntity> sysDetailList = allDetailList.stream().filter(a -> a.getPolicyNo().equals(fileBillDeatilPay.getPolicyNo())).collect(Collectors.toList());
                        if (sysSumCommission.compareTo(new BigDecimal(fileBillDeatilPay.getCommissionChargeTotalChannel())) == 0) {
                            //汇总保单级别手续费相等时， 用系统的交易级费用更新渠道方费用
                            sysDetailList.forEach(p -> {
                                if (p.getCommissionChargeTotalAfter() != null) {
                                    p.setCommissionChargeTotalChannel(p.getCommissionChargeTotalAfter());
                                } else {
                                    p.setCommissionChargeTotalChannel(p.getCommissionChargeTotalCount());
                                }
                                rylCountSettlementBillDetailPayMapper.updateCommissionChangeAmnt(p);
                            });
                        }
                    }
                }
            }
        }
        //3、解析excel里 非保单级数据--存量
        List<RylCountSettBillDetailVitalEntity> channelStockList = readExcelStockCharge(file, countNo);
        if (channelStockList != null && channelStockList.size() > 0) {
            channelStockList.forEach(channelStock -> {
                channelStock.setCountNo(countNo);
                //对账文件里的数据，有则更新，无则插入
                //同一天内存在多笔存量明细需进行汇总对账
                List<RylCountSettBillDetailVitalEntity> oldStockDetail = rylCountSettBillDetailVitalMapper.selectStockDetail(channelStock);
                if(oldStockDetail.isEmpty()){
                    throw new BaseException("-1", "统计日" + channelStock.getVitalDate() + "不在结算单内");
                }
                BigDecimal commissionCharge = new BigDecimal(0);
                List<RylCountSettBillDetailVitalEntity> afterStockDetail = oldStockDetail.stream().filter(x-> x.getCommissionChargeAfter() != null).collect(Collectors.toList());
                if(afterStockDetail.isEmpty()){
                    commissionCharge = oldStockDetail.stream().map(RylCountSettBillDetailVitalEntity::getCommissionCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                }else{
                    List<RylCountSettBillDetailVitalEntity> stockDetail = oldStockDetail.stream().filter(x-> x.getCommissionChargeAfter() == null).collect(Collectors.toList());
                    if(!stockDetail.isEmpty()){
                        BigDecimal stockCommission = stockDetail.stream().map(RylCountSettBillDetailVitalEntity::getCommissionCharge).reduce(BigDecimal.ZERO, BigDecimal::add);
                        commissionCharge = commissionCharge.add(stockCommission);
                    }
                    BigDecimal afterStockCommission = afterStockDetail.stream().map(RylCountSettBillDetailVitalEntity::getCommissionChargeAfter).reduce(BigDecimal.ZERO, BigDecimal::add);
                    commissionCharge = commissionCharge.add(afterStockCommission);
                }
                if(channelStock.getCommissionChargeChannel().compareTo(commissionCharge) == 0){
                    oldStockDetail.stream().forEach(x->{
                        if(x.getCommissionChargeAfter() != null){
                            x.setCommissionChargeChannel(x.getCommissionChargeAfter());
                        }else{
                            x.setCommissionChargeChannel(x.getCommissionCharge());
                        }
                        rylCountSettBillDetailVitalMapper.updateById(x);
                    });
                }else{
                    oldStockDetail.stream().forEach(x->{
                        x.setCommissionChargeChannel(channelStock.getCommissionChargeChannel());
                        rylCountSettBillDetailVitalMapper.updateById(x);
                    });
                }
            });
        }

        RylCountSettlementBillPayEntity rylCountSettlementBillPay = rylCountSettlementBillPayMapper.queryByCountNo(countNo);
        /* 整体调差 功能已不允许操作
        //有过结算单层级调大记录的数据用 总数对总数
        log.info("结算单{}对账，billpay查询结束,耗时:{}", countNo, System.currentTimeMillis() - time);
        RylCountSettlementBillDiffPayDto rylCountSettlementBillDiffPayDto = rylCountSettlementBillDetailPayMapper.queryCountPay(countNo);
        BigDecimal viatlAfterCharge = rylCountSettBillDetailVitalMapper.queryAfterSumCharge(countNo);

        log.info("结算单{}对账，billdetailpay查询结束,耗时:{}", countNo, System.currentTimeMillis() - time);
        //总数对 总数修改后或修改前可以对上 渠道导入的数据的总数  对账成功
        if (StringUtil.isNotBlank(rylCountSettlementBillPay.getCommissionChargeTotalAfter())) {
            BigDecimal detailSumCharge = new BigDecimal(0);
            BigDecimal newCommission = rylCountSettlementBillDetailPayMapper.queryDetailPayList(countNo);
            if(newCommission != null){
                detailSumCharge = detailSumCharge.add(newCommission);
            }
            if (viatlAfterCharge != null) {
                detailSumCharge = detailSumCharge.add(viatlAfterCharge);
            }
            BigDecimal billSumCharge = new BigDecimal(rylCountSettlementBillPay.getCommissionChargeTotalAfter());
            //批量调差时,只需要总手续金额一致即为对账成功
            if (billSumCharge.compareTo(detailSumCharge) == 0) {
                //更新 对账状态为成功，差异个数 为0
                rylCountSettlementBillPayMapper.updateReconciliationStatus(countNo, "1", "0");
                log.info("结算单号{}调差总金额、佣金，对账成功结束,耗时:{}", countNo, System.currentTimeMillis() - time);
                QueryWrapper<RylCountSettlementBillDetailPayEntity> billpayEntity = new QueryWrapper<>();
                billpayEntity.eq("count_no",countNo);
                billpayEntity.apply("commission_charge_total_after is not null");
                List<Map<String, Object>> billPayList = rylCountSettlementBillDetailPayMapper.queryCountAfterList(billpayEntity);
                QueryWrapper<RylCountSettBillDetailVitalEntity> vitalPayEntity = new QueryWrapper<>();
                vitalPayEntity.eq("count_no",countNo);
                vitalPayEntity.apply("commission_charge_after is not null");
                List<RylCountSettBillDetailVitalEntity> vitalPayList = rylCountSettBillDetailVitalMapper.selectList(vitalPayEntity);
                if(!billPayList.isEmpty() || !vitalPayList.isEmpty()){
                    rylCountSettlementCountLogMapper.deleteCountLog(countNo);
                    settBillService.handlerExportData(countNo, "对账成功存在调差数据重新生成结算单EXCEL",rylCountSettlementBillPay.getCountStartTime(),rylCountSettlementBillPay.getCountEndTime());
                }
                return BaseResponse.success();
            }
        }*/

        // 差异详情表
        List<RylCountSettlementBillDiffPayDto> rylCountSettlementBillDiffDtos = rylCountSettlementBillDetailPayMapper.queryCountDiff(countNo);
        List<RylCountSettBillDiffVitalEntity> rylCountSettBillDiffVitals = rylCountSettBillDetailVitalMapper.getVitalDiff(countNo);

        log.info("结算单{}对账，保单差异详情查询结束,耗时:{}", countNo, System.currentTimeMillis() - time);
        List<RylCountSettlementBillDiffPayEntity> rylCountSettlementBillDiffPayEntities =
                CollectionCopyUtil.copyList(rylCountSettlementBillDiffDtos, RylCountSettlementBillDiffPayEntity.class);
        rylCountSettlementBillDiffPayEntities.forEach(x -> {
            if (StringUtil.isNotBlank(x.getAmntDiffType()) || StringUtil.isNotBlank(x.getCcDiffType()) || StringUtil.isNotBlank(x.getVtialDiffType())) {
                rylCountSettlementBillDiffPayMapper.insert(x);
            }
        });
        rylCountSettBillDiffVitals.forEach(vitalDiff -> {
            if (vitalDiff.getCommissionChargeAfter() == null) {
                if (vitalDiff.getCcDiffType() != null) {
                    rylCountSettBillDiffVitalMapper.insert(vitalDiff);
                }
            } else {
                if (vitalDiff.getCcDiffTypeAfter() != null) {
                    vitalDiff.setCommissionChargeDiff(vitalDiff.getCommissionChargeAfterDiff());
                    vitalDiff.setCcDiffType(vitalDiff.getCcDiffTypeAfter());
                    rylCountSettBillDiffVitalMapper.insert(vitalDiff);
                }
            }
        });
        log.info("结算单{}对账，保单差异逐单入库,耗时:{}", countNo, System.currentTimeMillis() - time);
        int diffNum = rylCountSettlementBillDiffPayMapper.queryBillDiffNum(countNo);
        int vitalDiffNum = rylCountSettBillDiffVitalMapper.queryVitalDiffNum(countNo);
        //1：对账成功 2：对账失败
        if (diffNum > 0 || vitalDiffNum > 0) {
            rylCountSettlementBillPayMapper.updateReconciliationStatus(countNo, "2", diffNum + vitalDiffNum + "");
        } else {
            rylCountSettlementBillPayMapper.updateReconciliationStatus(countNo, "1", "0");
            QueryWrapper<RylCountSettlementBillDetailPayEntity> billpayEntity = new QueryWrapper<>();
            billpayEntity.eq("count_no",countNo);
            billpayEntity.apply("commission_charge_total_after is not null");
            List<Map<String, Object>> billPayList = rylCountSettlementBillDetailPayMapper.queryCountAfterList(billpayEntity);
            QueryWrapper<RylCountSettBillDetailVitalEntity> vitalPayEntity = new QueryWrapper<>();
            vitalPayEntity.eq("count_no",countNo);
            vitalPayEntity.apply("commission_charge_after is not null");
            List<RylCountSettBillDetailVitalEntity> vitalPayList = rylCountSettBillDetailVitalMapper.selectList(vitalPayEntity);
            if(!billPayList.isEmpty() || !vitalPayList.isEmpty()){
                rylCountSettlementCountLogMapper.deleteCountLog(countNo);
                settBillService.handlerExportData(countNo, "对账成功存在调差数据重新生成结算单EXCEL",rylCountSettlementBillPay.getCountStartTime(),rylCountSettlementBillPay.getCountEndTime());
            }
        }
        log.info("导入结算单号{}对账文件，对账结束,耗时:{}", countNo, System.currentTimeMillis() - time);
        return BaseResponse.success();

    }

    @Override
    public BaseResponse queryVitalBillDiff(QueryCountData queryCountData) {
        log.info("结算单管理-继续率补偿金/存量差异详情查询{}",queryCountData);
        QueryWrapper<RylCountSettlementBillPayEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("count_no",queryCountData.getCountNo());
        RylCountSettlementBillPayEntity payEntity = rylCountSettlementBillPayMapper.selectOne(queryWrapper);
        if(payEntity == null){
            return BaseResponse.fail("-1","未查询到结算单");
        }
        if(riskMap.size()<1){
            riskMap=rylCountSettlementRateMapper.getRiskList("").stream()
                    .collect(Collectors.toMap(RiskDTO::getRiskCode,RiskDTO::getRiskName));
        }
        if(channelMap.size()<1) {
            LambdaQueryWrapper<RylCountSettlementChannelCfgEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(RylCountSettlementChannelCfgEntity::getIsDel, "0");
            List<RylCountSettlementChannelCfgEntity> channelList =
                    rylCountSettlementChannelCfgMapper.selectList(lambdaQueryWrapper);
            List<RylCountSettlementChannelCfgEntity> channelRepeatList = channelList.stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(RylCountSettlementChannelCfgEntity::getChannelCode))), ArrayList::new)
            );
            channelMap = channelRepeatList.stream().collect(Collectors.toMap(RylCountSettlementChannelCfgEntity::getChannelCode,
                    RylCountSettlementChannelCfgEntity::getChannelName));
        }
        Page<RylCountSettBillDiffVitalDto> page =new Page<>(queryCountData.getPageNum(), queryCountData.getPageSize());
        LambdaQueryWrapper<RylCountSettBillDiffVitalEntity> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StringUtil.isNotBlank(queryCountData.getCountNo()),
                RylCountSettBillDiffVitalEntity::getCountNo,queryCountData.getCountNo());
        if("0".equals(queryCountData.getVtialFlag())){
            // 0-查继续率补偿金 差异
            lambdaQueryWrapper.eq(RylCountSettBillDiffVitalEntity::getChargeType,1);
        }else if("1".equals(queryCountData.getVtialFlag())){
            // 1-查沉淀量 差异
            lambdaQueryWrapper.eq(RylCountSettBillDiffVitalEntity::getChargeType,2);
        }
        IPage<RylCountSettBillDiffVitalDto> rylCountSettlementBillDiffDtoIPage = rylCountSettBillDiffVitalMapper.selectDiffByCountNo(page,lambdaQueryWrapper);
        if(!rylCountSettlementBillDiffDtoIPage.getRecords().isEmpty()){
            QueryWrapper<RylDictCfgEntity> dictCfgEntityQueryWrapper = new QueryWrapper<>();
            dictCfgEntityQueryWrapper.eq("code_type","plan_code");
            dictCfgEntityQueryWrapper.eq("is_del","0");
            List<RylDictCfgEntity> cfgEntityList = rylDictCfgMapper.selectList(dictCfgEntityQueryWrapper);
            Map<String,String> planCodeMap = cfgEntityList.stream().collect(Collectors.toMap(RylDictCfgEntity::getCode,RylDictCfgEntity::getName));
            rylCountSettlementBillDiffDtoIPage.getRecords().stream().forEach(x->{
                Long vitalId = x.getVitalId();
                RylCountSettBillDetailVitalEntity detailVitalEntity = rylCountSettBillDetailVitalMapper.selectById(vitalId);
                if(detailVitalEntity!=null){
                    x.setCommissionChargeAfter(detailVitalEntity.getCommissionChargeAfter());
                    if(!StringUtil.isBlank(detailVitalEntity.getRemark())){
                        x.setRemark(detailVitalEntity.getRemark());
                    }
                }
                x.setMainRiskName(riskMap.get(x.getMainRiskCode()));
                x.setChannelName(channelMap.get(x.getChannelCode()));
                if(!StringUtil.isBlank(x.getManageCom()) && !"DEFAULT".equals(x.getManageCom())){
                    x.setManageCom(CommonConstant.MANAGECOM_CONSTANTS.get("managecom",x.getManageCom().substring(0,4)));
                }
                if(!StringUtil.isBlank(x.getPlanCode()) && !"DEFAULT".equals(x.getPlanCode())){
                    if(!org.apache.commons.lang3.StringUtils.isBlank(planCodeMap.get(x.getPlanCode()))){
                        x.setPlanCode(planCodeMap.get(x.getPlanCode()));
                    }
                }
            });
        }
        log.info("结算单管理-继续率补偿金/存量差异详情查询返回：{}",JSON.toJSONString(rylCountSettlementBillDiffDtoIPage));
        return BaseResponse.success(rylCountSettlementBillDiffDtoIPage);
    }

    @Override
    public BaseResponse updateVitalDiff(RylVitalDiffVo rylVitalDiffVo) {
        try {
            if(StringUtil.isBlank(rylVitalDiffVo.getCountNo())){
                return BaseResponse.fail("-1","结算单号不能为空");
            }
            QueryWrapper<RylCountSettlementBillPayEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("count_no",rylVitalDiffVo.getCountNo());
            RylCountSettlementBillPayEntity payEntity = rylCountSettlementBillPayMapper.selectOne(queryWrapper);
            if(payEntity == null){
                return BaseResponse.fail("-1","未查询到结算单");
            }
            Long vitalId = rylVitalDiffVo.getVitalId();
            RylCountSettBillDetailVitalEntity vitalDiffOld = rylCountSettBillDetailVitalMapper.selectById(vitalId);
            if(vitalDiffOld==null){
                return BaseResponse.fail("-1","入参ID有误");
            }
            vitalDiffOld.setCommissionChargeAfter(new BigDecimal(rylVitalDiffVo.getCommissionChargeTotal()));
            vitalDiffOld.setRemark(rylVitalDiffVo.getRemark());
            rylCountSettBillDetailVitalMapper.updateById(vitalDiffOld);
            BigDecimal commissionCharge = new BigDecimal(0);
            //非保单级的总手续费
            BigDecimal vitalCharge = rylCountSettBillDetailVitalMapper.queryAfterSumCharge(vitalDiffOld.getCountNo());
            //保单级总手续费
            BigDecimal detailCharge = rylCountSettlementBillDetailPayMapper.queryDetailPayList(vitalDiffOld.getCountNo());
            if(vitalCharge != null){
                commissionCharge = commissionCharge.add(vitalCharge);
            }
            if(detailCharge != null){
                commissionCharge = commissionCharge.add(detailCharge);
            }
            RylCountSettlementBillPayEntity rylCountSettlementBillPayEntity = new RylCountSettlementBillPayEntity();
            rylCountSettlementBillPayEntity.setCommissionChargeTotalAfter(commissionCharge.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            if(StringUtil.isNotBlank(rylVitalDiffVo.getRemark())){
                rylCountSettlementBillPayEntity.setRemark(rylVitalDiffVo.getRemark());
            }
            QueryWrapper<RylCountSettlementChannelLabEntity> labEntityQueryWrapper = new QueryWrapper<>();
            labEntityQueryWrapper.eq("channel_code",payEntity.getChannelCode());
            labEntityQueryWrapper.eq("is_ryl_settlement",0);
            RylCountSettlementChannelLabEntity labEntity = rylCountSettlementChannelLabMapper.selectOne(labEntityQueryWrapper);
            if(labEntity != null){
                if(!StringUtil.isBlank(labEntity.getMerchantType()) && "3".equals(labEntity.getMerchantType())){
                    rylCountSettlementBillPayEntity.setCommissionChargeTotalAfter(commissionCharge.setScale(0, BigDecimal.ROUND_HALF_UP).toString());
                }
                if(labEntity.getIsRylSettlement() != null && "0".equals(labEntity.getIsRylSettlement().toString())){
                    rylCountSettlementBillPayEntity.setReconciliationStatus("3");
                }
            }
            rylCountSettlementBillPayMapper.update(rylCountSettlementBillPayEntity,new UpdateWrapper<RylCountSettlementBillPayEntity>().eq("count_no",vitalDiffOld.getCountNo()));
            return BaseResponse.success();
        }catch (Exception e){
            log.error("存量调差异常，原因:{}",e);
            return BaseResponse.fail("-1","存量调差异常");
        }
    }

    /**
     * 差异调整详情 查询
     * @param rylBillPayVo
     * @return
     */
    @Override
    public BaseResponse queryVitalDiffDetail(RylBillPayVo rylBillPayVo) {
        QueryWrapper<RylCountSettlementBillPayEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("count_no",rylBillPayVo.getCountNo());
        RylCountSettlementBillPayEntity payEntity = rylCountSettlementBillPayMapper.selectOne(queryWrapper);
        if(payEntity == null){
            return BaseResponse.fail("-1","未查询到结算单");
        }
        Page<RylCountSettBillDetailVitalEntity> page = new Page<>(rylBillPayVo.getPageNum(),rylBillPayVo.getPageSize());
        QueryWrapper<RylCountSettBillDetailVitalEntity> billDetailVitalEntityQueryWrapper=new QueryWrapper<>();
        billDetailVitalEntityQueryWrapper.eq("count_no",rylBillPayVo.getCountNo());
        billDetailVitalEntityQueryWrapper.eq("charge_type","0".equals(rylBillPayVo.getVtialFlag())?1:2);
        billDetailVitalEntityQueryWrapper.isNotNull("commission_charge_after");
        IPage<RylCountSettBillDetailVitalEntity> vitalDiffLogs =rylCountSettBillDetailVitalMapper.getVitalDiffDetail(page, billDetailVitalEntityQueryWrapper);
        if(!vitalDiffLogs.getRecords().isEmpty()){
            if(riskMap.size()<1){
                riskMap=rylCountSettlementRateMapper.getRiskList("").stream()
                        .collect(Collectors.toMap(RiskDTO::getRiskCode,RiskDTO::getRiskName));
            }
            if(channelMap.size()<1) {
                LambdaQueryWrapper<RylCountSettlementChannelCfgEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(RylCountSettlementChannelCfgEntity::getIsDel, "0");
                List<RylCountSettlementChannelCfgEntity> channelList =
                        rylCountSettlementChannelCfgMapper.selectList(lambdaQueryWrapper);
                List<RylCountSettlementChannelCfgEntity> channelRepeatList = channelList.stream().collect(
                        Collectors.collectingAndThen(
                                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(RylCountSettlementChannelCfgEntity::getChannelCode))), ArrayList::new)
                );
                channelMap = channelRepeatList.stream().collect(Collectors.toMap(RylCountSettlementChannelCfgEntity::getChannelCode,
                        RylCountSettlementChannelCfgEntity::getChannelName));
            }
            QueryWrapper<RylDictCfgEntity> cfgQueryWrapper = new QueryWrapper<>();
            cfgQueryWrapper.eq("code_type","plan_code");
            cfgQueryWrapper.eq("is_del","0");
            List<RylDictCfgEntity> cfgEntityList = rylDictCfgMapper.selectList(cfgQueryWrapper);
            Map<String,String> planCodeMap = cfgEntityList.stream().collect(Collectors.toMap(RylDictCfgEntity::getCode,RylDictCfgEntity::getName));
            vitalDiffLogs.getRecords().forEach(x -> {
                x.setMainRiskName(riskMap.get(x.getMainRiskCode()));
                x.setChannelName(channelMap.get(x.getChannelCode()));
                if(null == x.getCommissionChargeAfter()){
                    x.setCommissionChargeAfter(x.getCommissionCharge());
                }
                if(!StringUtil.isBlank(x.getManageCom()) && !"DEFAULT".equals(x.getManageCom())){
                    x.setManageCom(CommonConstant.MANAGECOM_CONSTANTS.get("managecom",x.getManageCom().substring(0,4)));
                }
                if(!StringUtil.isBlank(x.getPlanCode()) && !"DEFAULT".equals(x.getPlanCode())){
                    if(!StringUtils.isBlank(planCodeMap.get(x.getPlanCode()))){
                        x.setPlanCode(planCodeMap.get(x.getPlanCode()));
                    }
                }
            });
        }
        return BaseResponse.success(vitalDiffLogs);
    }

    @Override
    public BaseResponse importBatchDiffData(MultipartFile file, String countNo) throws Exception {
        try{
            QueryWrapper<RylCountSettlementBillPayEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("count_no",countNo);
            List<RylCountSettlementBillPayEntity> billPayList = rylCountSettlementBillPayMapper.selectList(queryWrapper);
            if(billPayList.isEmpty()){
                return BaseResponse.fail("-1", "结算单号不存在!");
            }
            //校验文件格式
            if (!StrUtil.contains(file.getOriginalFilename(),".xls") || !StrUtil.contains(file.getOriginalFilename(),".xlsx")){
                return BaseResponse.fail("-1", "请导入正确Excel文件");
            }
            //读取excel
            ExcelReader readerAll = ExcelUtil.getReader(file.getInputStream());
            //获取sheet页名字
            List<String> sheetNames = readerAll.getSheetNames();
            String fhResult = sheetNames.get(0);
            ExcelReader dSettleReaderRate = ExcelUtil.getReader(file.getInputStream(), fhResult);
            List<RylCountSettlementBillDiffExVO> fhResultDTOList = new ArrayList<>();
            if(dSettleReaderRate.getRowCount() > 0){
                fhResultDTOList = dSettleReaderRate.read(0, 1, dSettleReaderRate.getRowCount(), RylCountSettlementBillDiffExVO.class);
            }
            String vitalResult = sheetNames.get(1);
            ExcelReader vitalSettleReaderRate = ExcelUtil.getReader(file.getInputStream(), vitalResult);
            List<RylCountSettlementBillDiffVitalExVO> vitalResultDTOList = new ArrayList<>();
            if(vitalSettleReaderRate.getRowCount() > 0){
                vitalResultDTOList = vitalSettleReaderRate.read(0, 1, vitalSettleReaderRate.getRowCount(), RylCountSettlementBillDiffVitalExVO.class);
            }
            if(fhResultDTOList.isEmpty() && vitalResultDTOList.isEmpty()){
                return BaseResponse.fail("-1", "Excel空文件请检查");
            }
            QueryWrapper<RylCountSettlementChannelLabEntity> labEntityQueryWrapper = new QueryWrapper<>();
            labEntityQueryWrapper.eq("channel_code",billPayList.get(0).getChannelCode());
            RylCountSettlementChannelLabEntity labEntity = rylCountSettlementChannelLabMapper.selectOne(labEntityQueryWrapper);
            if(labEntity == null){
                return BaseResponse.fail("-1", "获取结算单渠道信息异常");
            }

            if(!fhResultDTOList.isEmpty()){
                List<String> detailIdList = fhResultDTOList.stream().filter(x-> x.getCommissionChargeTotalAfter() != null).map(RylCountSettlementBillDiffExVO::getDetailId).collect(Collectors.toList());
                if(detailIdList.isEmpty()){
                    return BaseResponse.fail("-1", "调差文件中不存在待处理调差数据!");
                }
                List<RylCountSettlementBillDiffExVO> errorlist = fhResultDTOList.stream().filter(x-> x.getCommissionChargeTotalAfter() != null && StringUtil.isBlank(x.getRemark())).collect(Collectors.toList());
                if(!errorlist.isEmpty()){
                    return BaseResponse.fail("-1", "调差文件涉及调差手续费金额数据请描述调差备注!");
                }
                QueryWrapper<RylCountSettlementBillDetailPayEntity> uqueryWrapper = new QueryWrapper<>();
                uqueryWrapper.eq("count_no",countNo);
                uqueryWrapper.in("id",detailIdList);
                List<RylCountSettlementBillDetailPayEntity> detailPayEntityList = rylCountSettlementBillDetailPayMapper.selectList(uqueryWrapper);
                if(detailPayEntityList.size() < detailIdList.size()){
                    return BaseResponse.fail("-1", "请检查EXCEL调差文件包含非结算单内保单!");
                }
                //处理对账明细
                fhResultDTOList.stream().filter(x-> x.getCommissionChargeTotalAfter() != null).forEach(x->{
                    rylCountSettlementBillDetailPayMapper.updateBatchDetailPay(x.getDetailId(),x.getCommissionChargeTotalAfter(),x.getRemark());
                });
            }

            if(!vitalResultDTOList.isEmpty()){
                List<String> vitalIdList = vitalResultDTOList.stream().filter(x-> x.getCommissionChargeTotalAfter() != null).map(RylCountSettlementBillDiffVitalExVO::getVitalId).collect(Collectors.toList());
                if(vitalIdList.isEmpty()){
                    return BaseResponse.fail("-1", "调差文件中不存在待处理调差数据!");
                }
                List<RylCountSettlementBillDiffVitalExVO> errorlist = vitalResultDTOList.stream().filter(x-> x.getCommissionChargeTotalAfter() != null && StringUtil.isBlank(x.getRemark())).collect(Collectors.toList());
                if(!errorlist.isEmpty()){
                    return BaseResponse.fail("-1", "调差文件涉及调差手续费金额数据请描述调差备注!");
                }
                QueryWrapper<RylCountSettBillDetailVitalEntity> vitalQueryWrapper = new QueryWrapper<>();
                vitalQueryWrapper.eq("count_no",countNo);
                vitalQueryWrapper.in("vital_id",vitalIdList);
                List<RylCountSettBillDetailVitalEntity> detailVitalEntityList = rylCountSettBillDetailVitalMapper.selectList(vitalQueryWrapper);
                if(detailVitalEntityList.size() < vitalIdList.size()){
                    return BaseResponse.fail("-1", "请检查EXCEL调差文件包含非结算单内存量统计日!");
                }
                //处理对账明细
                vitalResultDTOList.stream().filter(x-> x.getCommissionChargeTotalAfter() != null).forEach(x->{
                    rylCountSettBillDetailVitalMapper.updateBatchDetailVital(x.getVitalId(),x.getCommissionChargeTotalAfter(),x.getRemark());
                });
            }
            BigDecimal commissionCharge = new BigDecimal(0);
            BigDecimal newCommission = rylCountSettlementBillDetailPayMapper.queryDetailPayList(countNo);
            //非保单级的总手续费
            BigDecimal vitalCharge = rylCountSettBillDetailVitalMapper.queryAfterSumCharge(countNo);
            if(newCommission != null){
                commissionCharge = commissionCharge.add(newCommission);
            }
            if(vitalCharge != null){
                commissionCharge = commissionCharge.add(vitalCharge);
            }
            RylCountSettlementBillPayEntity rylCountSettlementBillPayEntity = new RylCountSettlementBillPayEntity();
            rylCountSettlementBillPayEntity.setCommissionChargeTotalAfter(commissionCharge.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            rylCountSettlementBillPayEntity.setRemark("调差文件批量调差");
            rylCountSettlementBillPayEntity.setMioAmntTotalAfter(billPayList.get(0).getMioAmntTotal());
            if(!StringUtil.isBlank(labEntity.getMerchantType()) && "3".equals(labEntity.getMerchantType())){
                rylCountSettlementBillPayEntity.setCommissionChargeTotalAfter(commissionCharge.setScale(0, BigDecimal.ROUND_HALF_UP).toString());
            }
            if(labEntity.getIsRylSettlement() != null && "0".equals(labEntity.getIsRylSettlement().toString())){
                rylCountSettlementBillPayEntity.setReconciliationStatus("3");
            }
            rylCountSettlementBillPayEntity.setAdjustmentType("1");
            rylCountSettlementBillPayMapper.update(rylCountSettlementBillPayEntity,new UpdateWrapper<RylCountSettlementBillPayEntity>().eq("count_no",countNo));
            return BaseResponse.success();
        }catch (Exception e){
            log.error("导入批量对账文件异常，原因：", e);
            return BaseResponse.fail("-1", "导入批量对账文件异常，请稍后再试");
        }
    }

    @Override
    public String billBatchDiffDownLoad(String countNo) {
        //上传 文件路径/fileKey
        String targetfilePath = BILL_BATCH_DIFF_ADDR.concat(countNo);
        String fileName = "批量调差明细文件" + countNo;
        String urlPrivate = ossFileUtil.getDownLoadUrlPrivate(5, targetfilePath, fileName);
        //转化为测试环境URL
        return fileUpoadUtil.replaceDomainAndPort(imagesOSSUrlChange, imagesPortCatalogChange, urlPrivate);
    }

    //解析对账文件里 基础手续费、继续率奖励数据
    private List<RylCountSettlementBillDetailPayEntity> readExcelPolicyCharge(MultipartFile file, String countNo) {
        try {
            ExcelReader readerAll = ExcelUtil.getReader(file.getInputStream());
            List<String> sheetNames = readerAll.getSheetNames();
            ExcelReader baseChargeSheet = ExcelUtil.getReader(file.getInputStream(), sheetNames.get(0));
            List<RylCountSettBillDetailFileVo> policysChargeList = baseChargeSheet.read(0, 1, baseChargeSheet.getRowCount(),
                    RylCountSettBillDetailFileVo.class);
            List<RylCountSettBillDetailFileVo> resultList = new ArrayList<>();
            if(!policysChargeList.isEmpty()){
                Map<String, Integer> result =
                        policysChargeList.stream().collect(Collectors.groupingBy(RylCountSettBillDetailFileVo::getPolicyNo, Collectors.summingInt(x -> Integer.valueOf(new BigDecimal(x.getCommissionChargeTotalChannel()).intValue()))));
                result.entrySet().stream().forEach(x->{
                    BigDecimal commission = new BigDecimal(0);
                    List<RylCountSettBillDetailFileVo> prusltList = policysChargeList.stream().filter(a -> x.getKey().equals(a.getPolicyNo())).collect(Collectors.toList());
                    for(RylCountSettBillDetailFileVo vo : prusltList){
                        commission = commission.add(new BigDecimal(vo.getCommissionChargeTotalChannel()));
                    }
                    RylCountSettBillDetailFileVo resultVo = new RylCountSettBillDetailFileVo();
                    resultVo.setPolicyNo(x.getKey());
                    resultVo.setCommissionChargeTotalChannel(commission.toString());
                    resultList.add(resultVo);
                });
            }


            //继续率奖励 合入基础手续费里
//            ExcelReader forwardChargeSheet = ExcelUtil.getReader(file.getInputStream(),  sheetNames.get(1));
//            resultList.addAll(forwardChargeSheet.read(0, 1, forwardChargeSheet.getRowCount(),
//                    RylCountSettBillDetailFileVo.class)) ;
//            List<String> baseChargeErrors = checkBaseCharge(resultList);
//            if(baseChargeErrors!=null && baseChargeErrors.size()>0){
//                StringBuffer stringBuffer=new StringBuffer();
//                for(int i=0;i<baseChargeErrors.size();i++){
//                    stringBuffer.append(baseChargeErrors.get(i));
//                    stringBuffer.append(",");
//                }
//                throw new BaseException("-9999",stringBuffer.toString());
//            }
            //循环将对账文件里的数据，更新到明细表
            if (!resultList.isEmpty()) {
                resultList.stream().forEach(fileData -> {
                    fileData.setCountNo(countNo);
                    String opCode = null;
                    if(!StringUtil.isBlank(fileData.getOpCode())){
                        opCode = CommonType.OPCODE.get(fileData.getOpCode());
                    }
                    if(opCode != null){
                        fileData.setOpCode(StringUtil.substring(opCode, 0, opCode.indexOf('_')));
                        fileData.setOpSubCode(StringUtil.substring(opCode, opCode.indexOf('_') + 1, opCode.length()));
                    }
                    if (StringUtils.isNotBlank(fileData.getRateType())) {
                        fileData.setRateType(CommonType.RATE_TYPE.get(fileData.getRateType()));
                        fileData.setRateTypeParam(null);
                    } else if (StringUtils.isNotBlank(CommonType.RATE_TYPE_PARAM.get(fileData.getRateTypeParam()))) {
                        fileData.setRateType("3");
                        fileData.setRateTypeParam(CommonType.RATE_TYPE_PARAM.get(fileData.getRateTypeParam()));
                    }
                });
                log.info(JSONObject.toJSONString(resultList));
                List<RylCountSettlementBillDetailPayEntity> channelChargeList = ObjConverUtil.copy(resultList, RylCountSettlementBillDetailPayEntity.class);
                return channelChargeList;
            }
            return null;
        }catch (Exception e){
            log.error("解析导入的对账文件-保单级手续费异常，原因：{}",e);
            throw new BaseException("-1", "解析导入的对账文件保单级手续费异常");
        }
    }
    //解析对账文件里的 继续率补偿金数据
    private List<RylCountSettBillDetailVitalEntity> readExcelVitalCharge(MultipartFile file,String countNo){
        try {
            ExcelReader readerAll = ExcelUtil.getReader(file.getInputStream());
            List<String> sheetNames = readerAll.getSheetNames();
            //继续率补偿金
            ExcelReader vitalSheet = ExcelUtil.getReader(file.getInputStream(),  sheetNames.get(2));
            List<RylCountSettBillVitalFileVo> vitalAccountList = vitalSheet.read(0,1,vitalSheet.getRowCount(),RylCountSettBillVitalFileVo.class);
            if(!vitalAccountList.isEmpty()){
                rylCountSettBillDetailVitalMapper.updateChannelAmntDefault(countNo,1);
                vitalAccountList.forEach(vitalAccount->{
                    vitalAccount.setChannelCode(StringUtil.substring(vitalAccount.getChannelName(),0,vitalAccount.getChannelName().indexOf('_')));
                    vitalAccount.setMainRiskCode(StringUtil.substring(vitalAccount.getMainRiskName(),0,vitalAccount.getMainRiskName().indexOf('-')));
                    vitalAccount.setPayIntv(CommonType.PAY_INTV_EXCEL.get(vitalAccount.getPayIntv()));
                    vitalAccount.setCommissionChargeAfter(vitalAccount.getCommissionChargeChannel());
                });
                List<RylCountSettBillDetailVitalEntity> channelVitalList = ObjConverUtil.copy(vitalAccountList,RylCountSettBillDetailVitalEntity.class);
                return channelVitalList;
            }
            return null;
        }catch (Exception e){
            log.error("解析导入的对账文件里继续率补偿金异常，原因：{}",e);
            throw new BaseException("-1", "解析导入的对账文件的继续率补偿金数据异常");
        }
    }
    //解析对账文件里的 存量数据
    private List<RylCountSettBillDetailVitalEntity> readExcelStockCharge(MultipartFile file,String countNo){
        try {
            ExcelReader readerAll = ExcelUtil.getReader(file.getInputStream());
            List<String> sheetNames = readerAll.getSheetNames();
            //沉淀量
            ExcelReader stockSheet = ExcelUtil.getReader(file.getInputStream(),  sheetNames.get(3));
            List<RylCountSettBillVitalFileVo> stockAccountList = stockSheet.read(0,1,stockSheet.getRowCount(),RylCountSettBillVitalFileVo.class);
            if(!stockAccountList.isEmpty()){
                rylCountSettBillDetailVitalMapper.updateChannelAmntDefault(countNo,2);
                stockAccountList.forEach(stockAccount->{
                    stockAccount.setChannelCode(StringUtil.substring(stockAccount.getChannelName(),0,stockAccount.getChannelName().indexOf('_')));
                    stockAccount.setMainRiskCode(StringUtil.substring(stockAccount.getMainRiskName(), 0, stockAccount.getMainRiskName().indexOf('_')));
                    stockAccount.setCommissionChargeAfter(stockAccount.getCommissionChargeChannel());
                    stockAccount.setManageCom(StringUtil.substring(stockAccount.getManageCom(), 0, stockAccount.getManageCom().indexOf('_')));
                    String planCode = StringUtils.substringAfter(stockAccount.getPlanCode(), "-");
                    stockAccount.setPlanCode(planCode);
                });
                List<RylCountSettBillDetailVitalEntity> channelStockList = ObjConverUtil.copy(stockAccountList,RylCountSettBillDetailVitalEntity.class);
                return channelStockList;
            }
            return null;
        }catch (Exception e){
            log.error("解析导入的对账文件里存量异常，原因：{}",e);
            throw new BaseException("-1", "解析导入的对账文件的存量数据异常");
        }
    }
    //对账文件 基本校验
    private void checkBaseExcel(MultipartFile file){
        if (!StrUtil.contains(file.getOriginalFilename(),".xls")){
            throw new BaseException("20002", "请导入正确Excel文件");
        }
        try {
            ExcelReader readerAll = ExcelUtil.getReader(file.getInputStream());
            List<String> sheetNames = readerAll.getSheetNames();
            if(sheetNames.size()<4){
                throw new BaseException("-20001","请导入正确的模板,模板应包含4个sheet页");
            }
        }catch (Exception e){
            log.error("解析导入的对账文件异常，原因：{}",e);
            throw new BaseException("-1", "解析导入的对账文件异常");
        }
    }
    private  List<String> checkBaseCharge(List<RylCountSettBillDetailFileVo> baseChargeFileData){
        if(baseChargeFileData.isEmpty() || baseChargeFileData.size()==0){
            return null;
        }
        List<String> responses=new ArrayList<>();
        int row=0;
        int rowmsg=0;
        for(int i=0;i<baseChargeFileData.size();i++){
            rowmsg=responses.size()+1;
            RylCountSettBillDetailFileVo item =baseChargeFileData.get(i);
            row=i+2;
            if(!StringUtil.isNotBlank(item.getPolicyNo())||!item.getPolicyNo().matches("[0-9]+")){
                responses.add(rowmsg+".本次上传文件中第"+row+"行第1列数据不符合规则");
                continue;
            }
            if(!StringUtil.isBlank(item.getRateTypeParam())){
                if(!StringUtil.isNotBlank(item.getOpCode())||!StringUtil.isNotBlank(CommonType.OPCODE.get(item.getOpCode()))){
                    responses.add(rowmsg+".本次上传文件中第"+row+"行第2列数据不符合规则");
                    continue;
                }
                if(StringUtil.isBlank(item.getMioAmntTotalChannel())){item.setMioAmntTotalChannel("0");}
                if(!StringUtil.isNotBlank(item.getMioAmntTotalChannel())||!isBigDecimal(item.getMioAmntTotalChannel())){
                    responses.add(rowmsg+".本次上传文件中第"+row+"行保费列数据不符合规则");
                    continue;
                }
            }
            if (StringUtil.isBlank(item.getCommissionChargeTotalChannel())) {
                item.setCommissionChargeTotalChannel("0");
            }
            if (!StringUtil.isNotBlank(item.getCommissionChargeTotalChannel()) || !isBigDecimal(item.getCommissionChargeTotalChannel())) {
                responses.add(rowmsg + ".本次上传文件中第" + row + "行手续费列数据不符合规则");
                continue;
            }
        }
        return responses;
    }

    //解析批量调差文件里 基础手续费、继续率奖励数据
    private List<RylCountSettlementBillDetailPayEntity> readDiffExcelCharge(MultipartFile file, String countNo,List<String> flag) {
        try {
            ExcelReader readerAll = ExcelUtil.getReader(file.getInputStream());
            List<String> sheetNames = readerAll.getSheetNames();
            ExcelReader baseChargeSheet = ExcelUtil.getReader(file.getInputStream(), sheetNames.get(0));
            List<RylCountSettBillDetailFileVo> policysChargeList = baseChargeSheet.read(0, 1, baseChargeSheet.getRowCount(),
                    RylCountSettBillDetailFileVo.class);
            //继续率奖励 合入基础手续费里
            ExcelReader forwardChargeSheet = ExcelUtil.getReader(file.getInputStream(), sheetNames.get(1));
            policysChargeList.addAll(forwardChargeSheet.read(0, 1, baseChargeSheet.getRowCount(),
                    RylCountSettBillDetailFileVo.class));
            //循环将对账文件里的数据，更新到明细表
            if (!policysChargeList.isEmpty()) {
                policysChargeList.stream().forEach(fileData -> {
                    if(StringUtils.isEmpty(fileData.getRemark())){
                        flag.add("1");
                        return;
                    }
                    fileData.setCountNo(countNo);
                    String opCode = CommonType.OPCODE.get(fileData.getOpCode());
                    fileData.setOpCode(StringUtil.substring(opCode, 0, opCode.indexOf('_')));
                    fileData.setOpSubCode(StringUtil.substring(opCode, opCode.indexOf('_') + 1, opCode.length()));
                    if (StringUtils.isNotBlank(fileData.getRateType())) {
                        fileData.setRateType(CommonType.RATE_TYPE.get(fileData.getRateType()));
                        fileData.setRateTypeParam(null);
                    } else if (StringUtils.isNotBlank(CommonType.RATE_TYPE_PARAM.get(fileData.getRateTypeParam()))) {
                        fileData.setRateType("3");
                        fileData.setRateTypeParam(CommonType.RATE_TYPE_PARAM.get(fileData.getRateTypeParam()));
                    }
                });
                log.info(JSONObject.toJSONString(policysChargeList));
                List<RylCountSettlementBillDetailPayEntity> channelChargeList = ObjConverUtil.copy(policysChargeList, RylCountSettlementBillDetailPayEntity.class);
                return channelChargeList;
            }
            return null;
        } catch (Exception e) {
            log.error("解析导入的批量调差文件-保单级手续费异常，原因：{}", e);
            throw new BaseException("-1", "解析导入的批量调差文件保单级手续费异常");
        }
    }
    public static boolean isBigDecimal(String str){
        if(str==null || str.trim().length() == 0){
            return false;
        }
        char[] chars = str.toCharArray();
        int sz = chars.length;
        int i = (chars[0] == '-') ? 1 : 0;
        if(i == sz) {
            return false;
        }

        if(chars[i] == '.') {
            return false;//除了负号，第一位不能为'小数点'
        }

        boolean radixPoint = false;
        for(; i < sz; i++){
            if(chars[i] == '.'){
                if(radixPoint) {
                    return false;
                }
                radixPoint = true;
            }else if(!(chars[i] >= '0' && chars[i] <= '9')){
                return false;
            }
        }
        return true;
    }

    @Override
    public BaseResponse reconciliation(String countNo) throws Exception {
        log.info("对账，以我司数据为准，开始对账。。。。", countNo);
        long time = System.currentTimeMillis();

        RylCountSettlementBillPayEntity rylCountSettlementBillPay = rylCountSettlementBillPayMapper.queryByCountNo(countNo);
        if(StringUtil.isBlank(rylCountSettlementBillPay.getCountNo())){
            return BaseResponse.fail("-1","结算单号有误");
        }
        rylCountSettlementBillPayMapper.updateReconciliationStatus(countNo, "1", "0");
        log.info("对账，以我司数据为准，对账成功结束,耗时:{}", countNo, System.currentTimeMillis() - time);
        return BaseResponse.success();
    }

    @Override
    public BaseResponse getSettStatusList() {
        return BaseResponse.success(rylDictCfgMapper.getSettStatusList());
    }

    @Override
    public BaseResponse getStatusList(QuerySettStatusDto querySettStatusDto) {
        QueryWrapper<RylDictCfgEntity> queryWrapper = new QueryWrapper<>();
        if(querySettStatusDto.getNodeList().isEmpty()){
            List<String> codeTypeList = Arrays.asList(StringUtil.split(settStatusList, "|"));
            queryWrapper.in("code_type",codeTypeList);
        }else{
            queryWrapper.in("code_type",querySettStatusDto.getNodeList());
        }
        queryWrapper.eq("is_del",0);
        List<RylDictCfgEntity> dictCfgList = rylDictCfgMapper.selectList(queryWrapper);
        return BaseResponse.success(dictCfgList);
    }

    @Override
    public BaseResponse queryNode(QuerySettStatusDto querySettStatusDto) {
        if(querySettStatusDto == null){
            return BaseResponse.fail("-1","请检查结算单入参");
        }else {
            if (StringUtils.isEmpty(querySettStatusDto.getUserId())) {
                return BaseResponse.fail("-1","userId不能为空");
            }
        }
        List<String> depCodeList = rylCountSettlementUserDepCfgMapper.queryDepCode(querySettStatusDto.getUserId());
        if (!depCodeList.isEmpty()){
            querySettStatusDto.setDepCodeList(depCodeList);
        }
        IPage<RylCountSettlementBillPayEntity> page = new Page<>(querySettStatusDto.getPageNum(), querySettStatusDto.getPageSize());
        IPage<QuerySettStatusResDto> billpayList = rylCountSettlementBillPayMapper.querySettNodeListPage(page,querySettStatusDto);
        if(billpayList.getRecords().size() >= 1){
            billpayList.getRecords().stream().forEach(x->{
                String countDatePeriod = SettlementUtil.dealSettDate(x.getCountStartTime(), x.getCountEndTime());
                String amount = StringUtils.isBlank(x.getCommissionChargeTotalAfter()) ? x.getCommissionChargeTotal() : x.getCommissionChargeTotalAfter();
                x.setCommissionChargeTotal(amount);
                String channelName = x.getChannelName();
                String desc = (StringUtils.isBlank(channelName) ? "渠道【" + x.getChannelCode() + "】" : channelName) + countDatePeriod + "结算（单号：" + x.getCountNo() + "）手续费汇总金额：" + amount + "元";
                if (StringUtils.isNotBlank(x.getCommissionChargeTotalAfter())) {
                    String commisionChargeTotal = x.getCommissionChargeTotal();
                    String commisionChargeToTalAfter = x.getCommissionChargeTotalAfter();
                    desc = desc + "，调差金额：" + new BigDecimal(commisionChargeTotal).subtract(new BigDecimal(commisionChargeToTalAfter)) + "元";
                }
                x.setDescription(desc);
                if(!StringUtil.isBlank(x.getReconciliationStatus())){
                    x.setNode("对账中");
                    if("0".equals(x.getReconciliationStatus())){
                        x.setStatus("未对账");
                        if (StringUtils.isNotEmpty(x.getOverallDiffExamineStatus())){
                            x.setNode("整体调差审批中");
                            if("0".equals(x.getOverallDiffExamineStatus())){
                                x.setStatus("整体调差待提交审核");
                            }else if("1".equals(x.getOverallDiffExamineStatus())){
                                x.setStatus("整体调差未审批");
                            }else if("2".equals(x.getOverallDiffExamineStatus())){
                                x.setStatus("整体调差审批成功");
                            }else if("3".equals(x.getOverallDiffExamineStatus())){
                                x.setStatus("整体调差审批失败");
                            }
                        }
                    }
                    if("1".equals(x.getReconciliationStatus())){
                        x.setStatus("对账成功");
                    }
                    if("2".equals(x.getReconciliationStatus())){
                        x.setStatus("对账失败");
                    }
                }
                if(!StringUtil.isBlank(x.getExamineStatus()) && !"0".equals(x.getExamineStatus())){
                    if(!StringUtil.isBlank(x.getMerchantType()) && "1".equals(x.getMerchantType())){
                        if ("0".equals(x.getExamineSubStatus())
                                || "1".equals(x.getExamineSubStatus())
                                || "2".equals(x.getExamineSubStatus())
                                || "4".equals(x.getExamineSubStatus())){
                            x.setNode("审核中");
                            if("1".equals(x.getExamineStatus())){
                                x.setStatus("未审核");
                            }
                            if("2".equals(x.getExamineStatus())){
                                x.setStatus("审核成功");
                            }
                            if("3".equals(x.getExamineStatus())){
                                x.setStatus("审核失败");
                            }
                        }else if ("3".equals(x.getExamineSubStatus())
                                || "5".equals(x.getExamineSubStatus())
                                || "6".equals(x.getExamineSubStatus())){
                            x.setNode("开票节点");
                            if("3".equals(x.getExamineSubStatus())){
                                x.setStatus("开票中");
                            }
                            if("5".equals(x.getExamineSubStatus())){
                                x.setNode("收款节点");
                                if ("0".equals(x.getBillPayStatus()) || "2".equals(x.getBillPayStatus())){
                                    x.setStatus("开票完成未收款");
                                }else {
                                    x.setStatus("开票完成已收款");
                                }
                            }
                            if("6".equals(x.getExamineSubStatus())){
                                x.setStatus("开票打回");
                            }
                        }
                    }else {
                        x.setNode("审核中");
                        if("1".equals(x.getExamineStatus())){
                            x.setStatus("未审核");
                        }
                        if("2".equals(x.getExamineStatus())){
                            x.setStatus("审核成功");
                        }
                        if("3".equals(x.getExamineStatus())){
                            x.setStatus("审核失败");
                        }
                    }
                }
                if(!StringUtil.isBlank(x.getAsynFkFlag())){
                    if("2".equals(x.getAsynFkFlag())){
                        x.setNode("推送中");
                        x.setStatus("推送失败");
                    }else if ("1".equals(x.getAsynFkFlag())){
                        x.setNode("费控环节");
                        x.setStatus(FkBillStatusEnum.queryByStatus(x.getFkBillStatus()).getState());
                    }
                }
            });
        }
        return BaseResponse.success(billpayList);
    }

    @Override
    public String queryHistoryOfApproval(String fkBillNo) {
        log.info("查询费控结算单节点审批进度请求参数：{}",fkBillNo);
        // 1. 先用原始参数查询数据库
        //根据账单上的费控账号 拆分 费控调用系统
        RylCountSettlementBillPayEntity rylCountSettlementBillPayEntity = rylCountSettlementBillPayMapper.queryCountNo(fkBillNo);
        RylCountSettlementBillMappingCfgEntity billMappingCfgEntity = rylCountSettlementBillMappingCfgMapper.selectById(rylCountSettlementBillPayEntity.getUserFkUuid());
        String fkUnitName = billMappingCfgEntity.getFkUnitName();
        String fkWfHistoryUrl = fkUnitName.startsWith(CommonConstant.FKUSERDEPT_HK) ? fkHkDns + fkQueryWfHistoryUrl : fkrhDns + fkQueryWfHistoryUrl;
        // 2. 在调用外部接口时 添加引号（外部接口需要JSON字符串格式）
        String jsonParam = "\"" + fkBillNo + "\"";
        ResponseEntity<String> response =
                externalRestTemplate.exchange(fkWfHistoryUrl, HttpMethod.POST, new HttpEntity<>(jsonParam), new ParameterizedTypeReference<String>() {
                });
        if (response.getStatusCodeValue()==200){
            String body = response.getBody();
            log.info("查询费控结算单节点审批进度返回参数：{}",body);
            return body;
        }
        return null;
    }

    @Override
    public void downSettNodeList(QuerySettStatusDto querySettStatusDto, HttpServletResponse response) {
        try {
            if(querySettStatusDto == null){
                log.info("请检查结算单入参");
                return;
            }else {
                if (StringUtils.isEmpty(querySettStatusDto.getUserId())) {
                    log.info("请检查结算单入参");
                    return;
                }
            }
            List<String> depCodeList = rylCountSettlementUserDepCfgMapper.queryDepCode(querySettStatusDto.getUserId());
            if (!depCodeList.isEmpty()){
                querySettStatusDto.setDepCodeList(depCodeList);
            }
            List<Map<String, Object>> exportdata = new ArrayList<>();
            List<QuerySettStatusResDto> billpayList = rylCountSettlementBillPayMapper.downSettNodeList(querySettStatusDto);
            if(!billpayList.isEmpty()) {
                billpayList.stream().forEach(x -> {
                    String countDatePeriod = SettlementUtil.dealSettDate(x.getCountStartTime(), x.getCountEndTime());
                    String amount = StringUtils.isBlank(x.getCommissionChargeTotalAfter()) ? x.getCommissionChargeTotal() : x.getCommissionChargeTotalAfter();
                    x.setCommissionChargeTotal(amount);
                    String channelName = x.getChannelName();
                    String desc = (StringUtils.isBlank(channelName) ? "渠道【" + x.getChannelCode() + "】" : channelName) + countDatePeriod + "结算（单号：" + x.getCountNo() + "）手续费汇总金额：" + amount + "元";
                    if (StringUtils.isNotBlank(x.getCommissionChargeTotalAfter())) {
                        String commisionChargeTotal = x.getCommissionChargeTotal();
                        String commisionChargeToTalAfter = x.getCommissionChargeTotalAfter();
                        desc = desc + "，调差金额：" + new BigDecimal(commisionChargeTotal).subtract(new BigDecimal(commisionChargeToTalAfter)) + "元";
                    }
                    x.setDescription(desc);
                    if (!StringUtil.isBlank(x.getReconciliationStatus())) {
                        x.setNode("对账中");
                        if ("0".equals(x.getReconciliationStatus())) {
                            x.setStatus("未对账");
                            if (StringUtils.isNotEmpty(x.getOverallDiffExamineStatus())){
                                x.setNode("整体调差审批中");
                                if("0".equals(x.getOverallDiffExamineStatus())){
                                    x.setStatus("整体调差待提交审核");
                                }else if("1".equals(x.getOverallDiffExamineStatus())){
                                    x.setStatus("整体调差未审批");
                                }else if("2".equals(x.getOverallDiffExamineStatus())){
                                    x.setStatus("整体调差审批成功");
                                }else if("3".equals(x.getOverallDiffExamineStatus())){
                                    x.setStatus("整体调差审批失败");
                                }
                            }
                        }
                        if ("1".equals(x.getReconciliationStatus())) {
                            x.setStatus("对账成功");
                        }
                        if ("2".equals(x.getReconciliationStatus())) {
                            x.setStatus("对账失败");
                        }
                    }
                    if(!StringUtil.isBlank(x.getExamineStatus()) && !"0".equals(x.getExamineStatus())){
                        if(!StringUtil.isBlank(x.getMerchantType()) && "1".equals(x.getMerchantType())){
                            if ("0".equals(x.getExamineSubStatus())
                                    || "1".equals(x.getExamineSubStatus())
                                    || "2".equals(x.getExamineSubStatus())
                                    || "4".equals(x.getExamineSubStatus())){
                                x.setNode("审核中");
                                if("1".equals(x.getExamineStatus())){
                                    x.setStatus("未审核");
                                }
                                if("2".equals(x.getExamineStatus())){
                                    x.setStatus("审核成功");
                                }
                                if("3".equals(x.getExamineStatus())){
                                    x.setStatus("审核失败");
                                }
                            }else if ("3".equals(x.getExamineSubStatus())
                                    || "5".equals(x.getExamineSubStatus())
                                    || "6".equals(x.getExamineSubStatus())){
                                x.setNode("开票节点");
                                if("3".equals(x.getExamineSubStatus())){
                                    x.setStatus("开票中");
                                }
                                if("5".equals(x.getExamineSubStatus())){
                                    x.setNode("收款节点");
                                    if ("0".equals(x.getBillPayStatus()) || "2".equals(x.getBillPayStatus())){
                                        x.setStatus("开票完成未收款");
                                    }else {
                                        x.setStatus("开票完成已收款");
                                    }
                                }
                                if("6".equals(x.getExamineSubStatus())){
                                    x.setStatus("开票打回");
                                }
                            }
                        }else {
                            x.setNode("审核中");
                            if("1".equals(x.getExamineStatus())){
                                x.setStatus("未审核");
                            }
                            if("2".equals(x.getExamineStatus())){
                                x.setStatus("审核成功");
                            }
                            if("3".equals(x.getExamineStatus())){
                                x.setStatus("审核失败");
                            }
                        }
                    }
                    if (!StringUtil.isBlank(x.getAsynFkFlag())) {
                        if ("2".equals(x.getAsynFkFlag())) {
                            x.setNode("推送中");
                            x.setStatus("推送失败");
                        } else if ("1".equals(x.getAsynFkFlag())) {
                            x.setNode("费控环节");
                            x.setStatus(FkBillStatusEnum.queryByStatus(x.getFkBillStatus()).getState());
                        }
                    }
                    Map<String, Object> map = new HashMap<>();
                    map.put("countNo",x.getCountNo());
                    map.put("fkBillNo",x.getFkBillNo());
                    map.put("channelName",x.getChannelName());
                    map.put("description",x.getDescription());
                    map.put("commissionChargeTotal",x.getCommissionChargeTotal());
                    map.put("node",x.getNode());
                    map.put("status",x.getStatus());
                    map.put("asynFkSuccessDate",x.getAsynFkSuccessDate());
                    map.put("fkPayDate",x.getFkPayDate());
                    exportdata.add(map);
                });
                MyExcelSheet sheetOne = new MyExcelSheet();
                sheetOne.setAliasMap(getSettNodeMap());
                sheetOne.setDataList(exportdata);
                if (billpayList.size() < 1) {
                    // 封装导出Excel表头
                    List<Map<String, String>> maps = new ArrayList<>();
                    maps.add(getSettNodeDefaultMap());
                    sheetOne.setAliasMap(getSettNodeMap());
                    sheetOne.setDataList(maps);
                }
                sheetOne.setSheetName("结算节点列表");
                List<MyExcelSheet> myExcelSheets = new ArrayList<>();
                myExcelSheets.add(sheetOne);
                RhExcelUtil.getExcel(response, LocalDateTime.now().toString().substring(0, 10), myExcelSheets);
            }
        } catch (IOException e) {
            log.error("结算节点列表导出,系统异常原因：", e);
        }
    }

    /**
     * 封装导出Excel表头
     *
     * @return
     */
    private Map<String, String> getSettNodeMap() {
        Map<String, String> aliasMap = new LinkedHashMap<>();
        aliasMap.put("countNo", "结算单号");
        aliasMap.put("fkBillNo", "费控单号");
        aliasMap.put("channelName", "渠道");
        aliasMap.put("description", "事由描述");
        aliasMap.put("commissionChargeTotal", "手续费金额");
        aliasMap.put("node", "节点");
        aliasMap.put("status", "状态");
        aliasMap.put("asynFkSuccessDate", "费控单创建时间");
        aliasMap.put("fkPayDate", "支付完成日期");
        return aliasMap;
    }

    /**
     * 封装导出Excel表头
     *
     * @return
     */
    private Map<String, String> getSettNodeDefaultMap() {
        Map<String, String> aliasMap = new LinkedHashMap<>();
        aliasMap.put("countNo", "");
        aliasMap.put("fkBillNo", "");
        aliasMap.put("channelName", "");
        aliasMap.put("description", "");
        aliasMap.put("commissionChargeTotal", "");
        aliasMap.put("node", "");
        aliasMap.put("status", "");
        aliasMap.put("examineOperator", "");
        aliasMap.put("asynFkSuccessDate", "");
        aliasMap.put("fkPayDate", "");
        return aliasMap;
    }

    /**
     * 流量商调差后生成结算单EXCEL
     * @param queryCountData
     * @throws IOException
     */
    @Override
    public BaseResponse diffCountDetailToExcel(QueryCountData queryCountData) throws IOException {
        QueryWrapper<RylCountSettlementBillPayEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("count_no",queryCountData.getCountNo());
        RylCountSettlementBillPayEntity billPay = rylCountSettlementBillPayMapper.selectOne(queryWrapper);
        if(billPay == null){
            return BaseResponse.fail("-1", "未查询到结算单");
        }
        if("2".equals(billPay.getExamineStatus())){
            return BaseResponse.fail("-1", "审批成功无法重新生成结算单");
        }
        rylCountSettlementCountLogMapper.deleteCountLog(queryCountData.getCountNo());
        settBillService.handlerExportData(queryCountData.getCountNo(), "流量商调差后生成结算单EXCEL",billPay.getCountStartTime(),billPay.getCountEndTime());
        return BaseResponse.success();
    }

    @Override
    public BaseResponse importOverallDiffData(MultipartFile file) {
        try {
            //处理文件，汇总扣减清单手续费总金额
            //校验文件格式
            if (!StrUtil.contains(file.getOriginalFilename(),".xls") || !StrUtil.contains(file.getOriginalFilename(),".xlsx")){
                return BaseResponse.fail("-1", "请导入正确Excel文件");
            }
            //读取excel
            ExcelReader readerAll = ExcelUtil.getReader(file.getInputStream());
            //获取sheet页名字
            List<String> sheetNames = readerAll.getSheetNames();
            String fhResult = sheetNames.get(0);
            ExcelReader reader = ExcelUtil.getReader(file.getInputStream(), fhResult);
            List<OverallDiffDetailDto> overallDiffDetailDtoList = reader.read(0, 1, reader.getRowCount(), OverallDiffDetailDto.class);
            if(overallDiffDetailDtoList.isEmpty()){
                return BaseResponse.fail("-1", "Excel空文件请检查");
            }
            List<OverallDiffDetailDto> errorList = overallDiffDetailDtoList.stream().filter(x-> StringUtils.isBlank(x.getRiskName())).collect(Collectors.toList());
            if(!errorList.isEmpty()){
                return BaseResponse.fail("-1", "Excel文件中存在险种名称为空请检查");
            }
            List<OverallDiffDetailDto> errorManageComList = overallDiffDetailDtoList.stream().filter(x-> StringUtils.isBlank(x.getMonth())).collect(Collectors.toList());
            if(!errorManageComList.isEmpty()){
                return BaseResponse.fail("-1", "Excel文件中存在所属月份为空请检查");
            }
            List<OverallDiffDetailDto> errorMsgList = overallDiffDetailDtoList.stream().filter(x-> x.getAmountDeducted()==null).distinct().collect(Collectors.toList());
            if(!errorMsgList.isEmpty()){
                return BaseResponse.fail("-1", "Excel文件中存在手续费金额为空的请检查");
            }
            InputStream inputStream = file.getInputStream();
            FileUploadOutDto fileListDTO = fileUpoadUtil.uploadN(inputStream,file.getOriginalFilename(), FileUploadAddrConstant.OVERALL_DIFF);
            if (fileListDTO == null) {
                return BaseResponse.fail("-1", "整体调差扣减清单附件上传失败，请稍后再试");
            }
            BigDecimal totalAmountDeducted = overallDiffDetailDtoList.stream().map(OverallDiffDetailDto::getAmountDeducted).reduce(BigDecimal.ZERO, BigDecimal::add);
            OverallDiffRes overallDiffRes = new OverallDiffRes();
            overallDiffRes.setFileName(fileListDTO.getName());
            overallDiffRes.setFileUrl(fileListDTO.getFileUrl());
            overallDiffRes.setFileKey(fileListDTO.getFileKey());
            overallDiffRes.setTotalAmountDeducted(totalAmountDeducted);
            overallDiffRes.setOverallDiffDetailList(overallDiffDetailDtoList);
            return BaseResponse.success(overallDiffRes);
        } catch (Exception e) {
            log.error("导入整体调差扣减清单异常，原因:", e);
            return BaseResponse.fail("-1","导入整体调差扣减清单异常");
        }
    }

    @Override
    public BaseResponse overallDiffConfirmation(OverallDiffToBillReqDto overallDiffToBillReqDto) {
        log.error("整体调差请求参数：{}",JSONObject.toJSONString(overallDiffToBillReqDto));
        try {
            List<RylCountSettlementOverallDiffEntity> rylCountSettlementOverallDiffEntities =
                    rylCountSettlementOverallDiffMapper.selectList(new QueryWrapper<RylCountSettlementOverallDiffEntity>()
                            .eq("count_no", overallDiffToBillReqDto.getCountNo()).eq("is_del", "0"));
            if (!CollectionUtils.isEmpty(rylCountSettlementOverallDiffEntities)){
                return BaseResponse.fail("-1","当前结算单已存在整体调差数据,去手续费扣减清单管理页面查看");
            }
            if(new BigDecimal(overallDiffToBillReqDto.getCommissionChargeTotal()).compareTo(overallDiffToBillReqDto.getTotalAmountDeducted())<0){
                return BaseResponse.fail("-1","整体调差金额不能大于结算单的手续费总金额");
            }
            RylCountSettlementOverallDiffEntity rylCountSettlementOverallDiffEntity = new RylCountSettlementOverallDiffEntity();
            BeanUtils.copyProperties(overallDiffToBillReqDto, rylCountSettlementOverallDiffEntity);
            List<OverallDiffDetailDto> overallDiffDetailList = overallDiffToBillReqDto.getOverallDiffDetailList();
            ArrayList<RylCountSettlementOverallDiffDetailEntity> rylCountSettlementOverallDiffDetailEntities = new ArrayList<>();
            overallDiffDetailList.stream().map(dto -> {
                RylCountSettlementOverallDiffDetailEntity entity = new RylCountSettlementOverallDiffDetailEntity();
                BeanUtils.copyProperties(dto, entity);
                entity.setCountNo(overallDiffToBillReqDto.getCountNo());
                entity.setIsDel("0");
                rylCountSettlementOverallDiffDetailEntities.add(entity);
                return entity;
            }).collect(Collectors.toList());
            rylCountSettlementOverallDiffDetailMapper.mysqlInsertOrUpdateBatch(rylCountSettlementOverallDiffDetailEntities);
            BigDecimal subtract;
            if(StringUtils.isNotEmpty(rylCountSettlementOverallDiffEntity.getCommissionChargeTotalAfter())){
                subtract = new BigDecimal(rylCountSettlementOverallDiffEntity.getCommissionChargeTotalAfter()).subtract(rylCountSettlementOverallDiffEntity.getTotalAmountDeducted());
            }else {
                subtract = new BigDecimal(rylCountSettlementOverallDiffEntity.getCommissionChargeTotal()).subtract(rylCountSettlementOverallDiffEntity.getTotalAmountDeducted());
            }
            rylCountSettlementOverallDiffEntity.setCommissionChargeTotalAfter(subtract.toString());
            rylCountSettlementOverallDiffMapper.insert(rylCountSettlementOverallDiffEntity);
            RylCountSettlementBillPayEntity rylCountSettlementBillPayEntity = new RylCountSettlementBillPayEntity();
            rylCountSettlementBillPayEntity.setAdjustmentType("2");
            rylCountSettlementBillPayMapper.update(rylCountSettlementBillPayEntity,
                    new QueryWrapper<RylCountSettlementBillPayEntity>().eq("count_no", overallDiffToBillReqDto.getCountNo()));
        } catch (Exception e) {
            log.error("整体调差异常，原因:", e);
            return BaseResponse.fail("-1","整体调差异常");
        }
        return BaseResponse.success();
    }

    @Override
    public BaseResponse submitOverallDiffApproval(OverallDiffToBillReqDto overallDiffToBillReqDto) {
        try {
            if (overallDiffToBillReqDto.getId()==null){
                return BaseResponse.fail("-1","id不能为空");
            }
            RylCountSettlementOverallDiffEntity overallDiffEntity = rylCountSettlementOverallDiffMapper.selectById(overallDiffToBillReqDto.getId());
            WeChatApproveTemplateEntity weChatApproveTemplateEntity = weChatApproveTemplateMapper
                        .selectOne(new QueryWrapper<WeChatApproveTemplateEntity>()
                                .eq("template_type", "5"));
            if (weChatApproveTemplateEntity!=null){
                String countDatePeriod = SettlementUtil.dealSettDate(overallDiffEntity.getCountStartTime(), overallDiffEntity.getCountEndTime());
                log.info("结算期间：{}", countDatePeriod);
                String channelName = overallDiffEntity.getChannelName();
                String desc = countDatePeriod + " "+ (StringUtils.isBlank(channelName) ? "【" + overallDiffEntity.getChannelCode() + "】" : channelName) + "整体调差总金额：" + overallDiffEntity.getTotalAmountDeducted().toString() + "元";
                Map<String, String> params = new HashMap<>();
                params.put("actionbody", desc);
                params.put("subject", weChatApproveTemplateEntity.getTemplateName());
                params.put("templateId", weChatApproveTemplateEntity.getTemplateId());
                String protocolAddr = FileUploadAddrConstant.OVERALL_DIFF;
                String s = fileUpoadUtil.weChatUploadFile(overallDiffEntity.getFileName(),overallDiffEntity.getFileKey(),protocolAddr);
                params.put("files",s);

                overallDiffEntity.setParams(params);
                String weChatUserCode = this.getWeChatUserCode(overallDiffToBillReqDto.getUserName());
                if (weChatUserCode!=null){
                    Map<String, String> map = weChatApproveService.approvalSave(overallDiffEntity, weChatUserCode);
                    if (map.get("code").equals("0")){
                        overallDiffEntity.setExamineStatus("1");
                        overallDiffEntity.setApprovalNum(map.get("spNo"));
                        rylCountSettlementOverallDiffMapper.updateById(overallDiffEntity);
                    }else {
                        return BaseResponse.fail("-1",map.get("msg"));
                    }
                }else {
                    return BaseResponse.fail("-1","未查询到融汇核心账号对应的企微账号");
                }
            }else {
                return BaseResponse.fail("-1","未查询到企微模板");
            }
        } catch (Exception e) {
            log.error("结算单" + overallDiffToBillReqDto.getCountNo() + "，发起企微审批异常，原因：", e);
            return BaseResponse.fail("-1","发起企微审批异常");
        }
        return BaseResponse.success();
    }

    public String getWeChatUserCode(String moblie){
        try {
            log.info("查询人事系统所有人员信息开始");
            HttpHeaders headers = new HttpHeaders();
            headers.add("token" , "Jz1UTotYB48Hxv7SyMdG0FRXOmfZQbaE");
            ResponseEntity<JSONObject> resultJsonSo = externalRestTemplate.exchange(getUserUrl, HttpMethod.POST, new HttpEntity(headers), new ParameterizedTypeReference<JSONObject>(){});
            if (resultJsonSo.getBody()!=null){
                JSONObject body = resultJsonSo.getBody();
                JSONArray data = body.getJSONArray("data");
                List<SysUserDto> sysUserDtoList = data.toJavaList(SysUserDto.class);
                List<SysUserDto> sysUserDtos = sysUserDtoList.stream().filter(w -> StringUtils.isNotEmpty(w.getPhonenumber()) && w.getPhonenumber().equals(moblie)).collect(Collectors.toList());
                if (sysUserDtos.isEmpty()){
                    return null;
                }else {
                    SysUserDto sysUserDto = sysUserDtos.get(0);
                    return sysUserDto.getLoginName();
                }
            }
            return null;
        }catch (Exception e){
            log.error("企微审批查询人事系统所有人员信息异常，原因：{}",e);
            return null;
        }
    }

    @Override
    public BaseResponse overallDiffDatToExcel(OverallDiffToBillReqDto overallDiffToBillReqDto, HttpServletResponse response) {
        try {
            log.info("整体调差扣减清单导出：{}", JSONObject.toJSONString(overallDiffToBillReqDto));
            RylCountSettlementOverallDiffEntity rylCountSettlementOverallDiffEntity = rylCountSettlementOverallDiffMapper.selectById(overallDiffToBillReqDto.getId());
            List<FileUploadOutDto> fileList = fileUpoadUtil.getFileList(rylCountSettlementOverallDiffEntity.getFileKey(), rylCountSettlementOverallDiffEntity.getFileName(), FileUploadAddrConstant.OVERALL_DIFF);
            return BaseResponse.success(fileList);
        }catch (Exception e){
            log.error("整体调差扣减清单导出异常，原因：{}",e);
            return BaseResponse.fail("-1","整体调差扣减清单导出异常");
        }
    }

    @Override
    public BaseResponse getOverallDiffList(OverallDiffToBillReqDto overallDiffToBillReqDto) {
        try {
            log.info("整体调差列表查询：{}", JSONObject.toJSONString(overallDiffToBillReqDto));
            QueryWrapper<RylCountSettlementOverallDiffEntity> rylCountSettlementOverallDiffEntityQueryWrapper = new QueryWrapper<>();
            rylCountSettlementOverallDiffEntityQueryWrapper.eq(StringUtils.isNotEmpty(overallDiffToBillReqDto.getCountNo()),"count_no",overallDiffToBillReqDto.getCountNo());
            rylCountSettlementOverallDiffEntityQueryWrapper.eq(StringUtils.isNotEmpty(overallDiffToBillReqDto.getChannelCode()),"channel_code",overallDiffToBillReqDto.getChannelCode());
            rylCountSettlementOverallDiffEntityQueryWrapper.eq(StringUtils.isNotEmpty(overallDiffToBillReqDto.getExamineStatus()),"examine_status",overallDiffToBillReqDto.getExamineStatus());
            if (StringUtil.isNotBlank(overallDiffToBillReqDto.getCountStartTime()) && StringUtil.isNotBlank(overallDiffToBillReqDto.getCountEndTime())) {
                rylCountSettlementOverallDiffEntityQueryWrapper.and(wq -> wq.le("count_start_time",
                        overallDiffToBillReqDto.getCountEndTime()).ge("count_start_time",
                        overallDiffToBillReqDto.getCountStartTime()).or(wq1 -> wq1.ge("count_start_time",
                        overallDiffToBillReqDto.getCountStartTime()).le("count_start_time",
                        overallDiffToBillReqDto.getCountEndTime())).or(wq2 -> wq2.ge("count_end_time",
                        overallDiffToBillReqDto.getCountStartTime()).le("count_end_time",
                        overallDiffToBillReqDto.getCountEndTime())));
            }
            Page<RylCountSettlementOverallDiffEntity> page = new Page<RylCountSettlementOverallDiffEntity>(overallDiffToBillReqDto.getPageNum()
                    , overallDiffToBillReqDto.getPageSize());
            IPage<RylCountSettlementOverallDiffEntity> rylCountSettlementOverallDiffEntityIPage = rylCountSettlementOverallDiffMapper.queryOverallDiffList(page, rylCountSettlementOverallDiffEntityQueryWrapper);
            return BaseResponse.success(rylCountSettlementOverallDiffEntityIPage);
        }catch (Exception e){
            log.error("整体调差列表查询，原因：{}",e);
            return null;
        }
    }

    @Override
    public BaseResponse getOverallDiffDetail(OverallDiffToBillReqDto overallDiffToBillReqDto) {
        try {
            if (StringUtils.isEmpty(overallDiffToBillReqDto.getCountNo())){
                return BaseResponse.fail("-1","结算单号不能为空");
            }
            List<OverallDiffDetailDto> overallDiffDetailDtoList = rylCountSettlementOverallDiffDetailMapper.selectAmountDeductedByCountNo(overallDiffToBillReqDto.getCountNo());
            return BaseResponse.success(overallDiffDetailDtoList);
        }catch (Exception e){
            log.error("整体调差扣减清单导出异常，原因：{}",e);
            return BaseResponse.fail("-1","整体调差扣减清单导出异常");
        }
    }

}
