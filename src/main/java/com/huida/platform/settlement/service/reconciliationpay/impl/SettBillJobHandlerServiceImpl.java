package com.huida.platform.settlement.service.reconciliationpay.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.huida.platform.common.util.DateUtil;
import com.huida.platform.common.util.ObjConverUtil;
import com.huida.platform.common.util.OssFileUtil;
import com.huida.platform.settlement.alert.AlertUtil;
import com.huida.platform.settlement.constant.*;
import com.huida.platform.settlement.dto.SendMailYkDto;
import com.huida.platform.settlement.dto.SettleStatusReasonDTO;
import com.huida.platform.settlement.dto.reconciliationpay.RylCountSettBillDeailVitalDto;
import com.huida.platform.settlement.dto.reconciliationpay.RylUserFkDataDto;
import com.huida.platform.settlement.dto.zj.ZjBillPayReqDto;
import com.huida.platform.settlement.dto.zj.ZjBillPayResDto;
import com.huida.platform.settlement.entity.AlertCfgEntity;
import com.huida.platform.settlement.entity.count.FkCemXgV;
import com.huida.platform.settlement.entity.count.RylCountSettlementBillMappingCfgEntity;
import com.huida.platform.settlement.entity.countpay.*;
import com.huida.platform.settlement.entity.settlement.*;
import com.huida.platform.settlement.mapper.RylCountSupplierPolicySettlementMapper;
import com.huida.platform.settlement.mapper.reconciliationpay.*;
import com.huida.platform.settlement.mapper.settlement.*;
import com.huida.platform.settlement.service.reconciliationpay.SettBillJobHandlerService;
import com.huida.platform.settlement.util.NumberServiceUtil;
import com.huida.platform.settlement.util.SettlementUtil;
import com.huida.platform.settlement.util.WorkDayUtils;
import com.huida.platform.settlement.vo.reconciliationpay.*;
import com.xxl.job.core.biz.model.ReturnT;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 结算清单 service
 * <AUTHOR>
 */
@Service
@Slf4j
public class SettBillJobHandlerServiceImpl implements SettBillJobHandlerService {
    @Autowired
    private RylCountSettlementBillPayMapper rylCountSettlementBillPayMapper;
    @Autowired
    NumberServiceUtil numberServiceUtil;
    @Autowired
    private RylCountSettlementBillMappingCfgMapper rylCountSettlementBillMappingCfgMapper;
    @Autowired
    private FkCemXgViewMapper fkCemXgViewMapper;
    @Autowired
    private RylCountSettlementUploadPayMapper rylCountSettlementUploadPayMapper;
    @Autowired
    private RylCountSettResultMapper rylCountSettResultMapper;
    @Autowired
    private RylCountSettlementChannelCfgMapper rylCountSettlementChannelCfgMapper;
    @Autowired
    private RylCountSettlementUploadVitalMapper rylCountSettlementUploadVitalMapper;
    @Autowired
    private RylCountSettResultVitalMapper rylCountSettResultVitalMapper;
    @Autowired
    private RylCountSettResultHandMapper rylCountSettResultHandMapper;
    @Autowired
    private RylCountSettlementChannelFinanceMappingCfgMapper rylCountSettlementChannelFinanceMappingCfgMapper;
    @Autowired
    private RylCountSettlementBillTempCfgMapper rylCountSettlementBillTempCfgMapper;
    @Value("${fk.hk.domain}")
    private String fkHkDns;
    @Value("${fk.rh.domain}")
    private String fkrhDns;
    @Value("${fk.url}")
    private String fkUrl;
    @Value("${hkUuid}")
    private String hkUuid;
    @Value("${rhUuid}")
    private String rhUuid;
    @Value("${yBUuid}")
    private String yBUuid;
    @Autowired
    private RylCountSettlementCountLogMapper rylCountSettlementCountLogMapper;
    @Autowired
    private RylCountSettlementRateMapper rylCountSettlementRateMapper;
    @Autowired
    private RylCountSettlementFileConfigLogMapper rylCountSettlementFileConfigLogMapper;
    @Autowired
    private RylCountSettlementBillDeptCfgMapper rylCountSettlementBillDeptCfgMapper;
    @Autowired
    private RylCountSettlementBillDetailPayMapper rylCountSettlementBillDetailPayMapper;
    @Autowired
    private RestTemplate externalRestTemplate;
    @Autowired
    private RestTemplate restTemplate;
    @Value("${sendYkMailUrl}")
    private String sendYkMailUrl;
    @Autowired
    private OssFileUtil ossFileUtil;
    @Value("${aysnZjPaymentStatus}")
    private String aysnZjPaymentStatus;
    @Autowired
    private ErrorData errorData;
    private AlertUtil alertUtil = new AlertUtil();
    @Autowired
    private RylCountSettResultMapper rylCountSettlementResultMapper;
    @Autowired
    private RylCountSettlementProtocolMapper rylCountSettlementProtocolMapper;

    @Value("#{${protocolTypeList}}")
    private Map<String,List<String>> protocolTypeList;

    @Autowired
    private RylCountSettlementHkrhResultMapper hkrhResultMapper;
    @Autowired
    private RylCountSettBillDetailVitalMapper rylCountSettBillDetailVitalMapper;
    @Autowired
    private RylCountSettlementChannelLabMapper rylCountSettlementChannelLabMapper;
    @Autowired
    private RylCountSupplierPolicySettlementMapper rylCountSupplierPolicySettlementMapper;
    @Value("${stockChannel}")
    private String stockChannel;
    //费控查询账单信息接口地址
    @Value("${fk.queryBillDetail}")
    private String fkBillDetailQueryUrl;

    @Override
    public ReturnT<String> syncSettBillToFk(String s) {
        try {
            /**
             * 指定参数时，可以重新推送；不指定时，筛选所有审批过的结算清单进行推送
             */
            log.info(JSONObject.toJSONString(protocolTypeList));
            RylCountSettlementBillSyncVo reqVo = StringUtils.isBlank(s) ? null : JSON.parseObject(s, RylCountSettlementBillSyncVo.class);
            List<RylCountSettlementBillPayEntity> billPayList = rylCountSettlementBillPayMapper.queryUnSyncBillPay(reqVo == null ? null : reqVo.getCountNo());
            if (billPayList.isEmpty()) {
                return null;
            }
            Map<String,Map<String,String>> map = new HashMap<>();
            //查待推送费控的结算单 费控账号及代付标识，返回通过校验的结算单号列表
            List<String> validCountNos = handCountNoToFk(billPayList, map);
            if (validCountNos.isEmpty()) {
                log.info("所有结算单校验都未通过，无需继续处理");
                return null;
            }
            // 只处理通过校验的结算单，从原列表中过滤
            List<RylCountSettlementBillPayEntity> billPayListNew = billPayList.stream()
                    .filter(billPay -> validCountNos.contains(billPay.getCountNo()))
                    .collect(Collectors.toList());
            for (RylCountSettlementBillPayEntity billPay : billPayListNew) {
                Map<String, String> billMap = map.get(billPay.getCountNo());
                String settBouncers = billMap.get("settBouncers");
                String depCode = billMap.get("depCode");
                String countNo = billPay.getCountNo();
                String errorMsg = checkMsgToFk(billPay);
                if (StringUtils.isNotBlank(errorMsg)) {
                    log.info("结算单号{}，信息有误{}，暂不同步", countNo, errorMsg);
                    billPay.setAsynFkFlag(2);
                    billPay.setAsynFkDesc(errorMsg);
                    rylCountSettlementBillPayMapper.updateBillToFkResult(billPay);
                    continue;
                }
                String channelType = null;
                List<RylCountSettlementChannelFinanceMappingCfgEntity> rylCountSettlementChannelFinanceMappingCfgEntityList = null;
                try {
                    RylCountSettlementChannelCfgEntity rylCountSettlementChannelCfgEntity = rylCountSettlementChannelCfgMapper.queryChannelNameByCode(billPay.getChannelCode());
                    if (rylCountSettlementChannelCfgEntity.getChannelType() != null) {
                        channelType = rylCountSettlementChannelCfgEntity.getChannelType().toString();
                        QueryWrapper<RylCountSettlementChannelFinanceMappingCfgEntity> channelFinanceMappingCfgQueryWrapper = new QueryWrapper();
                        channelFinanceMappingCfgQueryWrapper.eq("channel_code",rylCountSettlementChannelCfgEntity.getChannelCode());
                        if (StringUtils.isNotEmpty(billPay.getManageCom())){
                            String[] split = StringUtil.split(billPay.getManageCom(), "|");
                            List<String> list = Arrays.asList(split).stream()
                                    .map(x -> x.substring(0, Math.min(x.length(), 4)))
                                    .distinct()
                                    .collect(Collectors.toList());
                            if (list.size()==1){
                                channelFinanceMappingCfgQueryWrapper.like("manage_com",list.get(0));
                            }else if (list.size()==2){
                                channelFinanceMappingCfgQueryWrapper.and(queryWrapper1 ->
                                        queryWrapper1.like("manage_com", list.get(0))
                                                .or(queryWrapper2 -> queryWrapper2.like("manage_com", list.get(1))));

                            }else if (list.size()==3){
                                channelFinanceMappingCfgQueryWrapper.and(queryWrapper1 ->
                                        queryWrapper1.like("manage_com", list.get(0))
                                                .or(queryWrapper2 -> queryWrapper2.like("manage_com", list.get(1)))
                                                .or(queryWrapper3 -> queryWrapper3.like("manage_com", list.get(2))));

                            }else {
                                channelFinanceMappingCfgQueryWrapper.and(queryWrapper1 ->
                                        queryWrapper1.like("manage_com", list.get(0))
                                                .or(queryWrapper2 -> queryWrapper2.like("manage_com", list.get(1)))
                                                .or(queryWrapper3 -> queryWrapper3.like("manage_com", list.get(2)))
                                                .or(queryWrapper4 -> queryWrapper4.like("manage_com", list.get(3))));
                            }
                        }
                        rylCountSettlementChannelFinanceMappingCfgEntityList = rylCountSettlementChannelFinanceMappingCfgMapper.selectList(channelFinanceMappingCfgQueryWrapper);
                    }
                } catch (Exception e) {
                    log.error("结算单号：{},查询结算清单所属的渠道类型异常，原因：{}",countNo, e);
                }

                boolean yinB = channelType.equals("3") && rylCountSettlementChannelFinanceMappingCfgEntityList.isEmpty();
                boolean fYin = (!channelType.equals("3")) && (rylCountSettlementChannelFinanceMappingCfgEntityList.isEmpty()
                                    || rylCountSettlementChannelFinanceMappingCfgEntityList.size()!=1);
                if (yinB || fYin) {
                    log.info("结算单号{}，推送费控信息匹配失败，暂不同步", countNo);
                    billPay.setAsynFkFlag(2);
                    billPay.setAsynFkDesc("推送费控信息匹配失败");
                    rylCountSettlementBillPayMapper.updateBillToFkResult(billPay);
                    continue;
                }
                RylCountSettlementBillMappingCfgEntity billMappingCfgEntity = null;
                if (channelType != null && "3".equals(channelType)) {
                    billMappingCfgEntity = rylCountSettlementBillMappingCfgMapper.selectById(yBUuid);
                }else {
                    billMappingCfgEntity = rylCountSettlementBillMappingCfgMapper.selectById(billPay.getUserFkUuid());
                }
                if (billMappingCfgEntity == null) {
                    log.info("结算单号：{},无费控系统用户配置，暂不同步", countNo);
                    continue;
                }
                //获取费控系统用户名称, 费控拆分 根据用户部门 : 弘康账单提交弘康费控,融汇账单提交融汇费控
                String fkUnitName = billMappingCfgEntity.getFkUnitName();
                /**
                 * 非代付结算单，推一次费控（维持现状）
                 * 代付结算单，推二次费控（一次单据类型是还款单，一次单据类型是代付单） SXFHK-还款单、SXFDF-代付单
                 *
                 *   银保渠道：推一次借款单(弘康)、推一次还款单(弘康)
                 *   弘康融汇：推一次借款单(弘康)、推一次报销单(弘康)
                 *   代付渠道：推一次代付单(融汇)、推一次还款单(弘康)
                 *   非代付渠道: 科技公司-推一次服务费报销单(融汇)、其他-推一次手续费报销单(弘康/融汇,看结算主体)
                 */
                HttpResponse ybOrHkRHJkResContent = null;
                String reqContent = "";
                List<RylCountSettlementBillTempCfgEntity> rylCountSettlementBillTempCfgList = null;
                String billType = "";
                //推送的费控地址 完整地址
                String sendfkUrl = "";
                if (SettlementConstant.SETT_NOT_PAYBEHALF.equals(billPay.getIsPayBehalf())) {
                    billType = SettlementConstant.FK_BILLTYPE_SXFYTBXF;
                    if (channelType != null && "4".equals(channelType)) {
                        //科技类公司服务费报销单
                        billType = SettlementConstant.FK_BILLTYPE_FWFYTBXF;
                    } else if (channelType != null && ("3".equals(channelType) || "1".equals(billPay.getIsHkrhSett()) || SettlementConstant.HK_RH.equals(billPay.getChannelCode()))) {
                        //银保或者弘康融汇间手续费借款单
                        billType = SettlementConstant.FK_BILLTYPE_SXFYTJK;
                        rylCountSettlementBillTempCfgList = rylCountSettlementBillTempCfgMapper.selectList(new QueryWrapper<RylCountSettlementBillTempCfgEntity>()
                                .eq("bill_type", billType));
                        if (rylCountSettlementBillTempCfgList.isEmpty()) {
                            log.info("结算单：{},单据类型：{},未查询到单据模版配置", countNo, billType);
                            continue;
                        }
                        String content = dealFkReqContent(billPay, billMappingCfgEntity, billType, rylCountSettlementBillTempCfgList, rylCountSettlementChannelFinanceMappingCfgEntityList, settBouncers, depCode, channelType);
                        if (content == null) {
                            billPay.setAsynFkFlag(2);
                            billPay.setAsynFkDesc("根据账号信息匹配保单手续费汇总，分摊后的手续费金额再次汇总后与结算单手续费总金额不相等");
                            rylCountSettlementBillPayMapper.updateBillToFkResult(billPay);
                            continue;
                        }
                        sendfkUrl = fkUnitName.startsWith(CommonConstant.FKUSERDEPT_HK) ? fkHkDns + fkUrl : fkrhDns + fkUrl;
                        log.info("结算单{},借款单类型,推送费控请求地址：{}", countNo, sendfkUrl);
                        log.info("结算单{},借款单类型,推送费控请求报文：{}", countNo, content);
                        ybOrHkRHJkResContent = HttpRequest.post(sendfkUrl).body(content).execute();
                        log.info("结算单{},借款单类型，推送费控返回报文：{}", countNo, ybOrHkRHJkResContent);
                        billType = SettlementConstant.FK_BILLTYPE_SXFHKF;
                        if ("1".equals(billPay.getIsHkrhSett()) || SettlementConstant.HK_RH.equals(billPay.getChannelCode())) {
                            billType = SettlementConstant.FK_BILLTYPE_SXFYTBXF;
                        }
                    }
                    rylCountSettlementBillTempCfgList = rylCountSettlementBillTempCfgMapper.selectList(new QueryWrapper<RylCountSettlementBillTempCfgEntity>()
                            .eq("bill_type",billType));
                    if (rylCountSettlementBillTempCfgList.isEmpty()){
                        log.info("结算单：{},单据类型：{},未查询到单据模版配置", countNo, billType);
                        continue;
                    }
                    billPay.setBillType(billType);
                    reqContent = dealFkReqContent(billPay, billMappingCfgEntity, billType, rylCountSettlementBillTempCfgList, rylCountSettlementChannelFinanceMappingCfgEntityList, settBouncers, depCode, channelType);
                } else if (SettlementConstant.SETT_IS_PAYBEHALF.equals(billPay.getIsPayBehalf())) {
                    /**因借款单是弘康提，故而还款单需要用弘康的费控账号推送 (弘康下 目前有电子商务部与中介部两个账号，不特殊指定时默认还款单以弘康中介部账户推送费控)*/
                    RylCountSettlementBillMappingCfgEntity hkbillMappingCfg = rylCountSettlementBillMappingCfgMapper.selectById(hkUuid);
                    rylCountSettlementBillTempCfgList = rylCountSettlementBillTempCfgMapper.selectList(new QueryWrapper<RylCountSettlementBillTempCfgEntity>()
                            .eq("bill_type",SettlementConstant.FK_BILLTYPE_SXFHKF));
                    if (rylCountSettlementBillTempCfgList.isEmpty()){
                        log.info("结算单：{},单据类型：{},未查询到单据模版配置", countNo, SettlementConstant.FK_BILLTYPE_SXFHKF);
                        continue;
                    }
                    log.info("代付渠道结算单{},还款单费控账号：{}", countNo, hkUuid);
                    String fkHkUnitName = hkbillMappingCfg.getFkUnitName();
                    String hkReqContent = dealFkReqContent(billPay, hkbillMappingCfg, SettlementConstant.FK_BILLTYPE_SXFHKF, rylCountSettlementBillTempCfgList, rylCountSettlementChannelFinanceMappingCfgEntityList, settBouncers, depCode, channelType);
                    String toFkUrl = fkHkUnitName.startsWith(CommonConstant.FKUSERDEPT_HK) ? fkHkDns + fkUrl : fkrhDns + fkUrl;
                    log.info("代付渠道结算单{},还款单类型，推送费控接口地址：{}", countNo, toFkUrl);
                    log.info("代付渠道结算单{},还款单类型，推送费控请求报文：{}", countNo, hkReqContent);
                    HttpResponse hkResContent = HttpRequest.post(toFkUrl).body(hkReqContent).execute();
                    log.info("代付渠道结算单{},还款单类型，推送费控返回报文：{}", countNo, hkResContent);
                    if (200 == hkResContent.getStatus()) {
                        String body = hkResContent.body();
                        JSONObject response = JSONObject.parseObject(body);
                        JSONArray resJsonArray = response.getJSONArray("rspData");
                        if (resJsonArray != null) {
                            JSONObject dataObj = resJsonArray.getJSONObject(0);
                            if (dataObj != null) {
                                billPay.setFkVerificationSheet((String) dataObj.get("BILLNO"));
                            }
                        }
                    }
                    /**还款单未发生实际的支付，故需根据代付单回写支付结果*/
                    rylCountSettlementBillTempCfgList = rylCountSettlementBillTempCfgMapper.selectList(new QueryWrapper<RylCountSettlementBillTempCfgEntity>()
                            .eq("bill_type",SettlementConstant.FK_BILLTYPE_SXFDF));
                    if (rylCountSettlementBillTempCfgList.isEmpty()) {
                        log.info("结算单：{},单据类型：{},未查询到单据模版配置", countNo, SettlementConstant.FK_BILLTYPE_SXFDF);
                        continue;
                    }
                    billPay.setBillType(SettlementConstant.FK_BILLTYPE_SXFDF);
                    billType = SettlementConstant.FK_BILLTYPE_SXFDF;
                    reqContent = dealFkReqContent(billPay, billMappingCfgEntity, SettlementConstant.FK_BILLTYPE_SXFDF, rylCountSettlementBillTempCfgList, rylCountSettlementChannelFinanceMappingCfgEntityList, settBouncers, depCode, channelType);
                }
                /**pom文件引入的hutool-all依赖，支持的post请求*/
                sendfkUrl = fkUnitName.startsWith(CommonConstant.FKUSERDEPT_HK) ? fkHkDns + fkUrl : fkrhDns + fkUrl;
                log.info("结算单{},推送费控,单据类型：{},请求地址：{}", countNo, billType, sendfkUrl);
                log.info("结算单{},推送费控,单据类型：{},请求报文：{}", countNo, billType, reqContent);
                HttpResponse resContent = HttpRequest.post(sendfkUrl).body(reqContent).execute();
                log.info("结算单{},推送费控,单据类型：{},返回报文：{}", countNo, billType, resContent);
                if (ybOrHkRHJkResContent != null && ("3".equals(channelType) || "1".equals(billPay.getIsHkrhSett()) || SettlementConstant.HK_RH.equals(billPay.getChannelCode()))) {
                    if (200 == resContent.getStatus()) {
                        String body = resContent.body();
                        JSONObject response = JSONObject.parseObject(body);
                        JSONArray resJsonArray = response.getJSONArray("rspData");
                        if (resJsonArray != null) {
                            JSONObject dataObj = resJsonArray.getJSONObject(0);
                            if (dataObj != null) {
                                billPay.setFkVerificationSheet((String) dataObj.get("BILLNO"));
                            }
                        }
                    }
                    resContent = ybOrHkRHJkResContent;
                    billPay.setBillType(SettlementConstant.FK_BILLTYPE_SXFYTJK);
                }
                if (200 == resContent.getStatus()) {
                    String resBody = resContent.body();
                    JSONObject response = JSONObject.parseObject(resBody);
                    String rspCode = response.getString("rspCode");
                    if ("999999".equals(rspCode)){
                        billPay.setAsynFkFlag(2);
                        billPay.setAsynFkDesc("失败");
                        billPay.setAsynFkSuccessDate(new Date());
                        rylCountSettlementBillPayMapper.updateBillToFkResult(billPay);
                    }else {
                        JSONArray resJsonArray = response.getJSONArray("rspData");
                        if (resJsonArray != null) {
                            JSONObject dataObj = resJsonArray.getJSONObject(0);
                            if (dataObj != null) {
                                String status = dataObj.getString("STATUS");
                                billPay.setAsynFkFlag("Y".equals(status) ? 1 : 2);
                                billPay.setAsynFkDesc("Y".equals(status) ? "成功" : "失败");
                                billPay.setAsynFkSuccessDate(new Date());
                                billPay.setFkBillNo((String) dataObj.get("BILLNO"));
                                rylCountSettlementBillPayMapper.updateBillToFkResult(billPay);
                                if ("Y".equals(status)){
                                    //约款邮件发送
                                    try {
                                        paymentOfContractSendMail(billPay,settBouncers);
                                    }catch (Exception e){
                                        log.error("结算单约款邮件发送异常，结算单：{},异常原因：{}",billPay.getCountNo(),e);
                                    }
                                }
                            }
                        }
                    }
                }else{
                    log.info("结算单{},推送费控请求异常：{}", countNo, resContent);
                }
            }
        }catch (Exception e){
            log.error("推送费控，系统异常，原因：{}",e);
        }
        return null;
    }

    @Override
    public ReturnT<String> syncBillPayResult(String s) {
        FkReqDataVO fkReqDataVO = StringUtils.isBlank(s)?null: JSON.parseObject(s,FkReqDataVO.class);
        String countNo = null;
        Date startDate =null;
        Date endDate =null;
        if(fkReqDataVO!=null){
            countNo = fkReqDataVO.getCountNo();
            startDate = fkReqDataVO.getStartDate();
            endDate = fkReqDataVO.getEndDate();
        }
        try {
            // 1. 获取需要更新支付状态的单号
            List<Map<String, Object>> rawResult = rylCountSettlementBillPayMapper.queryUnPayList(countNo, startDate, endDate);
            if (CollectionUtils.isEmpty(rawResult)) {
                log.info("无需要更新费控支付状态的结算单号");
                return ReturnT.SUCCESS;
            }
            // 2. 按user_fk_uuid分组
            Map<Integer, List<String>> billNosMap = rawResult.stream().collect(
                    Collectors.groupingBy(map -> (Integer) map.get("user_fk_uuid"), Collectors.mapping(map -> (String) map.get("fk_bill_no"), Collectors.toList())
                    ));
            // 3. 预加载费控账号配置
            Set<Integer> fkUuids = billNosMap.keySet();
            Map<Integer, RylCountSettlementBillMappingCfgEntity> configMap =
                    rylCountSettlementBillMappingCfgMapper.selectBatchIds(fkUuids)
                            .stream()
                            .collect(Collectors.toMap(RylCountSettlementBillMappingCfgEntity::getUuid, cfg -> cfg));
            // 4. 并行处理不同费控账号的查询
            //新增费控接口,优先接口查询支付结果,查不到在用老的视图查
            List<FkCemXgV> allSuccessResults = new ArrayList<>();
            List<String> allFailedBills = new ArrayList<>();
            // 使用CompletableFuture并行处理
            List<CompletableFuture<QueryResult>> futures = billNosMap.entrySet().stream()
                    .map(entry -> CompletableFuture.supplyAsync(() ->
                            queryFkPaymentStatus(entry.getKey(), entry.getValue(), configMap.get(entry.getKey()))
                    ))
                    .collect(Collectors.toList());
            // 等待所有查询完成并合并结果
            for (CompletableFuture<QueryResult> future : futures) {
                try {
                    QueryResult result = future.get(30, TimeUnit.SECONDS); // 设置超时
                    allSuccessResults.addAll(result.getSuccessResults());
                    allFailedBills.addAll(result.getFailedBills());
                } catch (Exception e) {
                    log.error("查询费控支付状态异常: {}", e.getMessage());
                }
            }
            // 5. 批量处理支付结果
            if (!allSuccessResults.isEmpty()) {
                processPaymentResults(allSuccessResults);
            }
            // 6. 查询老视图作为兜底
            if (!allFailedBills.isEmpty()) {
                //确保list里不会放入null值,否则oracle库不支持这种jdbc类型会报错
                List<String> cleanBillNos = allFailedBills.stream()
                        .filter(Objects::nonNull)
                        .filter(StringUtils::isNotBlank)
                        .distinct() // 去重
                        .collect(Collectors.toList());
                if (!cleanBillNos.isEmpty()) {
                    log.info("查询费控单据信息视图，单据数量：{}", cleanBillNos.size());
                    List<FkCemXgV> fallbackResults = fkCemXgViewMapper.selectBillNoResult(cleanBillNos);
                    log.info("查询费控单据信息视图，返回参数：{}", fallbackResults.size());
                    if (!fallbackResults.isEmpty()) {
                        processPaymentResults(fallbackResults);
                    }
                } else {
                    log.warn("过滤后没有有效的失败单据号需要查询视图");
                }
            }
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("从费控同步结算审批单支付结果异常，原因：{}", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 处理支付结果
     *
     * @param list
     */
    private void processPaymentResults(List<FkCemXgV> list) {
        if (!list.isEmpty()) {
            try {
                //1、结算清单明细表 回写 结算系统
                for (FkCemXgV fkCemXgV : list) {
                    RylCountSettlementBillPayEntity rylCountSettlementBillPayEntity = rylCountSettlementBillPayMapper.queryCountNo(fkCemXgV.getBillNo());
                    //保单级 结算数据 支付结果回写
                    rylCountSettlementUploadPayMapper.updatePayStatus(rylCountSettlementBillPayEntity.getCountNo(), rylCountSettlementBillPayEntity.getChannelCode(), fkCemXgV.getPayStatus());
                    List<RylCountSettlementUploadPayEntity> billDetailList = rylCountSettlementUploadPayMapper.queryPolicyDetail(rylCountSettlementBillPayEntity.getCountNo(), rylCountSettlementBillPayEntity.getChannelCode());
                    if (billDetailList != null && billDetailList.size() > 0) {
                        QueryWrapper<RylCountSettlementBillDetailPayEntity> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("count_no", rylCountSettlementBillPayEntity.getCountNo());
                        queryWrapper.isNotNull("commission_charge_total_after");
                        queryWrapper.isNotNull("upload_id");
                        List<RylCountSettlementBillDetailPayEntity> billDetailPayList = rylCountSettlementBillDetailPayMapper.selectList(queryWrapper);
                        if (!billDetailPayList.isEmpty()) {
                            billDetailList.stream().forEach(x -> {
                                List<RylCountSettlementBillDetailPayEntity> payEntityList = billDetailPayList.stream().filter(a -> a.getUploadId().toString().equals(x.getId().toString())).collect(Collectors.toList());
                                if (!payEntityList.isEmpty()) {
                                    x.setCommissionCharge(new BigDecimal(payEntityList.get(0).getCommissionChargeTotalAfter()));
                                }
                            });
                        }
                        rylCountSettResultMapper.updatePayStatusByResult(billDetailList, fkCemXgV.getPayStatus());
                    }
                    //非保单级 结算数据 支付结果回写
                    rylCountSettlementUploadVitalMapper.updatePayStatus(rylCountSettlementBillPayEntity.getCountNo(), rylCountSettlementBillPayEntity.getChannelCode(), fkCemXgV.getPayStatus());
                    List<RylCountSettBillDeailVitalDto> vitalDtoList = rylCountSettlementUploadVitalMapper.queryPolicyDetail(rylCountSettlementBillPayEntity.getCountNo(), rylCountSettlementBillPayEntity.getChannelCode());
                    if (vitalDtoList != null && vitalDtoList.size() > 0) {
                        QueryWrapper<RylCountSettBillDetailVitalEntity> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("count_no", rylCountSettlementBillPayEntity.getCountNo());
                        queryWrapper.isNotNull("commission_charge_after");
                        queryWrapper.isNotNull("upload_vital_id");
                        List<RylCountSettBillDetailVitalEntity> vitalEntityList = rylCountSettBillDetailVitalMapper.selectList(queryWrapper);
                        if (!vitalEntityList.isEmpty()) {
                            vitalDtoList.stream().forEach(x -> {
                                List<RylCountSettBillDetailVitalEntity> vitalList = vitalEntityList.stream().filter(a -> a.getUploadVitalId().toString().equals(x.getVitalId().toString())).collect(Collectors.toList());
                                if (!vitalList.isEmpty()) {
                                    x.setCommissionCharge(vitalList.get(0).getCommissionChargeAfter());
                                }
                            });
                        }
                        //支付宝特殊处理
                        if (stockChannel.equals(rylCountSettlementBillPayEntity.getChannelCode())) {
                            List<Long> vitalIdList = vitalDtoList.stream().map(RylCountSettBillDeailVitalDto::getId).collect(Collectors.toList());
                            List<RylCountSettResultVitalEntity> vitalEntities = rylCountSettResultVitalMapper.selectBatchIds(vitalIdList);
                            if (!vitalEntities.isEmpty()) {
                                List<String> vitalChannelCode = vitalEntities.stream().map(RylCountSettResultVitalEntity::getChannelCode).distinct().collect(Collectors.toList());
                                if (vitalChannelCode.size() == 1) {
                                    vitalDtoList.stream().forEach(a -> {
                                        a.setChannelCode(vitalChannelCode.get(0));
                                    });
                                } else {
                                    vitalDtoList.stream().forEach(x -> {
                                        List<RylCountSettResultVitalEntity> vitalList = vitalEntities.stream().filter(a -> a.getId().equals(x.getId())).collect(Collectors.toList());
                                        if (!vitalList.isEmpty()) {
                                            x.setChannelCode(vitalList.get(0).getChannelCode());
                                        }
                                    });
                                }
                            }
                        }

                        rylCountSettResultVitalMapper.updatePayResult(vitalDtoList, fkCemXgV.getPayStatus());
                        if ("ronghui".equals(rylCountSettlementBillPayEntity.getChannelCode())) {
                            QueryWrapper<RylCountSupplierPolicySettlementEntity> settQueryWrapper = new QueryWrapper<>();
                            settQueryWrapper.eq("channel_code", "ronghui");
                            settQueryWrapper.eq("count_no", rylCountSettlementBillPayEntity.getCountNo());
                            List<RylCountSupplierPolicySettlementEntity> settlementEntities = rylCountSupplierPolicySettlementMapper.selectList(settQueryWrapper);
                            if (!settlementEntities.isEmpty()) {
                                settlementEntities.stream().forEach(x -> {
                                    x.setCountPayStatus(Integer.valueOf(fkCemXgV.getPayStatus()));
                                    rylCountSupplierPolicySettlementMapper.updateById(x);
                                });
                            }
                        }
                    }
                    //判断费控单总金额是否与结算单总金额一致
                    String amount = StringUtils.isBlank(rylCountSettlementBillPayEntity.getCommissionChargeTotalAfter()) ? rylCountSettlementBillPayEntity.getCommissionChargeTotal() : rylCountSettlementBillPayEntity.getCommissionChargeTotalAfter();
                    BigDecimal billAmount = new BigDecimal(amount);
                    BigDecimal fkAmount = new BigDecimal(fkCemXgV.getOriginalCurrencySum());
                    if (billAmount.compareTo(fkAmount) != 0) {
                        this.fkNotJsYj(rylCountSettlementBillPayEntity.getCountNo(), fkCemXgV.getBillNo());
                    }
                    //弘康融汇间结算单支付状态更新
                    if (rylCountSettlementBillPayEntity.getIsHkrhSett() != null && rylCountSettlementBillPayEntity.getIsHkrhSett().equals("1")) {
                        RylCountSettlementHkrhResultEntity hkrhResultEntity = new RylCountSettlementHkrhResultEntity();
                        hkrhResultEntity.setCountPayStatus(Integer.valueOf(fkCemXgV.getPayStatus()));
                        hkrhResultMapper.update(hkrhResultEntity, new QueryWrapper<RylCountSettlementHkrhResultEntity>().eq("count_no", rylCountSettlementBillPayEntity.getCountNo()));
                    }
                }
                //2、回写 结算单主表
                rylCountSettlementBillPayMapper.batchUpdatePayFlag(list);
            } catch (Exception e) {
                log.error("从费控同步结算审批单支付结果，回写结算系统异常，原因:{}", e);
            }
        }
    }

    public void fkNotJsYj(String countNo,String fkBillNo){
        try {
            List<AlertCfgEntity> alertCfgEntityList = alertUtil.getAlertMes("N0024");
            if(!alertCfgEntityList.isEmpty()){
                for(AlertCfgEntity alertCfg : alertCfgEntityList){
                    String content = alertCfg.getContent();
                    content = content.replace("countNo",countNo);
                    content = content.replace("fkBillNo",fkBillNo);
                    errorData.put(alertCfg.getAlertName(), content);
                    AlertUtil.errorMsg(content,"COUNT");
                }
            }
        }catch (Exception e){
            log.error("费控单总金额是否与结算单总金额不一致发送企微预警异常：{}",e);
        }
    }

    @Override
    public ReturnT<String> syncPaidToResult(String s){
        log.info("处理打标文件，入参：{}", s);
        HandResultVo handResultVo = StringUtils.isBlank(s) ? null : JSON.parseObject(s, HandResultVo.class);
        if(handResultVo == null){
            log.info("没有需要更新到result表里的手工结算数据");
            return null;
        }
        QueryWrapper<RylCountSettlementResultHand> queryWrapper = new QueryWrapper<>();
        if(!StringUtils.isBlank(handResultVo.getPolicyNo())){
            queryWrapper.eq("policy_no",handResultVo.getPolicyNo());
        }
        if(!StringUtils.isBlank(handResultVo.getFlagType())){
            queryWrapper.eq("flag_type",handResultVo.getFlagType());
        }
        if(!StringUtils.isBlank(handResultVo.getConfiglogId())){
            queryWrapper.eq("configlog_id",handResultVo.getConfiglogId());
        }
        queryWrapper.apply(" is_del=0 and flag_type!=0 and (flag is null or flag='') ");
        List<RylCountSettlementResultHand> resultHandList = rylCountSettResultHandMapper.selectList(queryWrapper);
        if (resultHandList.isEmpty()) {
            log.info("没有需要更新到result表里的手工结算数据");
            return null;
        }
        QueryWrapper<RylCountSettlementChannelLabEntity> labQueryWrapper = new QueryWrapper<>();
        labQueryWrapper.eq("is_del",0);
        List<RylCountSettlementChannelLabEntity> labEntityList = rylCountSettlementChannelLabMapper.selectList(labQueryWrapper);

        List<String> configList = resultHandList.stream().map(RylCountSettlementResultHand::getConfiglogId).distinct().collect(Collectors.toList());
        SettleStatusReasonDTO statusReasons = new SettleStatusReasonDTO();
        statusReasons.addRuleCode("DEFAULT").addMessage("可结");
        configList.stream().forEach(x->{
            List<String> errodMsg = new ArrayList<>();
            List<RylCountSettlementResultHand> paidLists = rylCountSettResultHandMapper.getUnAsynToResultLists(x);
            paidLists.stream().forEach(paidData -> {
                switch (paidData.getFlagType()) {
                    case 1:
                        handPaidData(paidData,errodMsg,statusReasons,labEntityList);
                        break;
                    case 2:
                        handBackToPayData(paidData,errodMsg,statusReasons);
                        break;
                    case 3:
                        handNeedBackPayData(paidData,errodMsg,statusReasons,labEntityList);
                        break;
                    case 4:
                        handNeedToPay(paidData,errodMsg,statusReasons,labEntityList);
                    default:
                        log.info("系统不识别打标类型，不处理该数据【{}】", JSON.toJSONString(paidData));
                        break;
                }
            });

            if(!errodMsg.isEmpty()){
                List<String> errorMsg = errodMsg.subList(0,Math.min(10,errodMsg.size()));
                String errorMSG = String.join(",",errorMsg);
                rylCountSettlementFileConfigLogMapper.updateFileRareConfig(errorMSG,1,x);
            }
        });
        return null;
    }
    @Override
    public ReturnT<String> syncZjSettBillPaymentStatus(String s) {
        RylCountSettlementBillSyncVo rylCountSettlementBillSyncVo = StringUtils.isBlank(s) ? null : JSON.parseObject(s, RylCountSettlementBillSyncVo.class);
        String countNo = null;
        if(rylCountSettlementBillSyncVo!=null){
            countNo = rylCountSettlementBillSyncVo.getCountNo();
        }
        try {
            Date kpDate = WorkDayUtils.getBeforeWorkDate(new Date(), 5, "F");
            List<RylCountSettlementBillPayEntity> rylCountSettlementBillPayEntities = rylCountSettlementBillPayMapper.selectList(new QueryWrapper<RylCountSettlementBillPayEntity>()
                    .eq("bill_pay_status", 0)
                    .eq("examine_status", 2)
                    .eq("merchant_type", "1")
                    .le("examine_time", kpDate)
                    .eq(StringUtils.isNotEmpty(countNo),"count_no",countNo));
            if (!rylCountSettlementBillPayEntities.isEmpty()){
                List<ZjBillPayReqDto> zjBillPayReqDtoList = new ArrayList<>();
                List<ZjBillPayReqDto> zjReqDtoList = new ArrayList<>();
                for (RylCountSettlementBillPayEntity billPayEntity : rylCountSettlementBillPayEntities) {
                    QueryWrapper<RylCountSettlementChannelFinanceMappingCfgEntity> channelFinanceMappingCfgQueryWrapper = new QueryWrapper();
                    channelFinanceMappingCfgQueryWrapper.eq("channel_code",billPayEntity.getChannelCode());
                    if (StringUtils.isNotEmpty(billPayEntity.getManageCom())){
                        String[] split = StringUtil.split(billPayEntity.getManageCom(), "|");
                        List<String> list = Arrays.asList(split).stream()
                                .map(x -> x.substring(0, Math.min(x.length(), 4)))
                                .distinct()
                                .collect(Collectors.toList());
                        if (list.size()==1){
                            channelFinanceMappingCfgQueryWrapper.like("manage_com",list.get(0));
                        }else if (list.size()==2){
                            channelFinanceMappingCfgQueryWrapper.and(queryWrapper1 ->
                                    queryWrapper1.like("manage_com", list.get(0))
                                            .or(queryWrapper2 -> queryWrapper2.like("manage_com", list.get(1))));

                        }else if (list.size()==3){
                            channelFinanceMappingCfgQueryWrapper.and(queryWrapper1 ->
                                    queryWrapper1.like("manage_com", list.get(0))
                                            .or(queryWrapper2 -> queryWrapper2.like("manage_com", list.get(1)))
                                            .or(queryWrapper3 -> queryWrapper3.like("manage_com", list.get(2))));

                        }else {
                            channelFinanceMappingCfgQueryWrapper.and(queryWrapper1 ->
                                    queryWrapper1.like("manage_com", list.get(0))
                                            .or(queryWrapper2 -> queryWrapper2.like("manage_com", list.get(1)))
                                            .or(queryWrapper3 -> queryWrapper3.like("manage_com", list.get(2)))
                                            .or(queryWrapper4 -> queryWrapper4.like("manage_com", list.get(3))));
                        }
                    }
                    List<RylCountSettlementChannelFinanceMappingCfgEntity> rylCountSettlementChannelFinanceMappingCfgEntityList = rylCountSettlementChannelFinanceMappingCfgMapper.selectList(channelFinanceMappingCfgQueryWrapper);
                    RylCountSettlementChannelFinanceMappingCfgEntity channelFinanceMappingCfgEntity = rylCountSettlementChannelFinanceMappingCfgEntityList.get(0);
                    ZjBillPayReqDto zjBillPayReqDto = new ZjBillPayReqDto();
                    zjBillPayReqDto.setOtherAccountCode(channelFinanceMappingCfgEntity.getPaymentAccountNo());
                    zjBillPayReqDto.setOtherAccountName(channelFinanceMappingCfgEntity.getPaymentAccountNoName());
                    String amount = StringUtils.isBlank(billPayEntity.getCommissionChargeTotalAfter()) ? billPayEntity.getCommissionChargeTotal() : billPayEntity.getCommissionChargeTotalAfter();
                    zjBillPayReqDto.setAmount(amount);
                    ZjBillPayReqDto zjReq = new ZjBillPayReqDto();
                    BeanUtils.copyProperties(zjBillPayReqDto,zjReq);
                    zjReqDtoList.add(zjReq);
                    zjBillPayReqDto.setMessage(billPayEntity.getCountNo());
                    zjBillPayReqDtoList.add(zjBillPayReqDto);
                }
                if (zjBillPayReqDtoList.size()>0){
                    log.info("查询资金系统收款状态，请求参数：{}",JSONObject.toJSONString(zjBillPayReqDtoList));
                    ResponseEntity<JSONObject> responseEntity = externalRestTemplate.exchange(aysnZjPaymentStatus, HttpMethod.POST, new HttpEntity(zjReqDtoList), new ParameterizedTypeReference<JSONObject>(){});
                    log.info("查询资金系统收款状态，返回参数：{}",JSONObject.toJSONString(responseEntity));
                    if (responseEntity.getBody()!=null){
                        JSONArray data = responseEntity.getBody().getJSONArray("data");
                        log.info("data:{}",data);
                        List<ZjBillPayResDto> zjBillPayResDtoList = data.toJavaList(ZjBillPayResDto.class);
                        String yjCountNos = null;
                        for (ZjBillPayReqDto zjBillPayReqDto : zjBillPayReqDtoList) {
                            RylCountSettlementBillPayEntity rylCountSettlementBillPayEntity = rylCountSettlementBillPayMapper.selectOne(new QueryWrapper<RylCountSettlementBillPayEntity>()
                                    .eq("count_No", zjBillPayReqDto.getMessage()));
                            List<ZjBillPayResDto> zjBillPayResDtos = zjBillPayResDtoList.stream().filter(w -> zjBillPayReqDto.getOtherAccountCode().equals(w.getOtherAccountCode())
                                                    && zjBillPayReqDto.getOtherAccountName().equals(w.getOtherAccountName()) && ((double)Double.valueOf(zjBillPayReqDto.getAmount()) == (double)Double.valueOf(w.getAmount()))).collect(Collectors.toList());
                            if (zjBillPayResDtos!=null&&zjBillPayResDtos.size()==1){
                                rylCountSettlementBillPayEntity.setBillPayStatus("1");
                                rylCountSettlementBillPayMapper.updateById(rylCountSettlementBillPayEntity);
                                rylCountSettlementUploadPayMapper.updatePayStatus(rylCountSettlementBillPayEntity.getCountNo(),rylCountSettlementBillPayEntity.getChannelCode(),"1");
                                List<RylCountSettlementUploadPayEntity> billDetailList = rylCountSettlementUploadPayMapper.queryPolicyDetail(rylCountSettlementBillPayEntity.getCountNo(),rylCountSettlementBillPayEntity.getChannelCode());
                                if(billDetailList!=null && billDetailList.size()>0){
                                    rylCountSettResultMapper.updatePayStatusByResult(billDetailList,"1");
                                }
                                //非保单级 结算数据 支付结果回写
                                rylCountSettlementUploadVitalMapper.updatePayStatus(rylCountSettlementBillPayEntity.getCountNo(),rylCountSettlementBillPayEntity.getChannelCode(),"1");
                                List<RylCountSettBillDeailVitalDto> vitalDtoList = rylCountSettlementUploadVitalMapper.queryPolicyDetail(rylCountSettlementBillPayEntity.getCountNo(),rylCountSettlementBillPayEntity.getChannelCode());
                                if(vitalDtoList!=null && vitalDtoList.size()>0){
                                    rylCountSettResultVitalMapper.updatePayResult(vitalDtoList,"1");
                                }
                            }else {
                                Date examineTime = rylCountSettlementBillPayEntity.getExamineTime();
                                LocalDate now = LocalDate.now();
                                LocalDate date = examineTime.toInstant()
                                        .atZone(ZoneId.systemDefault())
                                        .toLocalDate();
                                long daysBetween = ChronoUnit.DAYS.between(date, now);
                                if (daysBetween>15 || zjBillPayResDtos.size()>1){
                                    if (yjCountNos==null){
                                        yjCountNos = zjBillPayReqDto.getMessage();
                                    }else {
                                        yjCountNos = yjCountNos +"｜" + zjBillPayReqDto.getMessage();
                                    }
                                }
                            }
                        }
                        if (yjCountNos!=null){
                            //企微预警
                            Map<String,String> map = new HashMap<>();
                            map.put("countNos",yjCountNos);
                            alertUtil.alert("N0021",map);
                        }
                    }
                }
            }
        }catch (Exception e){
            log.info("查询资金系统收款状态异常，原因：{}",e);
        }
        return null;
    }

    /**
     * 已付打标
     */
    private void handPaidData(RylCountSettlementResultHand paidData,List<String> errodMsg,SettleStatusReasonDTO statusReasons,List<RylCountSettlementChannelLabEntity> labEntityList) {
        RylCountSettlementResult sysSettResult = rylCountSettResultMapper.getSysCountResult(paidData);
        if(sysSettResult != null){
            sysSettResult.setCommissionCharge(paidData.getCommissionCharge());
            sysSettResult.setCountPayStatus(1);
            sysSettResult.setCountRemark("人工已结佣金;handID="+paidData.getHandId());
            sysSettResult.setSettleStatus("Y");
            sysSettResult.setSettleStatusReason(JSONObject.toJSONString(statusReasons));
            sysSettResult.setUpdateTime(new Date());
            rylCountSettResultMapper.updateById(sysSettResult);
        }else{
            RylCountSettlementInit sysSettInit = rylCountSettResultMapper.getSysCountInit(paidData);
            if(sysSettInit == null){
                paidData.setFlagDesc("已付打标缺失基础数据");
                rylCountSettResultHandMapper.updateById(paidData);
                log.error(paidData.getPolicyNo() + "_" + paidData.getOpCode() + "_" + paidData.getOpSubCode() + "已付打标缺失基础数据");
                String errorMessage = paidData.getPolicyNo() + "_" + paidData.getOpCode() + "_" + paidData.getOpSubCode() + "_" + paidData.getPayCount() + "已付打标缺失基础数据";
                errodMsg.add(errorMessage);
                return;
            }
            sysSettResult = new RylCountSettlementResult();
            BeanUtils.copyProperties(sysSettInit, sysSettResult);
            sysSettResult.setOpCode(paidData.getOpCode());
            sysSettResult.setOpSubCode(paidData.getOpSubCode());
            sysSettResult.setRateType(paidData.getRateType());
            sysSettResult.setPayCount(paidData.getPayCount());
            if(!StringUtils.isBlank(paidData.getRateSubType())){
                sysSettResult.setRateSubType(paidData.getRateSubType());
            }
            sysSettResult.setCommissionCharge(paidData.getCommissionCharge());
            sysSettResult.setCountPayStatus(1);
            sysSettResult.setCountRemark("人工已结佣金;handID="+paidData.getHandId());
            sysSettResult.setSettleStatus("Y");
            sysSettResult.setCalType("sys");
            sysSettResult.setHandId(0L);
            sysSettResult.setSettleStatusReason(JSONObject.toJSONString(statusReasons));
            sysSettResult.setSettlementDate(paidData.getSettlementDate());
            sysSettResult.setCreateTime(new Date());
            sysSettResult.setUpdateTime(new Date());
            String channelCode = sysSettResult.getChannelCode();
            List<RylCountSettlementChannelLabEntity> labEntities = labEntityList.stream().filter(x-> x.getChannelCode().equals(channelCode)).collect(Collectors.toList());
            if(!labEntities.isEmpty()){
                sysSettResult.setMerchantType(labEntities.get(0).getMerchantType());
            }else{
                QueryWrapper<RylCountSettlementChannelLabEntity> query= new QueryWrapper<>();
                query.eq("is_del",0);
                query.like("sub_channel_code",channelCode);
                RylCountSettlementChannelLabEntity rylCountSettlementChannelLabEntity = rylCountSettlementChannelLabMapper.selectOne(query);
                if (rylCountSettlementChannelLabEntity!=null){
                    sysSettResult.setMerchantType(rylCountSettlementChannelLabEntity.getMerchantType());
                }
            }
            rylCountSettResultMapper.insert(sysSettResult);
        }
        paidData.setFlag(1);
        paidData.setUpdateTime(new Date());
        paidData.setResultId(sysSettResult.getResultId());
        paidData.setCountId(sysSettResult.getCountId());
        rylCountSettResultHandMapper.updateById(paidData);
    }

    /**
     * 已付已追回
     */
    private void handBackToPayData(RylCountSettlementResultHand paidData,List<String> errodMsg,SettleStatusReasonDTO statusReasons) {
        RylCountSettlementResult sysSettResult = rylCountSettResultMapper.getSysCountResult(paidData);
        if(sysSettResult != null){
            if(!"1".equals(sysSettResult.getCountPayStatus().toString())){
                sysSettResult.setCountPayStatus(1);
                sysSettResult.setSettleStatus("Y");
                sysSettResult.setSettleStatusReason(JSONObject.toJSONString(statusReasons));
                rylCountSettResultMapper.updateById(sysSettResult);
            }

            RylCountSettlementResult newSettResult = ObjConverUtil.copy(sysSettResult, RylCountSettlementResult.class);
            newSettResult.setResultId(null);
            newSettResult.setCountRemark("人工已付已追回");
            newSettResult.setCreateTime(new Date());
            newSettResult.setUpdateTime(new Date());
            newSettResult.setCountPayStatus(1);
            newSettResult.setHandId(paidData.getHandId());
            newSettResult.setCountId(-sysSettResult.getCountId());
            newSettResult.setCalType("sysHand");
            newSettResult.setSettleStatus("Y");
            newSettResult.setCommissionCharge(paidData.getCommissionCharge());
            newSettResult.setSettleStatusReason(JSONObject.toJSONString(statusReasons));
            rylCountSettResultMapper.insert(newSettResult);
            paidData.setResultId(newSettResult.getResultId());
            paidData.setCountId(newSettResult.getCountId());

        }else{
            paidData.setFlagDesc("已付已追回缺失结算基础数据");
            rylCountSettResultHandMapper.updateById(paidData);
            log.error(paidData.getPolicyNo() + "_" + paidData.getOpCode() + "_" + paidData.getOpSubCode() + "已付已追回缺失结算基础数据");
            String errorMessage = paidData.getPolicyNo() + "_" + paidData.getOpCode() + "_" + paidData.getOpSubCode() + "_" + paidData.getPayCount() + "已付已追回缺失结算基础数据";
            errodMsg.add(errorMessage);
            return;
        }
        paidData.setFlag(1);
        paidData.setUpdateTime(new Date());
        rylCountSettResultHandMapper.updateById(paidData);
    }

    /**
     * 已付要追回
     */
    private void handNeedBackPayData(RylCountSettlementResultHand paidData,List<String> errodMsg,SettleStatusReasonDTO statusReasons,List<RylCountSettlementChannelLabEntity> labEntityList) {
        RylCountSettlementResult sysSettResult = rylCountSettResultMapper.getSysCountResult(paidData);
        if(sysSettResult != null){
            if(!"1".equals(sysSettResult.getCountPayStatus().toString())){
                sysSettResult.setCountPayStatus(1);
                sysSettResult.setSettleStatus("Y");
                sysSettResult.setSettleStatusReason(JSONObject.toJSONString(statusReasons));
                rylCountSettResultMapper.updateById(sysSettResult);
            }
            RylCountSettlementResult writeOffResult = ObjConverUtil.copy(sysSettResult, RylCountSettlementResult.class);
            writeOffResult.setResultId(null);
            writeOffResult.setCountPayStatus(0);
            writeOffResult.setCountId(-sysSettResult.getCountId());
            writeOffResult.setHandId(paidData.getHandId());
            writeOffResult.setCountRemark(paidData.getRemark());
            writeOffResult.setCreateTime(new Date());
            writeOffResult.setUpdateTime(new Date());
            writeOffResult.setCalType("sysHand");
            writeOffResult.setSettleStatus("Y");
            writeOffResult.setSettleStatusReason(JSONObject.toJSONString(statusReasons));
            writeOffResult.setCommissionCharge(paidData.getCommissionCharge());
            writeOffResult.setSettlementDate(paidData.getSettlementDate());
            rylCountSettResultMapper.insert(writeOffResult);
        }else{
            RylCountSettlementInit sysSettInit = rylCountSettResultMapper.getSysCountInit(paidData);
            if (sysSettInit == null) {
                paidData.setFlagDesc("已付要追回缺失基础数据");
                rylCountSettResultHandMapper.updateById(paidData);
                log.error(paidData.getPolicyNo() + "_" + paidData.getOpCode() + "_" + paidData.getOpSubCode() + "已付要追回缺失基础数据");
                String errorMessage = paidData.getPolicyNo() + "_" + paidData.getOpCode() + "_" + paidData.getOpSubCode() + "_" + paidData.getPayCount() + "已付要追回缺失基础数据";
                errodMsg.add(errorMessage);
                return;
            }
            sysSettResult = new RylCountSettlementResult();
            BeanUtils.copyProperties(sysSettInit, sysSettResult);
            sysSettResult.setOpCode(paidData.getOpCode());
            sysSettResult.setOpSubCode(paidData.getOpSubCode());
            sysSettResult.setRateType(paidData.getRateType());
            if(!StringUtils.isBlank(paidData.getRateSubType())){
                sysSettResult.setRateSubType(paidData.getRateSubType());
            }
            sysSettResult.setCommissionCharge(paidData.getCommissionCharge());
            sysSettResult.setCountPayStatus(0);
            sysSettResult.setCountRemark(StringUtils.isBlank(paidData.getRemark()) ? "人工已付要追佣金" : paidData.getRemark());
            sysSettResult.setCalType("hand");
            sysSettResult.setSettleStatus("Y");
            sysSettResult.setSettleStatusReason(JSONObject.toJSONString(statusReasons));
            sysSettResult.setHandId(paidData.getHandId());
            sysSettResult.setSettlementDate(paidData.getSettlementDate());
            sysSettResult.setCreateTime(new Date());
            sysSettResult.setUpdateTime(new Date());
            String channelCode = sysSettResult.getChannelCode();
            List<RylCountSettlementChannelLabEntity> labEntities = labEntityList.stream().filter(x-> x.getChannelCode().equals(channelCode)).collect(Collectors.toList());
            if(!labEntities.isEmpty()){
                sysSettResult.setMerchantType(labEntities.get(0).getMerchantType());
            }else{
                QueryWrapper<RylCountSettlementChannelLabEntity> query= new QueryWrapper<>();
                query.eq("is_del",0);
                query.like("sub_channel_code",channelCode);
                RylCountSettlementChannelLabEntity rylCountSettlementChannelLabEntity = rylCountSettlementChannelLabMapper.selectOne(query);
                if (rylCountSettlementChannelLabEntity!=null){
                    sysSettResult.setMerchantType(rylCountSettlementChannelLabEntity.getMerchantType());
                }
            }
            rylCountSettResultMapper.insert(sysSettResult);
        }
        paidData.setFlag(1);
        paidData.setUpdateTime(new Date());
        paidData.setResultId(sysSettResult.getResultId());
        paidData.setCountId(sysSettResult.getCountId());
        rylCountSettResultHandMapper.updateById(paidData);
    }

    /**
     * 补结
     *
     * @param paidData
     */
    private void handNeedToPay(RylCountSettlementResultHand paidData,List<String> errodMsg,SettleStatusReasonDTO statusReasons,List<RylCountSettlementChannelLabEntity> labEntityList) {
        RylCountSettlementResult sysSettResult = rylCountSettResultMapper.getSysCountResult(paidData);
        if(sysSettResult != null){
            RylCountSettlementResult toPayResult = ObjConverUtil.copy(sysSettResult, RylCountSettlementResult.class);
            toPayResult.setCommissionCharge(paidData.getCommissionCharge());
            toPayResult.setCountPayStatus(0);
            toPayResult.setCountId(-sysSettResult.getCountId());
            toPayResult.setHandId(paidData.getHandId());
            toPayResult.setCountRemark(paidData.getRemark());
            if(StringUtils.isBlank(paidData.getRemark())){
                toPayResult.setCountRemark("补结打标生成结算数据");
            }
            toPayResult.setCreateTime(new Date());
            toPayResult.setUpdateTime(new Date());
            toPayResult.setCalType("sysHand");
            sysSettResult.setSettleStatus("Y");
            sysSettResult.setSettleStatusReason(JSONObject.toJSONString(statusReasons));
            toPayResult.setSettlementDate(paidData.getSettlementDate());
            rylCountSettResultMapper.insert(toPayResult);
        }else{
            RylCountSettlementInit sysSettInit = rylCountSettResultMapper.getSysCountInit(paidData);
            if (sysSettInit == null) {
                paidData.setFlagDesc("补结打标缺失基础数据");
                rylCountSettResultHandMapper.updateById(paidData);
                log.error(paidData.getPolicyNo() + "_" + paidData.getOpCode() + "_" + paidData.getOpSubCode() + "补结打标缺失基础数据");
                String errorMessage = paidData.getPolicyNo() + "_" + paidData.getOpCode() + "_" + paidData.getOpSubCode() + "_" + paidData.getPayCount() + "补结打标缺失基础数据";
                errodMsg.add(errorMessage);
                return;
            }
            sysSettResult = new RylCountSettlementResult();
            BeanUtils.copyProperties(sysSettInit, sysSettResult);
            sysSettResult.setOpCode(paidData.getOpCode());
            sysSettResult.setOpSubCode(paidData.getOpSubCode());
            sysSettResult.setRateType(paidData.getRateType());
            if(!StringUtils.isBlank(paidData.getRateSubType())){
                sysSettResult.setRateSubType(paidData.getRateSubType());
            }
            sysSettResult.setCommissionCharge(paidData.getCommissionCharge());
            sysSettResult.setCountPayStatus(0);
            sysSettResult.setCountRemark(StringUtils.isBlank(paidData.getRemark()) ? "人工计算佣金" : paidData.getRemark());
            sysSettResult.setCalType("hand");
            sysSettResult.setSettleStatus("Y");
            sysSettResult.setSettleStatusReason(JSONObject.toJSONString(statusReasons));
            sysSettResult.setHandId(paidData.getHandId());
            sysSettResult.setSettlementDate(paidData.getSettlementDate());
            sysSettResult.setCreateTime(new Date());
            sysSettResult.setUpdateTime(new Date());
            String channelCode = sysSettResult.getChannelCode();
            List<RylCountSettlementChannelLabEntity> labEntities = labEntityList.stream().filter(x-> x.getChannelCode().equals(channelCode)).collect(Collectors.toList());
            if(!labEntities.isEmpty()){
                sysSettResult.setMerchantType(labEntities.get(0).getMerchantType());
            }else{
                QueryWrapper<RylCountSettlementChannelLabEntity> query= new QueryWrapper<>();
                query.eq("is_del",0);
                query.like("sub_channel_code",channelCode);
                RylCountSettlementChannelLabEntity rylCountSettlementChannelLabEntity = rylCountSettlementChannelLabMapper.selectOne(query);
                if (rylCountSettlementChannelLabEntity!=null){
                    sysSettResult.setMerchantType(rylCountSettlementChannelLabEntity.getMerchantType());
                }
            }
            rylCountSettResultMapper.insert(sysSettResult);
        }
        paidData.setFlag(1);
        paidData.setUpdateTime(new Date());
        paidData.setResultId(sysSettResult.getResultId());
        paidData.setCountId(sysSettResult.getCountId());
        rylCountSettResultHandMapper.updateById(paidData);
    }

    /**
     * 推费控 结算单信息校验
     *
     * @param billPay
     * @return
     */
    private String checkMsgToFk(RylCountSettlementBillPayEntity billPay) {
        String countNo = billPay.getCountNo();
        String errorMsg = "";
        if (billPay.getUserFkUuid() == null) {
            return "结算单号" + countNo + ",无提交人信息，暂不同步";
        }
        try {
            /**
             * 2022-09-26 新增 推费控逐单检查
             * 保单级不能重复结(重复推费控，通过整体调差新增的非结算单里的保单，卡不住)
             */
            List<String> countNoList = rylCountSettlementBillPayMapper.queryFkCountNos(billPay.getChannelCode());
            if (countNoList == null || countNoList.size() <= 0) {
                return errorMsg;
            }
            //已经推费控的保单明细
            List<FkPolicyVo> fkPolicyList = rylCountSettlementUploadPayMapper.queryFkPolicys(countNoList);

            List<String> countNoToFk = new ArrayList<>();
            countNoToFk.add(countNo);
            //正要推费控的保单明细
            List<FkPolicyVo> newPolicyList = rylCountSettlementUploadPayMapper.queryFkPolicys(countNoToFk);


            for (FkPolicyVo toFkPolicy : newPolicyList) {
                FkPolicyVo uploadPayEntity = fkPolicyList.stream().filter(fkPolicy -> fkPolicy.getPolicyNo().equals(toFkPolicy.getPolicyNo())).findAny().orElse(null);
                if (uploadPayEntity != null && toFkPolicy.getOpCode().equals(uploadPayEntity.getOpCode()) &&
                        toFkPolicy.getOpSubCode().equals(uploadPayEntity.getOpSubCode()) &&
                        toFkPolicy.getRateType().equals(uploadPayEntity.getRateType()) &&
                        toFkPolicy.getRateSubType().equals(uploadPayEntity.getRateSubType()) &&
                        toFkPolicy.getPayCount().equals(uploadPayEntity.getPayCount()) &&
                        !"Pause".equals(uploadPayEntity.getSettleStatus())) {
                    if ("Y".equals(uploadPayEntity.getSettleStatus())) {
                        QueryWrapper<RylCountSettlementBillDetailPayEntity> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("count_no", countNo);
                        queryWrapper.eq("policy_no", toFkPolicy.getPolicyNo());
                        queryWrapper.eq("op_code", toFkPolicy.getOpCode());
                        queryWrapper.eq("op_sub_code", toFkPolicy.getOpSubCode());
                        queryWrapper.eq("pay_count", toFkPolicy.getPayCount());
                        queryWrapper.eq("rate_sub_type", toFkPolicy.getRateSubType());
                        List<RylCountSettlementBillDetailPayEntity> rylCountSettlementBillDetailPayEntities = rylCountSettlementBillDetailPayMapper.selectList(queryWrapper);
                        if (!rylCountSettlementBillDetailPayEntities.isEmpty()) {
                            RylCountSettlementBillDetailPayEntity rylCountSettlementBillDetailPayEntity = rylCountSettlementBillDetailPayEntities.get(0);
                            if (null != rylCountSettlementBillDetailPayEntity.getCommissionChargeTotalAfter() && Double.valueOf(rylCountSettlementBillDetailPayEntity.getCommissionChargeTotalAfter()) != 0) {
                                break;
                            }
                        }
                    }
                    if (toFkPolicy.getResultId().equals(uploadPayEntity.getResultId())) {
                        errorMsg = toFkPolicy.getPolicyNo() + "—" + toFkPolicy.getResultId() + "不允许重复推费控";
                        break;
                    }
                }
            }

        } catch (Exception e) {
            log.error("推费控结算单信息校验,系统异常，原因：", e);
            errorMsg = "结算单推送费控信息校验时,系统异常，请联系IT处理";
        }
        return errorMsg;
    }

    /**
     * 处理 待推送费控的结算单
     * 更新费控账号以及代付标识
     *
     * @param billPayList
     * @return 返回通过校验的结算单号列表
     */
    private List<String> handCountNoToFk(List<RylCountSettlementBillPayEntity> billPayList, Map<String, Map<String, String>> map) {
        List<String> validCountNos = new ArrayList<>();
        for(RylCountSettlementBillPayEntity billPay : billPayList){
            String settBouncers = null;
            Map<String,String> billMap = new HashMap<>();
            String channelName = billPay.getChannelName();
            Integer isDoubleSign = 0;
            RylCountSettlementChannelCfgEntity channelCfgEntity = null;
            /**
             * 协议主体 默认：与弘康签；部分渠道：与融汇签
             *    目前：上海、河南分公司，均只能与弘康签；但江苏 可以跟融汇、弘康签
             *    2022-08-11 支持双签 （由结算人员，自由选择渠道推费控的账户信息；双签渠道，8641  8631 是弘康，8611  8632 是融汇）
             *  -----》
             *  2023-06-09 改为：根据结算主体推送费控(费控账户信息不再可选)
             */
            try {
                channelCfgEntity = rylCountSettlementChannelCfgMapper.queryChannelNameByCode(billPay.getChannelCode());
                settBouncers = channelCfgEntity.getSettleBouncers();
                billMap.put("depCode",channelCfgEntity.getDepCode());
                if (StringUtils.isBlank(channelName)) {
                    channelName = channelCfgEntity.getChannelName();
                    billPay.setChannelName(channelName);
                }
                isDoubleSign = channelCfgEntity.getIsDoubleSign();
                //默认按渠道配置的代付标识 打标结算单
                billPay.setIsPayBehalf(channelCfgEntity.getIsPayBehalf());
                rylCountSettlementBillPayMapper.updateById(billPay);
            } catch (Exception e) {
                log.error("根据渠道码{}查渠道配置，系统异常，原因：", billPay.getChannelCode(), e);
            }
            if (billPay.getUserFkUuid() == null) {
                /**
                 * 结算业务全部移交总部，分公司结算权限关闭；故而结算单推送费控不再依赖融汇核心系统当前登录人；
                 * （且前端的费控账户选择框已去掉）
                 * 推送费控的账号：根据渠道配置的结算主体
                 */
                if (channelCfgEntity != null) {
                    if ((!SettlementConstant.SETT_BOUNCERS_HK.equals(channelCfgEntity.getSettleBouncers())) && (!SettlementConstant.SETT_BOUNCERS_RH.equals(channelCfgEntity.getSettleBouncers()))) {
                        billPay.setAsynFkFlag(2);
                        billPay.setAsynFkDesc("该渠道尚未配置结算主体，无法推送费控");
                        rylCountSettlementBillPayMapper.updateBillToFkResult(billPay);
                        log.info("结算单号{}校验失败：该渠道尚未配置结算主体，跳过处理", billPay.getCountNo());
                        continue;
                    }

                    List<String> fkUnitNames = rylCountSettlementBillMappingCfgMapper.getFkUnitName(channelCfgEntity.getSettleBouncersName().trim());
                    if (fkUnitNames.isEmpty()) {
                        billPay.setAsynFkFlag(2);
                        billPay.setAsynFkDesc("该渠道尚未配置结算主体，无法推送费控");
                        rylCountSettlementBillPayMapper.updateBillToFkResult(billPay);
                        log.info("结算单号{}校验失败：该渠道尚未配置结算主体，跳过处理", billPay.getCountNo());
                        continue;
                    }
                    if (fkUnitNames.size() > 1) {
                        billPay.setAsynFkFlag(2);
                        billPay.setAsynFkDesc("该渠道结算主体下有多个部门的费控账号，无法推送费控");
                        rylCountSettlementBillPayMapper.updateBillToFkResult(billPay);
                        log.info("结算单号{}校验失败：该渠道结算主体下有多个部门的费控账号，跳过处理", billPay.getCountNo());
                        continue;
                    }
                    List<RylUserFkDataDto> rylUserFkDataDto = rylCountSettlementBillMappingCfgMapper.getBillUserInfos(fkUnitNames.get(0).trim());
                    if (rylUserFkDataDto.isEmpty() || rylUserFkDataDto.size() > 1) {
                        billPay.setAsynFkFlag(2);
                        billPay.setAsynFkDesc("该渠道结算主体下费控账号(张营营)异常，无法推送费控");
                        rylCountSettlementBillPayMapper.updateBillToFkResult(billPay);
                        log.info("结算单号{}校验失败：该渠道结算主体下费控账号异常，跳过处理", billPay.getCountNo());
                        continue;
                    }
                    billPay.setUserFkUuid(rylUserFkDataDto.get(0).getUuid());
                    rylCountSettlementBillPayMapper.updateById(billPay);
                }
            }
            /*
             * 针对双签、且代付标识的渠道，分管理机构 给结算单打标代付标识
             *   上海、河南：走代付 (1)
             *   北京、江苏：走非代付 (0)
             */
            if (SettlementConstant.SETT_DOUBLESIGN.equals(isDoubleSign) && SettlementConstant.SETT_IS_PAYBEHALF.equals(billPay.getIsPayBehalf())) {
                List<String> manageList = rylCountSettlementUploadPayMapper.selectCountManages(billPay.getCountNo());
                if (manageList != null && manageList.size() > 2) {
                    billPay.setAsynFkFlag(2);
                    billPay.setAsynFkDesc("双签代付渠道，结算单内代付与非代付单不可混合结算");
                    rylCountSettlementBillPayMapper.updateBillToFkResult(billPay);
                    log.info("结算单号{}校验失败：双签代付渠道管理机构过多，跳过处理", billPay.getCountNo());
                    continue;
                } else if (manageList.size() == 2 && !(manageList.containsAll(Arrays.asList("8611", "8632")) || manageList.containsAll(Arrays.asList("8631", "8641")))) {
                    billPay.setAsynFkFlag(2);
                    billPay.setAsynFkDesc("双签代付渠道，结算单内代付与非代付单不可混合结算");
                    rylCountSettlementBillPayMapper.updateBillToFkResult(billPay);
                    log.info("结算单号{}校验失败：双签代付渠道管理机构组合不正确，跳过处理", billPay.getCountNo());
                    continue;
                }
                manageList.forEach(manageCom -> {
                    if ("8611".equals(manageCom) || "8632".equals(manageCom)) {
                        billPay.setIsPayBehalf(SettlementConstant.SETT_NOT_PAYBEHALF);
                        rylCountSettlementBillPayMapper.updateById(billPay);
                    }
                });
            }
            //双签非代付 8641  8631 是弘康，8611  8632 是融汇
            if (SettlementConstant.SETT_DOUBLESIGN.equals(isDoubleSign) && SettlementConstant.SETT_NOT_PAYBEHALF.equals(billPay.getIsPayBehalf())){
                List<String> manageList = rylCountSettlementUploadPayMapper.selectCountManages(billPay.getCountNo());
                if (manageList != null){
                    if (manageList.size()==1){
                        if (manageList.containsAll(Arrays.asList("8611")) || manageList.containsAll(Arrays.asList("8632"))){
                            settBouncers="2";
                        }else if (manageList.containsAll(Arrays.asList("8631")) || manageList.containsAll(Arrays.asList("8641"))){
                            settBouncers="1";
                        }
                    }else if(manageList.size()==2){
                        if (manageList.containsAll(Arrays.asList("8611", "8632"))){
                            settBouncers="2";
                        }else if (manageList.containsAll(Arrays.asList("8631", "8641"))){
                            settBouncers="1";
                        }
                    }
                }
            }
            /**
             * 如果结算单是融汇代付，但结算主体是弘康，则强制更新：费控账号为融汇结算主体下的账号
             */
            if (SettlementConstant.SETT_IS_PAYBEHALF.equals(billPay.getIsPayBehalf())) {
                log.info("结算单融汇代付，费控账号强制改为融汇结算主体下的账号，逻辑处理开始");
                billPay.setUserFkUuid(Integer.valueOf(rhUuid));
                rylCountSettlementBillPayMapper.updateById(billPay);
                log.info("结算单融汇代付，费控账号强制改为融汇结算主体下的账号，逻辑处理结束");
            }
            billMap.put("settBouncers",settBouncers);
            map.put(billPay.getCountNo(),billMap);
            // 校验通过的结算单添加到返回列表
            validCountNos.add(billPay.getCountNo());
        }
        log.info("结算单校验完成，总数：{}，通过校验：{}，失败：{}",
                billPayList.size(), validCountNos.size(), billPayList.size() - validCountNos.size());
        return validCountNos;
    }

    /**
     * 根据管理机构查询推送费控核算部门编码
     *
     * @param manageCom
     */
    private String queryFkLineUnitCode(String depCode,String billType,String manageCom,String relationshipLine) {
        if (StringUtils.isNotEmpty(manageCom)){
            QueryWrapper<RylCountSettlementBillDeptCfgEntity> queryWrapper = new QueryWrapper();
            queryWrapper.eq("manage_com",manageCom);
            queryWrapper.eq("relationship_line",relationshipLine);
            queryWrapper.eq("dep_code",depCode);
            queryWrapper.eq("bill_type",billType);
            List<RylCountSettlementBillDeptCfgEntity> rylCountSettlementBillDeptCfgEntityList = rylCountSettlementBillDeptCfgMapper.selectList(queryWrapper);
            if (rylCountSettlementBillDeptCfgEntityList.isEmpty()&&rylCountSettlementBillDeptCfgEntityList.size()==1){
                return rylCountSettlementBillDeptCfgEntityList.get(0).getFkLineUnitCode();
            }
        }
        return null;
    }

    /**
     * 组装费控接口请求报文
     *
     * @param billPay
     * @param billMappingCfgEntity
     * @param billType             费用类型
     * @return
     */
    private String dealFkReqContent(RylCountSettlementBillPayEntity billPay,
                                    RylCountSettlementBillMappingCfgEntity billMappingCfgEntity,
                                    String billType,
                                    List<RylCountSettlementBillTempCfgEntity> rylCountSettlementBillTempCfgEntityList,
                                    List<RylCountSettlementChannelFinanceMappingCfgEntity> rylCountSettlementChannelFinanceMappingCfgEntityList,
                                    String settBouncers,
                                    String deptCode,
                                    String channelType) throws Exception {
        PostFkDataNew postFkDataNew = new PostFkDataNew();
        List<SettBillToFkVoNew> settBillToFkVoNewList = new ArrayList<>();
        SettBillToFkVoNew settBillToFkVoNew = new SettBillToFkVoNew();
        //MAIN区
        List<SettBillToFkMainVo> settBillToFkMainVoList = new ArrayList<>();
        SettBillToFkMainVo settBillToFkMainVo = new SettBillToFkMainVo();
        settBillToFkMainVo.setSourceBatchID(numberServiceUtil.createFkNumber());
        settBillToFkMainVo.setRecordId(numberServiceUtil.createFkInterId());
        settBillToFkMainVo.setBillType(billType);
        settBillToFkMainVo.setSourceSys("融汇核心");
        if (channelType.equals("3")){
            settBillToFkMainVo.setChannelType("3");
        }else {
            settBillToFkMainVo.setChannelType("4");
        }
        settBillToFkMainVo.setCompanyCode(billMappingCfgEntity.getFkCompanyCode());
        settBillToFkMainVo.setPouCode("0");
        settBillToFkMainVo.setUserCode(billMappingCfgEntity.getFkUserCode());
        settBillToFkMainVo.setUnitCode(billMappingCfgEntity.getFkUnitCode());
        settBillToFkMainVo.setPositionCode(billMappingCfgEntity.getFkPositionCode());
        settBillToFkVoNew.setMain(settBillToFkMainVoList);
        String countDatePeriod = SettlementUtil.dealSettDate(billPay.getCountStartTime(), billPay.getCountEndTime());
        log.info("结算期间：{}", countDatePeriod);
        String amount = StringUtils.isBlank(billPay.getCommissionChargeTotalAfter()) ? billPay.getCommissionChargeTotal() : billPay.getCommissionChargeTotalAfter();
        String channelName = billPay.getChannelName();
        String desc = null;
        if("4".equals(channelType)){
            desc = (StringUtils.isBlank(channelName) ? "渠道【" + billPay.getChannelCode() + "】" : channelName) + countDatePeriod + "结算（单号：" + billPay.getCountNo() + "）服务费汇总金额：" + amount + "元";
        }else{
            desc = (StringUtils.isBlank(channelName) ? "渠道【" + billPay.getChannelCode() + "】" : channelName) + countDatePeriod + "结算（单号：" + billPay.getCountNo() + "）手续费汇总金额：" + amount + "元";
        }
        try {
            if (StringUtils.isNotBlank(billPay.getCommissionChargeTotalAfter())) {
                String commisionChargeTotal = billPay.getCommissionChargeTotal();
                String commisionChargeToTalAfter = billPay.getCommissionChargeTotalAfter();
                desc = desc + "，调差金额：" + new BigDecimal(commisionChargeTotal).subtract(new BigDecimal(commisionChargeToTalAfter)) + "元";
            }
        } catch (Exception e) {
            log.error("结算单" + billPay.getCountNo() + "推送费控，解析调差金额异常，原因：", e);
        }
        //优化新接口
        settBillToFkMainVo.setOriginalCurrencySum(StringUtils.isBlank(amount)?"0":amount);
        settBillToFkMainVo.setDescription(desc);
        settBillToFkMainVoList.add(settBillToFkMainVo);
        //明细区
        List<SettBillToFkDetailVo> settBillToFkDetailVoList = new ArrayList<>();
        SettBillToFkDetailVo settBillToFkDetailVo = new SettBillToFkDetailVo();
        RylCountSettlementBillTempCfgEntity fieldValue;
        if (billType.equals(SettlementConstant.FK_BILLTYPE_SXFHKF) || billType.equals(SettlementConstant.FK_BILLTYPE_SXFDF) || billType.equals(SettlementConstant.FK_BILLTYPE_SXFYTJK)){
            fieldValue = rylCountSettlementBillTempCfgEntityList.stream().filter(x -> 1 == x.getStatus() && "DETAIL".equals(x.getBillArea()) && "BUDGET_UNIT_CODE".equals(x.getBillField())).findAny().orElse(null);
            settBillToFkDetailVo.setBudgetUnitCode(fieldValue.getBillFieldValue());
        }else {
            if (settBouncers!=null) {
                if (settBouncers.equals("1")) {
                    settBillToFkDetailVo.setBudgetUnitCode("01000030000");
                } else {
                    settBillToFkDetailVo.setBudgetUnitCode("02000020009");
                }
            }
        }
        fieldValue = rylCountSettlementBillTempCfgEntityList.stream().filter(x -> 1 == x.getStatus() && "DETAIL".equals(x.getBillArea()) && "SUBJECT_CODE".equals(x.getBillField())).findAny().orElse(null);
        settBillToFkDetailVo.setSubjectCode(fieldValue.getBillFieldValue());
        fieldValue = rylCountSettlementBillTempCfgEntityList.stream().filter(x -> 1 == x.getStatus() && "DETAIL".equals(x.getBillArea()) && "PROJECT_CODE".equals(x.getBillField())).findAny().orElse(null);
        settBillToFkDetailVo.setProjectCode(fieldValue.getBillFieldValue());
        if (billType.equals(SettlementConstant.FK_BILLTYPE_SXFDF)){
            fieldValue = rylCountSettlementBillTempCfgEntityList.stream().filter(x -> 1 == x.getStatus() && "DETAIL".equals(x.getBillArea()) && "PAY_APPROVE_TYPE".equals(x.getBillField())).findAny().orElse(null);
            settBillToFkDetailVo.setPayApproveType(fieldValue.getBillFieldValue());
        }
        fieldValue = rylCountSettlementBillTempCfgEntityList.stream().filter(x -> 1 == x.getStatus() && "DETAIL".equals(x.getBillArea()) && "INVOICE_TYPE".equals(x.getBillField())).findAny().orElse(null);
        settBillToFkDetailVo.setInvoiceType(fieldValue.getBillFieldValue());
        settBillToFkDetailVo.setOriginalCurrencySum(StringUtils.isBlank(amount)?"0":amount);
        //分摊区
        List<SettBillToFkExpenseVo> settBillToFkExpenseVoList = new ArrayList<>();
        //付款区
        List<SettBillToFkPaymentVo> settBillToFkPaymentVoList = new ArrayList<>();
        //核算部门保持原值，后期优化
        String fkLineUnitCode = queryFkLineUnitCode(deptCode,billType,billPay.getManageCom(), settBouncers);
        BigDecimal chargeTotal = new BigDecimal(amount);
        BigDecimal total = null;
        List<RylCountSettlementResult> settlementResultList = null;
        if (channelType.equals("3")){
            List<String> resultIdList = rylCountSettlementUploadPayMapper.selectResultIdList(billPay.getCountNo());
            settlementResultList = rylCountSettlementResultMapper.selectList(new QueryWrapper<RylCountSettlementResult>().in("result_id", resultIdList));
        }
        for (RylCountSettlementChannelFinanceMappingCfgEntity rylCountSettlementChannelFinanceMappingCfgEntity : rylCountSettlementChannelFinanceMappingCfgEntityList) {
            SettBillToFkExpenseVo settBillToFkExpenseVo = new SettBillToFkExpenseVo();
            if (channelType.equals("3")){
                //银保手续费
                //根据推送费控信息循环匹配满足条件保单
                List<String> policyNoList;
                if (StringUtils.isNotEmpty(rylCountSettlementChannelFinanceMappingCfgEntity.getAttribution())){
                    policyNoList = settlementResultList.stream().filter(x -> rylCountSettlementChannelFinanceMappingCfgEntity.getManageCom().contains(x.getManageCom().substring(0,4))
                            && StringUtils.isNotEmpty(x.getAgentComAttr()) && rylCountSettlementChannelFinanceMappingCfgEntity.getAttribution().contains(x.getAgentComAttr()))
                            .map(RylCountSettlementResult::getPolicyNo).collect(Collectors.toList());
                }else {
                    policyNoList = settlementResultList.stream().filter(x -> rylCountSettlementChannelFinanceMappingCfgEntity.getManageCom().contains(x.getManageCom().substring(0,4))
                            && StringUtils.isEmpty(x.getAgentComAttr()))
                            .map(RylCountSettlementResult::getPolicyNo).collect(Collectors.toList());
                }
                BigDecimal bigDecimal;
                if (policyNoList.isEmpty()){
                    bigDecimal = BigDecimal.ZERO;
                }else {
                    bigDecimal = rylCountSettlementBillDetailPayMapper.queryAfterSumCharge(billPay.getCountNo(),policyNoList);
                }
                if (total==null){
                    total=bigDecimal;
                }else {
                    total=total.add(bigDecimal);
                }
                settBillToFkExpenseVo.setOriginalCurrencySum(bigDecimal.toString());
            }else {
                settBillToFkExpenseVo.setOriginalCurrencySum(StringUtils.isBlank(amount)?"0":amount);
            }
            if (fkLineUnitCode!=null){
                settBillToFkExpenseVo.setLineUnitCode(fkLineUnitCode);
            }else {
                settBillToFkExpenseVo.setLineUnitCode(billMappingCfgEntity.getFkLineUnitCode());
            }
            fieldValue = rylCountSettlementBillTempCfgEntityList.stream().filter(x -> 1 == x.getStatus() && "EXPENSE".equals(x.getBillArea()) && "BXSUBJECT_CODE".equals(x.getBillField())).findAny().orElse(null);
            settBillToFkExpenseVo.setBxSubjectCode(fieldValue.getBillFieldValue());
            if (billType.equals(SettlementConstant.FK_BILLTYPE_SXFHKF) || billType.equals(SettlementConstant.FK_BILLTYPE_SXFDF) || billType.equals(SettlementConstant.FK_BILLTYPE_SXFYTJK)){
                fieldValue = rylCountSettlementBillTempCfgEntityList.stream().filter(x -> 1 == x.getStatus() && "EXPENSE".equals(x.getBillArea()) && "PRODUCT_CODE".equals(x.getBillField())).findAny().orElse(null);
                settBillToFkExpenseVo.setProductCode(fieldValue.getBillFieldValue());
            }else {
                if (settBouncers!=null) {
                    if (settBouncers.equals("1")) {
                        settBillToFkExpenseVo.setProductCode("000");
                    } else {
                        settBillToFkExpenseVo.setProductCode("000000");
                    }
                }
            }
            //渠道
            if (StringUtils.isNotEmpty(rylCountSettlementChannelFinanceMappingCfgEntity.getFinanceChannelCode())){
                settBillToFkExpenseVo.setChannelCode(rylCountSettlementChannelFinanceMappingCfgEntity.getFinanceChannelCode());
            }else {
                fieldValue = rylCountSettlementBillTempCfgEntityList.stream().filter(x -> 1 == x.getStatus() && "EXPENSE".equals(x.getBillArea()) && "CHANNEL_CODE".equals(x.getBillField())).findAny().orElse(null);
                settBillToFkExpenseVo.setChannelCode(fieldValue.getBillFieldValue());
            }
            //销售方税号
            settBillToFkExpenseVo.setExpenseSellerPayerNum(rylCountSettlementChannelFinanceMappingCfgEntity.getExpenseSellerPayerNum());
            fieldValue = rylCountSettlementBillTempCfgEntityList.stream().filter(x -> 1 == x.getStatus() && "EXPENSE".equals(x.getBillArea()) && "ACCOUNT_CODE".equals(x.getBillField())).findAny().orElse(null);
            settBillToFkExpenseVo.setAccountCode(fieldValue.getBillFieldValue());
            fieldValue = rylCountSettlementBillTempCfgEntityList.stream().filter(x -> 1 == x.getStatus() && "EXPENSE".equals(x.getBillArea()) && "IS_DEDUCTIBLE".equals(x.getBillField())).findAny().orElse(null);
            settBillToFkExpenseVo.setIsDeductible(fieldValue.getBillFieldValue());
            fieldValue = rylCountSettlementBillTempCfgEntityList.stream().filter(x -> 1 == x.getStatus() && "EXPENSE".equals(x.getBillArea()) && "EXPENSE_TAXRATE".equals(x.getBillField())).findAny().orElse(null);
            settBillToFkExpenseVo.setExpenseTaxRate(fieldValue.getBillFieldValue());
            fieldValue = rylCountSettlementBillTempCfgEntityList.stream().filter(x -> 1 == x.getStatus() && "EXPENSE".equals(x.getBillArea()) && "EXPENSE_STRUCTUREDETAIL".equals(x.getBillField())).findAny().orElse(null);
            settBillToFkExpenseVo.setExpenseStructureDetail(fieldValue.getBillFieldValue());
            settBillToFkExpenseVoList.add(settBillToFkExpenseVo);
            settBillToFkDetailVo.setExpense(settBillToFkExpenseVoList);

            //付款区
            SettBillToFkPaymentVo settBillToFkPaymentVo = new SettBillToFkPaymentVo();
            settBillToFkPaymentVo.setAccountNo(rylCountSettlementChannelFinanceMappingCfgEntity.getAccountNo());
            fieldValue = rylCountSettlementBillTempCfgEntityList.stream().filter(x -> 1 == x.getStatus() && "PAYMENT".equals(x.getBillArea()) && "PAY_METHOD".equals(x.getBillField())).findAny().orElse(null);
            settBillToFkPaymentVo.setPayMethod(fieldValue.getBillFieldValue());
            if (billType.equals(SettlementConstant.FK_BILLTYPE_SXFHKF) || billType.equals(SettlementConstant.FK_BILLTYPE_SXFDF) || billType.equals(SettlementConstant.FK_BILLTYPE_SXFYTJK)){
                fieldValue = rylCountSettlementBillTempCfgEntityList.stream().filter(x -> 1 == x.getStatus() && "PAYMENT".equals(x.getBillArea()) && "PAY_ACCOUNT".equals(x.getBillField())).findAny().orElse(null);
                settBillToFkPaymentVo.setPayAccount(fieldValue.getBillFieldValue());
            }else {
                if (settBouncers!=null) {
                    if (settBouncers.equals("1")) {
                        settBillToFkPaymentVo.setPayAccount("SBA20230210095504AD075DF500694B279A0FC29383AFB719");
                    } else {
                        settBillToFkPaymentVo.setPayAccount("SBA20230210155528027801FFA22A4140A51DDDA5DBE5BB9F");
                    }
                }
            }
            settBillToFkPaymentVo.setOriginalCurrencySum(settBillToFkExpenseVo.getOriginalCurrencySum());
            fieldValue = rylCountSettlementBillTempCfgEntityList.stream().filter(x -> 1 == x.getStatus() && "PAYMENT".equals(x.getBillArea()) && "CURRENCY_CODE".equals(x.getBillField())).findAny().orElse(null);
            settBillToFkPaymentVo.setCurrencyCode(fieldValue.getBillFieldValue());
            settBillToFkPaymentVoList.add(settBillToFkPaymentVo);
            settBillToFkVoNew.setPayment(settBillToFkPaymentVoList);
        }
        settBillToFkDetailVoList.add(settBillToFkDetailVo);
        settBillToFkVoNew.setDetail(settBillToFkDetailVoList);
        if (channelType.equals("3") && chargeTotal.compareTo(total)!=0){
            log.error("根据账号信息匹配保单手续费汇总，分摊后的手续费金额再次汇总后与结算单手续费总金额不相等，结算单手续费总金额：{}，分摊汇总手续费总金额：{}",amount,total.toString());
            return null;
        }
        //附件区
        List<SettBillToFkFileVo> settBillToFkFileVoList = new ArrayList<>();
        if (!billType.equals(SettlementConstant.FK_BILLTYPE_FWFYTBXF)){
            //结算单
            RylCountSettlementCountLogEntity rylCountSettlementCountLogEntity = new RylCountSettlementCountLogEntity();
            rylCountSettlementCountLogEntity.setCountNo(billPay.getCountNo());
            List<RylCountSettlementCountLogEntity> rylCountSettlementCountLogList = rylCountSettlementCountLogMapper.selectCountUrl(rylCountSettlementCountLogEntity);
            if (!rylCountSettlementCountLogList.isEmpty()){
                RylCountSettlementCountLogEntity rylCountSettlementCountLog = rylCountSettlementCountLogList.get(rylCountSettlementCountLogList.size() - 1);
                if (StringUtils.isNotEmpty(rylCountSettlementCountLog.getUrl())){
                    SettBillToFkFileVo settBillToFkFileVo = new SettBillToFkFileVo();
                    settBillToFkFileVo.setName(rylCountSettlementCountLog.getFileName());
                    settBillToFkFileVo.setFileSize("0");
                    settBillToFkFileVo.setFileCode(FileUploadAddrConstant.PROTOCOL_ADDR.concat(rylCountSettlementCountLog.getUrl()));
                    settBillToFkFileVoList.add(settBillToFkFileVo);
                }
            }
        }
        List<String> configLogIdList = new ArrayList<>();
        List<String> protocolTypes = protocolTypeList.get(billType);
        if (protocolTypes.contains("0")){
            //主协议
            RylCountSettlementProtocolEntity rylCountSettlementProtocolEntity = rylCountSettlementProtocolMapper.selectOne(new QueryWrapper<RylCountSettlementProtocolEntity>()
                    .eq("channel_code", billPay.getChannelCode())
                    .eq("protocol_type", "0")
                    .le("effective_date", DateUtil.changeDate2Str(new Date(),"yyyy-MM-dd"))
                    .ge("invalid_date", DateUtil.changeDate2Str(new Date(),"yyyy-MM-dd"))
                    .eq("protocol_state", "1")
                    .orderByDesc("create_time").last(" limit 1"));
            if (rylCountSettlementProtocolEntity!=null){
                configLogIdList.add(rylCountSettlementProtocolEntity.getConfiglogId());
            }
        }
        //产品协议
        List<String> configLogIds = rylCountSettlementRateMapper.queryConfiglogId(billPay.getCountNo(),protocolTypes);
        if (!CollectionUtils.isEmpty(configLogIds)){
            configLogIdList.addAll(configLogIds);
        }
        if (!CollectionUtils.isEmpty(configLogIdList)){
            List<String> collect = configLogIdList.stream().distinct().collect(Collectors.toList());
            List<RylCountSettlementFileConfigLogEntity> rylCountSettlementFileConfigLogEntityList = rylCountSettlementFileConfigLogMapper
                    .selectList(new QueryWrapper<RylCountSettlementFileConfigLogEntity>()
                            .in("configlog_id", collect));
            for (RylCountSettlementFileConfigLogEntity configLogEntity : rylCountSettlementFileConfigLogEntityList) {
                SettBillToFkFileVo settBillToFkFileVo = new SettBillToFkFileVo();
                settBillToFkFileVo.setName(configLogEntity.getFileName());
                settBillToFkFileVo.setFileSize("0");
                settBillToFkFileVo.setFileCode(FileUploadAddrConstant.RATE_GRADE_COMMON_RULE.concat(configLogEntity.getUrl()));
                settBillToFkFileVoList.add(settBillToFkFileVo);
            }
        }
        //盖章结算单及发票
        List<RylCountSettlementFileConfigLogEntity> rylCountSettlementFileConfigLogEntityList = rylCountSettlementFileConfigLogMapper
                .selectList(new QueryWrapper<RylCountSettlementFileConfigLogEntity>()
                        .eq("configlog_id", "RYL-INVOICE-" + billPay.getCountNo()));
        if (!CollectionUtils.isEmpty(rylCountSettlementFileConfigLogEntityList)){
            for (RylCountSettlementFileConfigLogEntity rylCountSettlementFileConfigLogEntity : rylCountSettlementFileConfigLogEntityList) {
                SettBillToFkFileVo settBillToFkFileVo = new SettBillToFkFileVo();
                settBillToFkFileVo.setName(rylCountSettlementFileConfigLogEntity.getFileName());
                settBillToFkFileVo.setFileSize("0");
                String key = rylCountSettlementFileConfigLogEntity.getUrl().substring(rylCountSettlementFileConfigLogEntity.getUrl().lastIndexOf("/")+1);
                settBillToFkFileVo.setFileCode(FileUploadAddrConstant.RYL_FILE_ADDR.concat(key));
                settBillToFkFileVoList.add(settBillToFkFileVo);
            }
        }
        settBillToFkVoNew.setFile(settBillToFkFileVoList);
        settBillToFkVoNewList.add(settBillToFkVoNew);
        postFkDataNew.setDataList(settBillToFkVoNewList);
        postFkDataNew.setDatacount(1);
        return new ObjectMapper().writeValueAsString(postFkDataNew);
    }
    /**
     * 约款邮件发送
     * @return
     */
    private void paymentOfContractSendMail(RylCountSettlementBillPayEntity billPayEntity,String settBouncers){
        String amount = StringUtils.isBlank(billPayEntity.getCommissionChargeTotalAfter()) ? billPayEntity.getCommissionChargeTotal() : billPayEntity.getCommissionChargeTotalAfter();
        if (billPayEntity!=null&&Double.valueOf(amount)>=2000000){
            String countDatePeriod = SettlementUtil.dealSettDate(billPayEntity.getCountStartTime(), billPayEntity.getCountEndTime());
            SendMailYkDto sendMailYkDto = new SendMailYkDto();
            if (StringUtils.isEmpty(billPayEntity.getChannelName())){
                sendMailYkDto.setChannelName("");
            }else {
                sendMailYkDto.setChannelName(billPayEntity.getChannelName());
            }
            sendMailYkDto.setCommissionChargeTotal(amount);
            sendMailYkDto.setCountDate(countDatePeriod);
            sendMailYkDto.setFkBillNo(billPayEntity.getFkBillNo());
            if (billPayEntity.getIsPayBehalf()!=null&&billPayEntity.getIsPayBehalf()==1){
                sendMailYkDto.setSettleBouncersName("融汇");
            }else {
                if (StringUtils.isEmpty(settBouncers)){
                    sendMailYkDto.setSettleBouncersName("");
                }else {
                    if (settBouncers.equals("1")) {
                        sendMailYkDto.setSettleBouncersName("弘康");
                    } else {
                        sendMailYkDto.setSettleBouncersName("融汇");
                    }
                }
            }
            if (StringUtils.isNotEmpty(billPayEntity.getManageCom())){
                String[] split = StringUtil.split(billPayEntity.getManageCom(), "|");
                if (split.length==1){
                    if (split[0].startsWith("8611")){
                        sendMailYkDto.setManageCom("北京");
                    }else if (split[0].startsWith("8631")){
                        sendMailYkDto.setManageCom("上海");
                    }else if (split[0].startsWith("8641")){
                        sendMailYkDto.setManageCom("河南");
                    }else if (split[0].startsWith("8632")){
                        sendMailYkDto.setManageCom("江苏");
                    }
                }
            }
            Date b = WorkDayUtils.getBeforeWorkDate(new Date(), 4, "B");
            log.info("邮件发送时间为：{}",b);
            if (b==null){
                log.info("未获取到邮件发送时间，不发送邮件");
                return;
            }
            sendMailYkDto.setPaymentDate(DateUtil.changeDate2Str(b,"yyyy年MM月dd号"));
            RylCountSettlementCountLogEntity rylCountSettlementCountLogEntity = new RylCountSettlementCountLogEntity();
            rylCountSettlementCountLogEntity.setCountNo(billPayEntity.getCountNo());
            List<RylCountSettlementCountLogEntity> rylCountSettlementCountLogList = rylCountSettlementCountLogMapper.selectCountUrl(rylCountSettlementCountLogEntity);
            if (!rylCountSettlementCountLogList.isEmpty()){
                RylCountSettlementCountLogEntity rylCountSettlementCountLog = rylCountSettlementCountLogList.get(rylCountSettlementCountLogList.size() - 1);
                if (StringUtils.isNotEmpty(rylCountSettlementCountLog.getUrl())){
                    String url = FileUploadAddrConstant.PROTOCOL_ADDR.concat(rylCountSettlementCountLog.getUrl());
                    List<Map<String,String>> fileList = new ArrayList<>();
                    Map<String,String> map = new HashMap<>();
                    map.put("fileName",rylCountSettlementCountLog.getFileName());
                    map.put("fileUrl",url);
                    fileList.add(map);
                    sendMailYkDto.setFileList(fileList);
                }
            }
            log.info("约款邮件发送请求报文：{}",JSON.toJSONString(sendMailYkDto));
            ResponseEntity<String> baseResponse =
                    restTemplate.exchange(sendYkMailUrl, HttpMethod.POST, new HttpEntity<>(sendMailYkDto), new ParameterizedTypeReference<String>() {
                    });
            log.info("约款邮件发送返回报文：{}",JSON.toJSONString(baseResponse));
        }else {
            log.info("未达到500W，不发送邮件");
        }
    }

    /**
     * 查询费控支付状态
     */
    private QueryResult queryFkPaymentStatus(Integer fkUuid, List<String> billNos,
                                             RylCountSettlementBillMappingCfgEntity config) {
        QueryResult result = new QueryResult();
        // 1. 输入参数验证和过滤
        if (fkUuid == null || config == null || CollectionUtils.isEmpty(billNos)) {
            log.error("费控账号配置异常: fkUuid={}, config={}, billNos={}", fkUuid, config, billNos);
            return result;
        }
        // 2. 过滤null和空字符串
        List<String> validBillNos = billNos.stream()
                .filter(Objects::nonNull)
                .filter(billNo -> StringUtils.isNotBlank(billNo))
                .collect(Collectors.toList());

        if (validBillNos.isEmpty()) {
            log.warn("费控账号[{}]没有有效的单据号", fkUuid);
            return result;
        }
        try {
            // 分批处理，避免单次请求数据量过大
            List<List<String>> batches = Lists.partition(validBillNos, 50);

            for (List<String> batch : batches) {
                QueryResult batchResult = queryBatchPaymentStatus(config, batch);
                result.getSuccessResults().addAll(batchResult.getSuccessResults());
                // 3. 确保失败列表也不包含null
                List<String> validFailedBills = batchResult.getFailedBills().stream()
                        .filter(Objects::nonNull)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
                result.getFailedBills().addAll(validFailedBills);
            }

        } catch (Exception e) {
            log.error("查询费控账号[{}]下的费控单据的支付状态异常: {}", fkUuid, e.getMessage());
            // 4. 异常时也要过滤null值
            List<String> safeFailedBills = validBillNos.stream()
                    .filter(Objects::nonNull)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            result.getFailedBills().addAll(safeFailedBills);
        }

        return result;
    }

    /**
     * 批量查询支付状态
     */
    private QueryResult queryBatchPaymentStatus(RylCountSettlementBillMappingCfgEntity config,
                                                List<String> billNos) throws Exception {
        QueryResult result = new QueryResult();

        String fkUnitName = config.getFkUnitName();
        PostFkData postFkData = new PostFkData();
        postFkData.setBillNos(billNos);

        String requestBody = new ObjectMapper().writeValueAsString(postFkData);
        String fkBillDetailUrl = fkUnitName.startsWith(CommonConstant.FKUSERDEPT_HK) ?
                fkHkDns + fkBillDetailQueryUrl : fkrhDns + fkBillDetailQueryUrl;
        //定义标识,便于日志定位
        String requestId = String.format("FK%d-B%d-%d",
                config.getUuid(), // 费控账号ID
                billNos.size(),   // 批次大小
                System.nanoTime() % 10000); // 纳秒时间戳后4位

        String batchInfo = String.format("首单:%s", billNos.isEmpty() ? "无" : billNos.get(0));
        log.info("调用费控{} [{}|{}] 查询支付与审批节点结果请求报文：{}", fkBillDetailUrl, requestId, batchInfo, requestBody);
        HttpResponse resContent = HttpRequest.post(fkBillDetailUrl)
                .body(requestBody)
                .timeout(10000) // 设置超时时间
                .execute();
        log.info("调用费控{} [{}|{}] 查询支付与审批节点结果返回报文：{}", fkBillDetailUrl, requestId, batchInfo, resContent);

        if (200 == resContent.getStatus()) {
            String body = resContent.body();
            JSONObject response = JSONObject.parseObject(body);
            JSONArray resJsonArray = response.getJSONArray("rspData");

            if (resJsonArray != null && resJsonArray.size() > 0) {
                // 修复：处理所有返回的单据，不只是第一个
                for (int i = 0; i < resJsonArray.size(); i++) {
                    JSONObject dataObj = resJsonArray.getJSONObject(i);
                    if (dataObj != null) {
                        String payStatus = dataObj.getString("payStatus");
                        String billStatus = dataObj.getString("billStatus");
                        log.info("查询费控账号[{}]下的单据[{}]的支付状态: payStatus={}, billStatus={}",
                                config.getUuid(), dataObj.getString("billNo"), payStatus, billStatus);
                        //接口有数,支付非未付 或 审核节点非草稿 状态,则更新 单据状态
                        if (!CommonConstant.FKPAYSTATUS_NOPAY.equals(payStatus) || !FkBillStatusEnum.草稿.getState().equals(billStatus)) {
                            FkCemXgV fkCemXgV = buildFkCemXgV(dataObj);
                            result.getSuccessResults().add(fkCemXgV);
                        }
                    }
                }
            } else {
                // 接口未查到单据 eg:单据在草稿状态时 返回的示例报文: {"rspCode":"000000","rspMsg":"成功","rspData":[]}
                result.getFailedBills().addAll(billNos);
            }
        } else {
            result.getFailedBills().addAll(billNos);
        }

        return result;
    }

    /**
     * 构建FkCemXgV对象
     */
    private FkCemXgV buildFkCemXgV(JSONObject dataObj) {
        FkCemXgV fkCemXgV = new FkCemXgV();
        fkCemXgV.setBillNo(dataObj.getString("billNo"));
        //费控返回的 支付状态 是编码(同旧版)
        fkCemXgV.setPayStatus(dataObj.getString("payStatus"));
        fkCemXgV.setGetName(dataObj.getString("getName"));
        fkCemXgV.setGetAccount(dataObj.getString("getAccount"));
        fkCemXgV.setAccountingDate(dataObj.getString("accountingDate"));
        fkCemXgV.setPayMadeDate(dataObj.getString("payMadeDate"));
        fkCemXgV.setGetBankBranch(dataObj.getString("getBankBranch"));
        fkCemXgV.setOriginalCurrencySum(dataObj.getString("originalCurrencySum"));
        //费控返回的 节点审批状态是 中文,结算又转成编码落库(同旧版)
        fkCemXgV.setBillStatus(FkBillStatusEnum.queryByState(dataObj.getString("billStatus")).getStatus());
        return fkCemXgV;
    }

    /**
     * 查询结果封装类
     */
    private static class QueryResult {
        private List<FkCemXgV> successResults = new ArrayList<>();
        private List<String> failedBills = new ArrayList<>();

        // getters and setters
        public List<FkCemXgV> getSuccessResults() {
            return successResults;
        }

        public List<String> getFailedBills() {
            return failedBills;
        }
    }
}
