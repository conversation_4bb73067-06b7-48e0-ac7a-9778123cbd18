package com.huida.platform.settlement.service.reconciliationpay;

import com.xxl.job.core.biz.model.ReturnT;

/**
 * 付款 结算清单 service
 * <AUTHOR>
 */
public interface SettBillJobHandlerService {

    /**
     * 同步 审批后的结算清单数据 到费控系统
     * @param s
     * @return
     */
    ReturnT<String> syncSettBillToFk(String s);

    /**
     * 查询费控 结算单号的支付状态，更新融汇、结算中心
     *
     * @param s
     * @return
     */
    ReturnT<String> syncBillPayResult(String s);

    /**
     * 以手动结算数据为主，更新result
     *
     * @param s
     * @return
     */
    ReturnT<String> syncPaidToResult(String s) throws Exception;
    /**
     * 查询费控 上游结算单号的收款状态
     *
     * @param s
     * @return
     */
    ReturnT<String> syncZjSettBillPaymentStatus(String s);

}
