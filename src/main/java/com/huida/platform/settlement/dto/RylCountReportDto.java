package com.huida.platform.settlement.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class RylCountReportDto {


    /**
     * 部门集合
     */
    private List<String> deptCodeList;
    /**
     * 业务月起
     */
    @Pattern(regexp = "^\\d{4}-\\d{2}$", message = "开始月份格式错误，应为yyyy-MM")
    private String startMonth;
    /**
     * 业务月止
     */
    @Pattern(regexp = "^\\d{4}-\\d{2}$", message = "结束月份格式错误，应为yyyy-MM")
    private String endMonth;
    /**
     * 费用类型
     */
    private List<String> settlementTypeList;
    /**
     * 渠道结算状态
     */
    private List<String> channelSettlementStatusList;
    /**
     * 结算负责人
     */
    private List<String> settlementOperatorList;
    /**
     * 渠道编码
     */
    private String channelCode;
    /**
     * 业务日起
     */
    private String startDate;
    /**
     * 业务日止
     */
    private String endDate;

    private Long userId;
    private Integer pageNum;//页数
    private Integer pageSize;//条数
    /**
     * 商户类型 1-供应商、2-转化商、3-流量商
     */
    private List<String> merchantTypeList;
    /**
     * 渠道编码集合
     */
    private List<String> channelCodeList;
    /**
     * 险种编码集合
     */
    private List<String> riskCodeList;
    /**
     * 非弘报表范围 0-全部，,1-非保通
     */
    private Integer isBaoTong;
}
